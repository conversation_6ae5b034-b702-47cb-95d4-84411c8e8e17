import request from '@/utils/request'
import { monitorApiCall } from '@/utils/performance'

// 客户基本信息管理
export function getClientList(params = {}) {
  // 如果没有指定排序，默认按最近修改时间排序
  if (!params.ordering && !params.sort) {
    params.ordering = '-updated_at'
  }
  
  return request({
    url: '/api/clients/',
    method: 'get',
    params
  })
}

export const getClientById = monitorApiCall('getClientById', (id) => {
  return request({
    url: `/api/clients/${id}/`,
    method: 'get'
  })
})

export function addClient(data) {
  return request({
    url: '/api/clients/',
    method: 'post',
    data
  })
}

export function updateClient(id, data) {
  return request({
    url: `/api/clients/${id}/`,
    method: 'put',
    data
  })
}

export function deleteClient(id) {
  return request({
    url: `/api/clients/${id}/`,
    method: 'delete'
  })
}

// 归档客户相关API
export function toggleClientArchive(id) {
  return request({
    url: `/api/clients/${id}/archive`,
    method: 'patch'
  })
}

// 教育经历相关API
export function addEducation(clientId, data) {
  return request({
    url: `/api/clients/${clientId}/education`,
    method: 'post',
    data
  })
}

export function updateEducation(clientId, educationId, data) {
  return request({
    url: `/api/clients/${clientId}/education/${educationId}`,
    method: 'put',
    data
  })
}

export function deleteEducation(clientId, educationId) {
  return request({
    url: `/api/clients/${clientId}/education/${educationId}`,
    method: 'delete'
  })
}

// 学术经历相关API
export function addAcademic(clientId, data) {
  return request({
    url: `/api/clients/${clientId}/academic`,
    method: 'post',
    data
  })
}

export function updateAcademic(clientId, academicId, data) {
  return request({
    url: `/api/clients/${clientId}/academic/${academicId}`,
    method: 'put',
    data
  })
}

export function deleteAcademic(clientId, academicId) {
  return request({
    url: `/api/clients/${clientId}/academic/${academicId}`,
    method: 'delete'
  })
}

// 工作经历相关API
export function addWork(clientId, data) {
  return request({
    url: `/api/clients/${clientId}/work`,
    method: 'post',
    data
  })
}

export function updateWork(clientId, workId, data) {
  return request({
    url: `/api/clients/${clientId}/work/${workId}`,
    method: 'put',
    data
  })
}

export function deleteWork(clientId, workId) {
  return request({
    url: `/api/clients/${clientId}/work/${workId}`,
    method: 'delete'
  })
}

// 活动经历相关API
export function addActivity(clientId, data) {
  return request({
    url: `/api/clients/${clientId}/activities`,
    method: 'post',
    data
  })
}

export function updateActivity(clientId, activityId, data) {
  return request({
    url: `/api/clients/${clientId}/activities/${activityId}`,
    method: 'put',
    data
  })
}

export function deleteActivity(clientId, activityId) {
  return request({
    url: `/api/clients/${clientId}/activities/${activityId}`,
    method: 'delete'
  })
}

// 奖项相关API
export function addAward(clientId, data) {
  return request({
    url: `/api/clients/${clientId}/awards`,
    method: 'post',
    data
  })
}

export function updateAward(clientId, awardId, data) {
  return request({
    url: `/api/clients/${clientId}/awards/${awardId}`,
    method: 'put',
    data
  })
}

export function deleteAward(clientId, awardId) {
  return request({
    url: `/api/clients/${clientId}/awards/${awardId}`,
    method: 'delete'
  })
}

// 技能相关API
export function addSkill(clientId, data) {
  return request({
    url: `/api/clients/${clientId}/skills`,
    method: 'post',
    data
  })
}

export function updateSkill(clientId, skillId, data) {
  return request({
    url: `/api/clients/${clientId}/skills/${skillId}`,
    method: 'put',
    data
  })
}

export function deleteSkill(clientId, skillId) {
  return request({
    url: `/api/clients/${clientId}/skills/${skillId}`,
    method: 'delete'
  })
}

// 语言成绩相关API
export function addLanguageScore(clientId, data) {
  return request({
    url: `/api/clients/${clientId}/language-scores`,
    method: 'post',
    data
  })
}

export function updateLanguageScore(clientId, languageScoreId, data) {
  return request({
    url: `/api/clients/${clientId}/language-scores/${languageScoreId}`,
    method: 'put',
    data
  })
}

export function deleteLanguageScore(clientId, languageScoreId) {
  return request({
    url: `/api/clients/${clientId}/language-scores/${languageScoreId}`,
    method: 'delete'
  })
}

// 文件处理相关API
export function uploadFile(file, user) {
  const formData = new FormData()
  formData.append('file', file)
  if (user) {
    formData.append('user', user)
  }

  return request({
    url: '/api/clients/files/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

export function runWorkflow(fileIds, user, extensions) {
  return request({
    url: '/api/clients/workflows/run',
    method: 'post',
    data: {
      upload_file_ids: fileIds,
      user,
      extensions
    },
    timeout: 120000  // 将超时时间设置为2分钟，给AI处理提供充足时间
  })
}

// 客户文件相关API
export function getClientFiles(id) {
  return request({
    url: `/api/clients/${id}/files`,
    method: 'get'
  })
}

export function uploadClientFile(id, data) {
  return request({
    url: `/api/clients/${id}/files`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

export function deleteClientFile(clientId, fileId) {
  return request({
    url: `/api/clients/${clientId}/files/${fileId}`,
    method: 'delete'
  })
}

// 客户笔记相关API
export function getClientNotes(id) {
  return request({
    url: `/api/clients/${id}/notes`,
    method: 'get'
  })
}

export function addClientNote(id, data) {
  return request({
    url: `/api/clients/${id}/notes`,
    method: 'post',
    data
  })
}

export function updateClientNote(clientId, noteId, data) {
  return request({
    url: `/api/clients/${clientId}/notes/${noteId}`,
    method: 'put',
    data
  })
}

export function deleteClientNote(clientId, noteId) {
  return request({
    url: `/api/clients/${clientId}/notes/${noteId}`,
    method: 'delete'
  })
}

export function searchClients(keyword) {
  return request({
    url: '/api/clients/',
    method: 'get',
    params: { 
      search: keyword,
      limit: 20,  // 限制搜索结果数量
      is_archived: false,  // 只搜索未归档的客户
      ordering: '-updated_at'  // 按最近修改时间倒序排列
    }
  })
}

// 个人想法相关API
export function addThought(clientId, data) {
  return request({
    url: `/api/clients/${clientId}/thoughts`,
    method: 'post',
    data
  })
}

export function updateThought(clientId, data) {
  return request({
    url: `/api/clients/${clientId}/thoughts`,
    method: 'put',
    data
  })
}

export function deleteThought(clientId) {
  return request({
    url: `/api/clients/${clientId}/thoughts`,
    method: 'delete'
  })
}

// 自定义模块相关API
export function addCustomModule(clientId, data) {
  return request({
    url: `/api/clients/${clientId}/custom-modules`,
    method: 'post',
    data
  })
}

export function updateCustomModule(clientId, moduleId, data) {
  return request({
    url: `/api/clients/${clientId}/custom-modules/${moduleId}`,
    method: 'put',
    data
  })
}

export function emptyCustomModule(clientId, moduleId) {
  return request({
    url: `/api/clients/${clientId}/custom-modules/${moduleId}`,
    method: 'put',
    data: { content: '' } // 清空内容而不是删除模块
  })
}

export function deleteCustomModule(clientId, moduleId) {
  return request({
    url: `/api/clients/${clientId}/custom-modules/${moduleId}`,
    method: 'delete'
  })
}

// 修改为:

// 背景自定义模块相关API
export function addBackgroundModule(clientId, data) {
  return request({
    url: `/api/clients/${clientId}/background-modules`,
    method: 'post',
    data
  })
}

export function updateBackgroundModule(clientId, moduleId, data) {
  return request({
    url: `/api/clients/${clientId}/background-modules/${moduleId}`,
    method: 'put',
    data
  })
}

export function deleteBackgroundModule(clientId, moduleId) {
  return request({
    url: `/api/clients/${clientId}/background-modules/${moduleId}`,
    method: 'delete'
  })
}

// 想法自定义模块相关API
export function addThoughtModule(clientId, data) {
  return request({
    url: `/api/clients/${clientId}/thought-modules`,
    method: 'post',
    data
  })
}

export function updateThoughtModule(clientId, moduleId, data) {
  return request({
    url: `/api/clients/${clientId}/thought-modules/${moduleId}`,
    method: 'put',
    data
  })
}

export function deleteThoughtModule(clientId, moduleId) {
  return request({
    url: `/api/clients/${clientId}/thought-modules/${moduleId}`,
    method: 'delete'
  })
}

// 智能背景提取API
export function extractBackgroundFromFile(file) {
  const formData = new FormData();
  formData.append('file', file);
  
  return request({
    url: '/api/background-extraction/extraction/extract-from-file',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 120000 // 设置2分钟超时，因为AI处理需要时间
  });
}

// 定校书相关API
export const getClientPrograms = monitorApiCall('getClientPrograms', (clientId) => {
  return request({
    url: `/api/clients/${clientId}/programs/`,
    method: 'get'
  })
})

export function addClientProgram(clientId, data) {
  return request({
    url: `/api/clients/${clientId}/programs`,
    method: 'post',
    data
  })
}

export function removeClientProgram(clientId, programId) {
  return request({
    url: `/api/clients/${clientId}/programs/${programId}`,
    method: 'delete'
  })
}

// 智能增强：从文件创建经历
export function augmentExperienceFromFile(clientId, experienceType, file) {
  const formData = new FormData();
  formData.append('file', file);

  return request({
    url: `/api/ai-augmentation/augment/${clientId}/${experienceType}`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 获取客户文书列表（CV和RL）
export const getClientDocuments = async (clientId) => {
  const response = await request.get(`/api/clients/${clientId}/documents`);
  return response;
};