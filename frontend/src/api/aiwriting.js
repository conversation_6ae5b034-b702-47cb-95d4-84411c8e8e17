import request from '@/utils/request'

/**
 * AI写作 - CV相关API
 */

// 获取客户档案列表
export const getClientProfiles = () => {
  return request({
    url: '/api/ai-writing/cv/clients',
    method: 'get'
  })
}

// 获取客户详细模块数据
export const getClientModules = (clientId) => {
  return request({
    url: `/api/ai-writing/cv/clients/${clientId}/modules`,
    method: 'get'
  })
}

// 流式生成CV
export const generateCVStream = (data) => {
  return fetch('/api/ai-writing/cv/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
  })
}

// 保存CV内容
export const saveCVContent = (data) => {
  return request({
    url: '/api/ai-writing/cv/save',
    method: 'post',
    data
  })
}

/**
 * AI写作 - PS相关API
 */

// PS相关API将在未来添加
// export const generatePSStream = (data) => { ... }
// export const savePSContent = (data) => { ... } 

/**
 * AI写作 - RL相关API
 */

/**
 * 解析推荐信模板文件 (.docx)
 * @param {FormData} formData 包含文件的表单数据
 * @returns {Promise<any>}
 */
export function parseRLTemplate(data) {
  return request({
    url: '/api/ai-writing/rl/parse',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 流式生成推荐信
 * @param {object} data 请求体
 * @returns {Promise<Response>} 返回原始的 Fetch API Response 对象用于流式读取
 */
export function generateRLStream(data) {
  const token = localStorage.getItem('token'); // 假设token存储在localStorage
  
  return fetch(import.meta.env.VITE_APP_BASE_API + '/api/ai-writing/rl/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(data)
  });
}

export function saveRL(data) {
  return request({
    url: '/api/ai-writing/rl/save',
    method: 'post',
    data
  });
} 