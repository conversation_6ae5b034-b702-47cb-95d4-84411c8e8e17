<template>
  <div class="h-screen bg-white flex overflow-hidden">
    <!-- 左侧表单区域 -->
    <div class="w-96 flex-shrink-0 bg-white border-r border-gray-200 flex flex-col h-full">
      <div class="flex-1 overflow-y-auto p-6">
        <div class="space-y-6">
          <!-- 客户档案 -->
          <div>
                          <div class="flex items-center space-x-2 mb-3">
                <span class="material-icons-outlined text-[#4F46E5] text-lg">person</span>
                <span class="text-sm font-medium text-gray-700 flex items-center">
                  <span class="text-red-500 mr-1">*</span>客户档案
                </span>
                <button 
                  type="button"
                  @click="navigateToClientList"
                  class="text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium ml-auto flex items-center"
                >
                  + 新建档案
                </button>
              </div>

            <!-- 已选择客户信息显示 -->
            <div v-if="selectedClient" class="mb-3">
              <div class="flex items-center p-3 bg-[#4F46E5]/5 rounded-lg border border-[#4F46E5]/20">
                <div class="w-8 h-8 rounded-full bg-[#4F46E5] text-white flex items-center justify-center text-xs font-medium mr-3">
                  {{ selectedClient.name?.charAt(0)?.toUpperCase() || '?' }}
                </div>
                <div class="flex-1">
                                      <div class="font-medium text-gray-800 text-sm">{{ selectedClient.name }}</div>
                    <div class="text-xs text-gray-500">
                      {{ selectedClient.phone || '暂无联系方式' }}
                      {{ selectedClient.location ? ' · ' + selectedClient.location : '' }}
                    </div>
                </div>
                <button 
                  @click="clearSelectedClient"
                  class="text-gray-500 hover:text-gray-700 p-1 rounded-md hover:bg-gray-100 transition-colors"
                >
                  <span class="material-icons-outlined text-sm">clear</span>
                </button>
              </div>
            </div>

            <!-- 客户选择搜索框 -->
            <div v-else class="relative client-selector">
              <AnimatedInput
                v-model="clientSearchQuery"
                label="搜索客户"
                placeholder="输入客户姓名、联系方式或学生ID"
                type="input"
                @input="handleClientSearch"
                @focus="handleClientSearchFocus"
              />

              <!-- 搜索结果下拉框 -->
              <Teleport to="body">
                <div 
                  v-if="showClientSelector && (clientSearchResults.length > 0 || clientSearchLoading)" 
                  ref="clientDropdown"
                  class="fixed z-[9999] bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto client-dropdown"
                  :style="dropdownStyle"
                >
                  <!-- 搜索结果 -->
                  <div v-if="clientSearchResults.length > 0" class="p-1">
                    <div 
                      v-for="client in clientSearchResults" 
                      :key="client.id_hashed"
                      @click="selectClient(client)"
                      class="flex items-center p-2 hover:bg-gray-50 rounded-md cursor-pointer transition-colors"
                    >
                      <div class="w-6 h-6 rounded-full bg-[#4F46E5]/10 text-[#4F46E5] flex items-center justify-center text-xs font-medium mr-2">
                        {{ client.name?.charAt(0)?.toUpperCase() || '?' }}
                      </div>
                                              <div class="flex-1">
                          <div class="font-medium text-gray-800 text-xs">{{ client.name }}</div>
                          <div class="text-xs text-gray-500">
                            {{ client.phone || '暂无联系方式' }}
                            {{ client.location ? ' · ' + client.location : '' }}
                          </div>
                        </div>
                    </div>
                  </div>

                  <!-- 无搜索结果 -->
                  <div v-else-if="clientSearchQuery && !clientSearchLoading && clientSearchResults.length === 0" class="p-3 text-center text-gray-500 text-xs">
                    未找到匹配的客户
                  </div>
                  
                  <!-- 默认提示（首次点击且没有搜索内容时） -->
                  <div v-else-if="!clientSearchQuery && !clientSearchLoading && clientSearchResults.length === 0" class="p-3 text-center text-gray-500 text-xs">
                    输入客户姓名、联系方式或学生ID进行搜索
                  </div>

                  <!-- 加载状态 -->
                  <div v-else-if="clientSearchLoading" class="p-3 text-center text-gray-500 text-xs">
                    <span class="material-icons-outlined animate-spin mr-1 text-sm">refresh</span>
                    搜索中...
                  </div>
                </div>
              </Teleport>
            </div>
          </div>

          <!-- 选择经历 -->
          <div v-if="selectedClient && clientModuleData">
            <label class="form-label text-red-500">
              <span class="text-red-500 mr-1">*</span>选择经历
              <span class="text-sm text-[#4F46E5] font-medium">(已选 {{ totalSelectedCount }} 项)</span>
            </label>
            
            <div class="space-y-4 max-h-80 overflow-y-auto border border-gray-200 rounded-lg p-4 bg-gray-50"
                 v-if="Object.keys(clientModuleData).some(key => clientModuleData[key]?.length > 0)">
              
              <!-- 如果没有任何经历数据时的提示 -->
              <div v-if="!Object.keys(clientModuleData).some(key => clientModuleData[key]?.length > 0)" 
                   class="text-center text-gray-500 py-8">
                该客户暂无可选择的经历数据
              </div>
              <!-- 教育经历 -->
              <div v-if="clientModuleData.education?.length > 0" class="experience-section">
                <h3 class="section-title">教育经历</h3>
                <div class="section-items">
                  <label v-for="item in clientModuleData.education" :key="item.id" class="experience-item">
                    <el-checkbox 
                      :model-value="formData.selectedEducationIds.includes(item.id)" 
                      @change="() => toggleSelection('selectedEducationIds', item.id)" 
                      size="small"
                    />
                    <span class="item-content">{{ item.school }} - {{ item.major || '专业未填' }}</span>
                  </label>
                </div>
              </div>

              <!-- 学术经历 -->
              <div v-if="clientModuleData.academic?.length > 0" class="experience-section">
                <h3 class="section-title">学术经历</h3>
                <div class="section-items">
                  <label v-for="item in clientModuleData.academic" :key="item.id" class="experience-item">
                    <el-checkbox 
                      :model-value="formData.selectedAcademicIds.includes(item.id)" 
                      @change="() => toggleSelection('selectedAcademicIds', item.id)" 
                      size="small"
                    />
                    <span class="item-content">{{ item.title || '学术项目' }}</span>
                  </label>
                </div>
              </div>

              <!-- 工作经历 -->
              <div v-if="clientModuleData.work?.length > 0" class="experience-section">
                <h3 class="section-title">工作经历</h3>
                <div class="section-items">
                  <label v-for="item in clientModuleData.work" :key="item.id" class="experience-item">
                    <el-checkbox 
                      :model-value="formData.selectedWorkIds.includes(item.id)" 
                      @change="() => toggleSelection('selectedWorkIds', item.id)" 
                      size="small"
                    />
                    <span class="item-content">{{ item.company || '公司' }} - {{ item.position || '职位' }}</span>
                  </label>
                </div>
              </div>

              <!-- 课外活动 -->
              <div v-if="clientModuleData.activities?.length > 0" class="experience-section">
                <h3 class="section-title">课外活动</h3>
                <div class="section-items">
                  <label v-for="item in clientModuleData.activities" :key="item.id" class="experience-item">
                    <el-checkbox 
                      :model-value="formData.selectedActivityIds.includes(item.id)" 
                      @change="() => toggleSelection('selectedActivityIds', item.id)" 
                      size="small"
                    />
                    <span class="item-content">{{ item.name || '课外活动' }}</span>
                  </label>
                </div>
              </div>

              <!-- 荣誉奖项 -->
              <div v-if="clientModuleData.awards?.length > 0" class="experience-section">
                <h3 class="section-title">荣誉奖项</h3>
                <div class="section-items">
                  <label v-for="item in clientModuleData.awards" :key="item.id" class="experience-item">
                    <el-checkbox 
                      :model-value="formData.selectedAwardIds.includes(item.id)" 
                      @change="() => toggleSelection('selectedAwardIds', item.id)" 
                      size="small"
                    />
                    <span class="item-content">{{ item.name || '奖项' }}</span>
                  </label>
                </div>
              </div>

              <!-- 技能特长 -->
              <div v-if="clientModuleData.skills?.length > 0" class="experience-section">
                <h3 class="section-title">技能特长</h3>
                <div class="section-items">
                  <label v-for="item in clientModuleData.skills" :key="item.id" class="experience-item">
                    <el-checkbox 
                      :model-value="formData.selectedSkillIds.includes(item.id)" 
                      @change="() => toggleSelection('selectedSkillIds', item.id)" 
                      size="small"
                    />
                    <span class="item-content">{{ item.type }}: {{ item.description }}</span>
                  </label>
                </div>
              </div>

              <!-- 语言成绩 -->
              <div v-if="clientModuleData.language_scores?.length > 0" class="experience-section">
                <h3 class="section-title">语言成绩</h3>
                <div class="section-items">
                  <label v-for="item in clientModuleData.language_scores" :key="item.id" class="experience-item">
                    <el-checkbox 
                      :model-value="formData.selectedLanguageScoreIds.includes(item.id)" 
                      @change="() => toggleSelection('selectedLanguageScoreIds', item.id)" 
                      size="small"
                    />
                    <span class="item-content">{{ item.type?.toUpperCase() }} - {{ item.score }}</span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- 简历语言 -->
          <div>
            <label class="form-label">简历语言</label>
            <div class="flex space-x-3">
              <button 
                @click="formData.language = 'english'"
                :class="[
                  'flex-1 py-2.5 px-4 rounded-lg text-sm font-medium transition-colors duration-200',
                  formData.language === 'english' 
                    ? 'bg-[#4F46E5] text-white shadow-sm' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-200'
                ]"
              >
                英文
              </button>
              <button 
                @click="formData.language = 'chinese'"
                :class="[
                  'flex-1 py-2.5 px-4 rounded-lg text-sm font-medium transition-colors duration-200',
                  formData.language === 'chinese' 
                    ? 'bg-[#4F46E5] text-white shadow-sm' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-200'
                ]"
              >
                中文
              </button>
            </div>
          </div>

          <!-- 额外信息 -->
          <div>
            <label class="form-label">
              额外信息
              <button 
                type="button"
                class="ml-2 text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium"
              >
                查看范例
              </button>
            </label>
            <el-input
              v-model="formData.additionalInfo"
              type="textarea"
              :rows="7"
              placeholder="请输入补充信息..."
              class="form-textarea"
            ></el-input>
          </div>

          <!-- 版本信息 -->
          <!-- <div class="space-y-4">
            <!-- 版本名称 -->
            <!-- <div>
              <label class="form-label">版本名称</label>
              <el-input
                v-model="formData.versionName"
                placeholder="例如：申请MIT版本、初稿版本等..."
                class="form-input"
              ></el-input>
            </div> -->

            <!-- 目标专业 -->
            <!-- <div>
              <label class="form-label">目标专业</label>
              <el-input
                v-model="formData.targetMajor"
                placeholder="请输入目标专业"
                class="form-input"
              ></el-input>
            </div>
          </div> -->

          <!-- 底部按钮区域 -->
          <div class="pt-6 border-t border-gray-100 mt-6">
            <!-- 生成按钮 -->
            <button 
              v-if="!isGenerating"
              @click="handleGenerateCV" 
              :disabled="!isFormValid"
              :class="[
                'w-full py-3 px-4 rounded-lg font-medium text-white transition-all duration-300 flex items-center justify-center relative overflow-hidden',
                isFormValid
                  ? 'bg-[#4F46E5] hover:bg-[#4338CA] shadow-sm cursor-pointer hover:shadow-lg'
                  : 'bg-gray-400 cursor-not-allowed'
              ]"
            >
              <span class="flex items-center">
                <span class="material-icons-outlined mr-2 text-lg">auto_fix_high</span>
                开始智能生成
              </span>
            </button>
            
            <!-- 生成中状态和取消按钮 -->
            <div v-else class="space-y-3">
              <!-- 生成状态显示 -->
              <div class="w-full py-3 px-4 rounded-lg bg-[#4F46E5] text-white font-medium flex items-center justify-center relative overflow-hidden">
                <span class="flex items-center">
                  <span class="material-icons-outlined animate-spin mr-2 text-lg">auto_fix_high</span>
                  <span class="typing-text">AI正在智能生成中</span>
                  <span class="dots">
                    <span class="dot">.</span>
                    <span class="dot">.</span>
                    <span class="dot">.</span>
                  </span>
                </span>
                <!-- 生成中的进度条效果 -->
                <div class="absolute bottom-0 left-0 h-1 bg-white/30 rounded-full progress-bar"></div>
              </div>
              
              <!-- 取消按钮 -->
              <button 
                @click="handleCancelGeneration"
                class="w-full py-2 px-4 rounded-lg border border-gray-300 text-gray-600 hover:text-gray-800 hover:border-gray-400 transition-colors duration-200 flex items-center justify-center"
              >
                <span class="material-icons-outlined mr-2 text-sm">stop</span>
                取消生成
              </button>
            </div>
            
            <!-- 温馨提示 -->
            <div class="mt-3 text-xs text-gray-500 text-center">
              <span class="material-icons-outlined text-xs align-middle mr-1">info</span>
              <span v-if="!isGenerating">AI将根据您选择的经历生成个性化简历</span>
              <span v-else>正在使用先进AI技术为您量身定制简历内容...</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧编辑器区域 -->
    <div class="flex-1 bg-white h-full overflow-hidden">
      <TiptapEditor
        ref="cvEditorRef"
        v-model="cvContent"
        document-type="cv"
        placeholder="选择客户档案并点击左侧'开始智能生成'获取AI智能简历，生成后可以编辑并保存..."
        @save="handleSave"
        @export="handleExport"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter, useRoute, onBeforeRouteLeave } from 'vue-router'
import TiptapEditor from '@/components/writing/TiptapEditor.vue'
import AnimatedInput from '@/components/common/AnimatedInput.vue'
import { Packer } from 'docx'
import html2pdf from 'html2pdf.js'
import { saveAs } from 'file-saver'
import { pdfStyles, createDocxFromHtml } from '@/utils/exportStyles.js'

const router = useRouter()
const route = useRoute()

// 客户档案相关状态
const selectedClient = ref(null)
const showClientSelector = ref(false)
const clientSearchQuery = ref('')
const clientSearchResults = ref([])
const clientSearchLoading = ref(false)
const clientModuleData = ref(null)
const clientDropdown = ref(null)
const dropdownStyle = ref({})

// 表单数据
const formData = reactive({
  language: 'english',
  additionalInfo: '',
  // versionName: '',
  // targetMajor: '',
  selectedEducationIds: [],
  selectedAcademicIds: [],
  selectedWorkIds: [],
  selectedActivityIds: [],
  selectedAwardIds: [],
  selectedSkillIds: [],
  selectedLanguageScoreIds: []
})

// CV内容
const cvContent = ref('')
const isGenerating = ref(false)
const abortController = ref(null)

// 用于PDF导出的DOM引用
const cvEditorRef = ref(null)

// 验证表单是否有效
const isFormValid = computed(() => {
  return selectedClient.value !== null
})

// 计算总选择数
const totalSelectedCount = computed(() => {
  return Object.keys(formData).filter(key => key.startsWith('selected')).reduce((sum, key) => {
    return sum + formData[key].length;
  }, 0);
});

// 导航到客户列表页面
const navigateToClientList = () => {
  router.push('/clients')
}

// 计算下拉框位置
const calculateDropdownPosition = () => {
  const inputElement = document.querySelector('.client-selector .animated-input-container')
  if (inputElement) {
    const rect = inputElement.getBoundingClientRect()
    dropdownStyle.value = {
      position: 'fixed',
      top: `${rect.bottom + 4}px`,
      left: `${rect.left}px`,
      width: `${rect.width}px`,
      zIndex: 9999
    }
  }
}

// 处理客户搜索输入框 focus 事件
const handleClientSearchFocus = async () => {
  showClientSelector.value = true
  calculateDropdownPosition()
  
  // 如果没有搜索内容，加载默认客户列表
  if (!clientSearchQuery.value || clientSearchQuery.value.trim() === '') {
    await loadDefaultClientList()
  }
}

// 加载默认客户列表 - 优化版本，添加缓存
const loadDefaultClientList = async () => {
  // 检查缓存
  const cacheKey = 'client_list_cv'
  const cached = sessionStorage.getItem(cacheKey)
  const cacheTime = sessionStorage.getItem(`${cacheKey}_time`)

  // 如果缓存存在且未过期（5分钟）
  if (cached && cacheTime && (Date.now() - parseInt(cacheTime)) < 300000) {
    try {
      const clients = JSON.parse(cached)
      console.log('🎯 使用缓存的客户列表')
      clientSearchResults.value = clients
      return
    } catch (e) {
      console.warn('缓存数据解析失败，重新获取')
    }
  }

  clientSearchLoading.value = true
  const startTime = performance.now()

  try {
    const response = await fetch('/api/ai-writing/cv/clients', {
      headers: {
        'Cache-Control': 'max-age=300' // 5分钟缓存
      }
    })
    if (!response.ok) {
      throw new Error('获取客户列表失败')
    }
    const data = await response.json()

    const loadTime = Math.round(performance.now() - startTime)
    console.log(`📊 客户列表加载完成: ${loadTime}ms`)

    let clients = data || []

    // 按最近修改时间排序
    clients.sort((a, b) => {
      const dateA = new Date(a.updated_at || a.created_at || 0)
      const dateB = new Date(b.updated_at || b.created_at || 0)
      return dateB - dateA // 倒序排列，最新的在前面
    })

    // 缓存数据
    sessionStorage.setItem(cacheKey, JSON.stringify(clients))
    sessionStorage.setItem(`${cacheKey}_time`, Date.now().toString())

    clientSearchResults.value = clients
  } catch (error) {
    console.error('加载客户列表失败:', error)
    clientSearchResults.value = []
  } finally {
    clientSearchLoading.value = false
  }
}

// 处理客户搜索
const handleClientSearch = async (query) => {
  if (!query || query.trim() === '') {
    if (showClientSelector.value) {
      await loadDefaultClientList()
    } else {
      clientSearchResults.value = []
    }
    return
  }

  clientSearchLoading.value = true
  try {
    // 注意：这里需要根据后端实际实现调整搜索逻辑
    // 当前使用全客户列表并在前端过滤
    const response = await fetch('/api/ai-writing/cv/clients')
    if (!response.ok) {
      throw new Error('搜索客户失败')
    }
    const data = await response.json()
    
    // 前端过滤搜索结果
    const searchTerm = query.trim().toLowerCase()
    let filteredResults = data.filter(client => 
      client.name?.toLowerCase().includes(searchTerm) ||
      client.phone?.includes(searchTerm) ||
      client.email?.toLowerCase().includes(searchTerm)
    )
    
    // 按最近修改时间排序搜索结果
    filteredResults.sort((a, b) => {
      const dateA = new Date(a.updated_at || a.created_at || 0)
      const dateB = new Date(b.updated_at || b.created_at || 0)
      return dateB - dateA // 倒序排列，最新的在前面
    })
    
    clientSearchResults.value = filteredResults
    console.log('搜索结果:', filteredResults)
  } catch (error) {
    console.error('搜索客户失败:', error)
    clientSearchResults.value = []
    ElMessage.error('搜索客户失败')
  } finally {
    clientSearchLoading.value = false
  }
}

// 选择客户 - 优化版本，添加加载状态和更好的错误处理
const selectClient = async (client) => {
  // 显示加载状态
  const loadingMessage = ElMessage({
    type: 'info',
    message: '正在加载客户档案...',
    duration: 0 // 不自动关闭
  })

  try {
    selectedClient.value = client
    showClientSelector.value = false
    clientSearchQuery.value = ''
    clientSearchResults.value = []

    // 重置选择状态和版本信息
    Object.keys(formData).forEach(key => {
      if(key.startsWith('selected')) {
        formData[key] = []
      }
    })

    // 获取客户详细模块数据
    await getClientModuleData(client.id_hashed)

    loadingMessage.close()
    ElMessage.success(`已选择客户：${client.name}`)
  } catch (error) {
    loadingMessage.close()
    console.error('选择客户时发生错误:', error)
    ElMessage.error('获取客户详细信息失败，请重试')
    // 重置选择状态
    selectedClient.value = null
    clientModuleData.value = null
  }
}

// 点击外部关闭下拉框
const handleClickOutside = (event) => {
  const dropdown = clientDropdown.value
  const clientSelector = document.querySelector('.client-selector')
  
  if (dropdown && clientSelector && 
      !dropdown.contains(event.target) && 
      !clientSelector.contains(event.target)) {
    showClientSelector.value = false
  }
}

// 监听窗口滚动和调整大小，更新下拉框位置
const handleWindowEvent = () => {
  if (showClientSelector.value) {
    calculateDropdownPosition()
  }
}

// 页面切换时自动取消生成
onBeforeRouteLeave((to, from, next) => {
  if (isGenerating.value) {
    handleCancelGeneration()
    ElMessage.info('检测到页面切换，已自动取消生成')
  }
  next()
})

// 组件卸载时自动取消生成
onBeforeUnmount(() => {
  if (isGenerating.value) {
    handleCancelGeneration()
  }
})

// 生命周期
onMounted(async () => {
  document.addEventListener('click', handleClickOutside)
  window.addEventListener('scroll', handleWindowEvent, true)
  window.addEventListener('resize', handleWindowEvent)
  
  // 检查是否为编辑模式
  await handleEditMode()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('scroll', handleWindowEvent, true)
  window.removeEventListener('resize', handleWindowEvent)
})

// 处理编辑模式
const handleEditMode = async () => {
  const clientId = route.query.client
  const editId = route.query.edit

  // 如果有客户ID，自动选择客户
  if (clientId) {
    try {
      // 首先获取客户信息
      const response = await fetch(`/api/ai-writing/cv/clients/${clientId}`)
      if (response.ok) {
        const client = await response.json()
        await selectClient(client)
      }
    } catch (error) {
      console.error('获取客户信息失败:', error)
    }
  }

  // 如果有编辑ID，加载CV内容
  if (editId) {
    try {
      const response = await fetch(`/api/ai-writing/cv/documents/${editId}`)
      if (response.ok) {
        const document = await response.json()
        // 加载CV内容到编辑器
        cvContent.value = document.content || ''
        // 如果有版本名称，可以设置到表单中
        if (document.version_name) {
          formData.versionName = document.version_name
        }
        ElMessage.success('CV内容已加载')
      } else {
        ElMessage.error('加载CV内容失败')
      }
    } catch (error) {
      console.error('加载CV内容失败:', error)
      ElMessage.error('加载CV内容失败')
    }
  }
}

// 清除选中的客户
const clearSelectedClient = () => {
  selectedClient.value = null
  clientModuleData.value = null
  cvContent.value = ''
  
  // 重置选择状态和版本信息
  Object.keys(formData).forEach(key => {
    if(key.startsWith('selected')) {
      formData[key] = []
    }
  })
  
  // 重置版本信息
  // formData.versionName = ''
  // formData.targetMajor = ''
}

// 获取客户模块数据 - 优化版本，添加缓存和性能监控
const getClientModuleData = async (clientId) => {
  // 检查缓存
  const cacheKey = `client_modules_${clientId}`
  const cached = sessionStorage.getItem(cacheKey)
  if (cached) {
    try {
      const data = JSON.parse(cached)
      console.log('🎯 使用缓存的客户模块数据:', clientId)
      clientModuleData.value = data
      autoSelectAllExperiences(data)
      return
    } catch (e) {
      console.warn('缓存数据解析失败，重新获取')
    }
  }

  const startTime = performance.now()
  try {
    const response = await fetch(`/api/ai-writing/cv/clients/${clientId}/modules`, {
      headers: {
        'Cache-Control': 'max-age=300' // 5分钟缓存
      }
    })
    if (!response.ok) {
      throw new Error('获取客户模块数据失败')
    }
    const data = await response.json()

    const loadTime = Math.round(performance.now() - startTime)
    console.log(`📊 客户模块数据加载完成: ${loadTime}ms`)

    // 缓存数据
    sessionStorage.setItem(cacheKey, JSON.stringify(data))

    clientModuleData.value = data

    // 默认全选所有经历
    autoSelectAllExperiences(data)
  } catch (error) {
    console.error('获取客户模块数据失败:', error)
    throw error
  }
}

// 自动选择所有经历
const autoSelectAllExperiences = (moduleData) => {
  console.log('开始自动选择所有经历...')
  
  // 教育经历
  if (moduleData.education && Array.isArray(moduleData.education)) {
    formData.selectedEducationIds = moduleData.education.map(item => item.id)
    console.log('自动选择教育经历:', formData.selectedEducationIds.length, '项')
  }
  
  // 学术经历
  if (moduleData.academic && Array.isArray(moduleData.academic)) {
    formData.selectedAcademicIds = moduleData.academic.map(item => item.id)
    console.log('自动选择学术经历:', formData.selectedAcademicIds.length, '项')
  }
  
  // 工作经历
  if (moduleData.work && Array.isArray(moduleData.work)) {
    formData.selectedWorkIds = moduleData.work.map(item => item.id)
    console.log('自动选择工作经历:', formData.selectedWorkIds.length, '项')
  }
  
  // 课外活动
  if (moduleData.activities && Array.isArray(moduleData.activities)) {
    formData.selectedActivityIds = moduleData.activities.map(item => item.id)
    console.log('自动选择课外活动:', formData.selectedActivityIds.length, '项')
  }
  
  // 荣誉奖项
  if (moduleData.awards && Array.isArray(moduleData.awards)) {
    formData.selectedAwardIds = moduleData.awards.map(item => item.id)
    console.log('自动选择荣誉奖项:', formData.selectedAwardIds.length, '项')
  }
  
  // 技能特长
  if (moduleData.skills && Array.isArray(moduleData.skills)) {
    formData.selectedSkillIds = moduleData.skills.map(item => item.id)
    console.log('自动选择技能特长:', formData.selectedSkillIds.length, '项')
  }
  
  // 语言成绩
  if (moduleData.language_scores && Array.isArray(moduleData.language_scores)) {
    formData.selectedLanguageScoreIds = moduleData.language_scores.map(item => item.id)
    console.log('自动选择语言成绩:', formData.selectedLanguageScoreIds.length, '项')
  }
  
  console.log('自动选择完成，总计选择:', totalSelectedCount.value, '项经历')
}

// 通用选择切换函数
const toggleSelection = (key, id) => {
  const selectedArray = formData[key];
  const index = selectedArray.indexOf(id);
  if (index > -1) {
    selectedArray.splice(index, 1);
  } else {
    selectedArray.push(id);
  }
}

// 已使用通用的 toggleSelection 函数替代各种特定的切换函数

// 生成CV (流式输出)
const handleGenerateCV = async () => {
  if (!isFormValid.value) {
    ElMessage.warning('请先选择客户档案')
    return
  }

  // 检查是否至少选择了一项经历
  if (totalSelectedCount.value === 0) {
    ElMessage.warning('请至少选择一项经历')
    return
  }

  isGenerating.value = true
  
  // 创建取消控制器
  abortController.value = new AbortController()
  
  try {
    ElMessage.info('🚀 开始生成CV，请稍候...')

    // 将 Proxy 响应式数组转换为普通数组，避免 JSON 序列化丢失数据
    const requestBody = {
      client_id: selectedClient.value.id_hashed,
      language: formData.language,
      additional_info: formData.additionalInfo,
      selected_education_ids: [...formData.selectedEducationIds],
      selected_academic_ids: [...formData.selectedAcademicIds],
      selected_work_ids: [...formData.selectedWorkIds],
      selected_activity_ids: [...formData.selectedActivityIds],
      selected_award_ids: [...formData.selectedAwardIds],
      selected_skill_ids: [...formData.selectedSkillIds],
      selected_language_score_ids: [...formData.selectedLanguageScoreIds]
    }

    // 调试输出，确认序列化内容
    console.group('=== CV 生成请求详细信息 ===')
    console.log('客户ID:', requestBody.client_id)
    console.log('语言:', requestBody.language)
    console.log('额外信息:', requestBody.additional_info)
    console.log('选择的教育经历IDs:', requestBody.selected_education_ids)
    console.log('选择的学术经历IDs:', requestBody.selected_academic_ids)
    console.log('选择的工作经历IDs:', requestBody.selected_work_ids)
    console.log('选择的课外活动IDs:', requestBody.selected_activity_ids)
    console.log('选择的奖项IDs:', requestBody.selected_award_ids)
    console.log('选择的技能IDs:', requestBody.selected_skill_ids)
    console.log('选择的语言成绩IDs:', requestBody.selected_language_score_ids)
    console.log('完整请求体对象:', requestBody)
    console.log('JSON 序列化结果:', JSON.stringify(requestBody))
    console.groupEnd()

    const response = await fetch('/api/ai-writing/cv/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
      signal: abortController.value.signal
    })

    if (!response.ok) {
      const errorData = await response.json()
      console.error('CV生成失败:', errorData)
      ElMessage.error(`生成失败: ${errorData.detail || '未知错误'}`)
      return
    }

    // 检查是否支持流式处理
    if (!response.body) {
      ElMessage.error('浏览器不支持流式响应')
      return
    }

    // 处理流式响应
    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let fullMarkdownContent = ''
    let lastCompleteLineIndex = 0
    
    // 初始化编辑器内容
    cvContent.value = ''
    
    // 显示流式生成状态
    ElMessage.info('✨ AI正在智能生成中...')

    try {
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) {
          break
        }

        // 解码新的数据块
        const chunk = decoder.decode(value, { stream: true })
        fullMarkdownContent += chunk
        
        // 为了更好的流式体验，我们可以按行处理
        // 检查是否有完整的行（以换行符结尾）
        const lines = fullMarkdownContent.split('\n')
        
        // 如果有新的完整行，则更新显示
        if (lines.length > lastCompleteLineIndex + 1) {
          // 取前n-1行（最后一行可能不完整）
          const completeLines = lines.slice(0, -1).join('\n')
          if (completeLines.length > 0) {
            // 直接设置 Markdown 内容，TiptapEditor 会自动转换为 HTML 显示
            cvContent.value = completeLines
            lastCompleteLineIndex = lines.length - 1
          }
        }
        
        // 流式处理的延迟，使效果更自然
        await new Promise(resolve => setTimeout(resolve, 50))
      }
      
      // 处理最后的内容（包括可能的不完整行）
      if (fullMarkdownContent.trim()) {
        // 设置最终的 Markdown 内容，TiptapEditor 会自动转换为 HTML 显示
        cvContent.value = fullMarkdownContent
      }
      
      console.log('CV流式生成完成')
      console.log('最终Markdown内容长度:', fullMarkdownContent.length)
      console.log('最终内容长度:', cvContent.value.length)
      
      if (!fullMarkdownContent.trim()) {
        ElMessage.error('生成的内容为空，请重试')
        return
      }
      
      ElMessage.success(`🎉 CV生成完成 (${formData.language})，您可以继续编辑`)
      
    } catch (streamError) {
      console.error('流式处理错误:', streamError)
      ElMessage.error('生成过程中出现错误，请重试')
    } finally {
      reader.releaseLock()
    }
    
  } catch (error) {
    if (error.name === 'AbortError') {
      console.log('CV生成已被用户取消')
      ElMessage.warning('生成已取消')
    } else {
      console.error('CV生成请求失败:', error)
      ElMessage.error('生成失败，请重试')
    }
  } finally {
    isGenerating.value = false
    abortController.value = null
  }
}

// 取消生成
const handleCancelGeneration = () => {
  if (abortController.value) {
    abortController.value.abort()
    ElMessage.info('正在取消生成...')
  }
}

// 保存处理
const handleSave = async (data) => {
  console.log('Saving CV:', data)
  
  if (!selectedClient.value) {
    ElMessage.warning('请先选择客户档案')
    return
  }

  if (!data.content || !data.content.trim()) {
    ElMessage.warning('CV内容不能为空')
    return
  }

  // 如果没有输入版本名称，自动生成一个
  // let versionName = formData.versionName.trim()
  // if (!versionName) {
    const now = new Date()
    const versionName = `版本 ${now.getFullYear()}/${String(now.getMonth() + 1).padStart(2, '0')}/${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`
    // formData.versionName = versionName
  // }

  try {
    ElMessage.info('正在保存CV...')

    const requestBody = {
      client_id: selectedClient.value.id_hashed,
      content_markdown: data.content,
      version_name: versionName,
      // target_major: formData.targetMajor.trim() || undefined
    }

    console.log('保存CV请求:', requestBody)

    const response = await fetch('/api/ai-writing/cv/save', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      const errorData = await response.json()
      console.error('CV保存失败:', errorData)
      ElMessage.error(`保存失败: ${errorData.detail || '未知错误'}`)
      return
    }

    const result = await response.json()
    console.log('CV保存成功:', result)
    
    ElMessage.success(`✅ CV已成功保存 - ${result.client_name} (${versionName})`)
    
    // 保存成功后，清空版本名称以便下次保存时自动生成新的版本名
    // formData.versionName = ''

  } catch (error) {
    console.error('CV保存请求失败:', error)
    ElMessage.error('保存失败，请检查网络连接后重试')
  }
}

// 导出处理
const handleExport = async (data) => {
  const { content, textContent, format } = data;
  const clientName = selectedClient.value?.name || 'CV';
  const dateStr = new Date().toLocaleDateString('sv');
  const fileName = `${clientName}_CV_${dateStr}`;

  if (!content) {
    ElMessage.warning('没有可导出的内容');
    return;
  }

  try {
    if (format === 'pdf') {
      ElMessage.info('正在生成PDF文件...');

      const editorEl = cvEditorRef.value?.$el;
      if (!editorEl) {
        ElMessage.error('无法找到编辑器元素，导出失败');
        return;
      }
      
      // 为打印目标及其父级添加标记，以便全局CSS识别
      const parentEl = editorEl.parentElement;
      const grandParentEl = parentEl?.parentElement;
      
      editorEl.classList.add('printable-content');
      parentEl?.classList.add('printable-content-parent');
      grandParentEl?.classList.add('printable-content-grandparent');
      
      // 启用打印模式
      document.body.classList.add('is-printing');

      const opt = {
        margin: [15, 15, 15, 15],
        filename: `${fileName}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: {
          scale: 2,
          useCORS: true,
          letterRendering: true,
        },
        jsPDF: {
          unit: 'mm',
          format: 'a4',
          orientation: 'portrait'
        },
        pagebreak: { mode: ['css', 'legacy'] }
      };
      
      try {
        await html2pdf().set(opt).from(editorEl).save();
        ElMessage.success(`PDF文件 ${fileName}.pdf 已下载完成`);
      } finally {
        // 关键：无论成功或失败，都恢复页面原始样式
        document.body.classList.remove('is-printing');
        editorEl.classList.remove('printable-content');
        parentEl?.classList.remove('printable-content-parent');
        grandParentEl?.classList.remove('printable-content-grandparent');
      }

    } else if (format === 'txt') {
      const finalFileName = `${fileName}.txt`;
      const finalTextContent = textContent || content.replace(/<[^>]*>/g, '').replace(/\\n\\s*\\n/g, '\\n\\n').trim();
      const blob = new Blob([finalTextContent], { type: 'text/plain;charset=utf-t' });
      saveAs(blob, finalFileName);
      ElMessage.success(`TXT文件 ${finalFileName} 已下载完成`);
      
    } else if (format === 'docx') {
      ElMessage.info('正在生成DOCX文件...');
      
      // 使用新的、更强大的HTML到DOCX转换器
      const doc = createDocxFromHtml(content);
      
      const buffer = await Packer.toBlob(doc);
      saveAs(buffer, `${fileName}.docx`);
      ElMessage.success(`DOCX文件 ${fileName}.docx 已下载完成`);
    }
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error(`导出${format.toUpperCase()}文件失败，请重试`);
  }
}
</script>

<style scoped>
/* 表单标签样式 */
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* 表单输入框样式 - 统一边框颜色与AnimatedInput一致 */
.form-input :deep(.el-input__wrapper) {
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.form-input :deep(.el-input__wrapper:focus-within) {
  border-color: #4F46E5 !important;
  box-shadow: none !important;
}

.form-textarea :deep(.el-textarea__inner) {
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.form-textarea :deep(.el-textarea__inner:focus) {
  border-color: #4F46E5 !important;
  box-shadow: none !important;
}

/* 客户选择器样式 */
.client-selector {
  position: relative;
}

.client-dropdown {
  min-width: 250px;
  max-width: 400px;
}

/* Material Icons */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  line-height: 1;
  transition: color 0.2s ease;
}

/* 按钮悬停效果 */
button:hover {
  @apply transform transition-transform duration-200;
}

/* 覆盖 Element Plus 主题色 */
:deep(.el-input) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-textarea) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-checkbox) {
  --el-color-primary: #4F46E5 !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .w-96 {
    @apply w-full;
  }
  
  .flex {
    @apply flex-col;
  }
  
  .h-screen {
    @apply min-h-screen;
  }
  
  /* 移动端调整 */
  .w-96 {
    @apply w-full;
  }
  
  .px-6 {
    @apply px-4;
  }
  
  .p-6 {
    @apply p-4;
  }
}

/* 经历选择相关样式 */
.experience-section {
  @apply mb-4 last:mb-0;
}

.section-title {
  @apply text-xs font-semibold text-gray-600 mb-2 px-1;
}

.section-items {
  @apply space-y-1;
}

.experience-item {
  @apply flex items-start p-2 hover:bg-white rounded-md cursor-pointer transition-colors duration-200 border border-transparent hover:border-gray-200;
}

.item-content {
  @apply ml-2 text-xs text-gray-700 select-none leading-relaxed flex-1;
}

/* 生成按钮动画效果 */
.typing-text {
  @apply font-medium;
}

.dots {
  @apply ml-1 flex;
}

.dot {
  @apply inline-block w-1 h-1 bg-white rounded-full mx-0.5;
  animation: typing 1.4s infinite;
}

.dot:nth-child(1) {
  animation-delay: 0s;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.progress-bar {
  animation: progress 2s ease-in-out infinite;
  width: 0%;
}

@keyframes progress {
  0% {
    width: 0%;
    opacity: 0.6;
  }
  50% {
    width: 70%;
    opacity: 1;
  }
  100% {
    width: 100%;
    opacity: 0.3;
  }
}

/* 流式生成状态指示器 */
.streaming-indicator {
  @apply inline-flex items-center px-2 py-1 bg-blue-50 text-blue-600 rounded-full text-xs font-medium;
}

.streaming-indicator::before {
  content: '';
  @apply w-2 h-2 bg-blue-400 rounded-full mr-2;
  animation: pulse 1s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

:deep(.el-popper) {
  --el-dropdown-menuItem-hover-color: #6366f1 !important; /* 紫色 */
}
</style> 