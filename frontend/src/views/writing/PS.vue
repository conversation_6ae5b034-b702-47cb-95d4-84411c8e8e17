<template>
  <div class="h-screen bg-white flex overflow-hidden">
    <!-- 左侧表单区域 -->
    <div class="w-96 flex-shrink-0 bg-white border-r border-gray-200 flex flex-col h-full">
      <div class="flex-1 overflow-y-auto p-6">
          <div class="space-y-6">
            <!-- 客户档案 -->
            <div>
              <div class="flex items-center space-x-2 mb-3">
                <span class="material-icons-outlined text-[#4F46E5] text-lg">person</span>
                <span class="text-sm font-medium text-gray-700 flex items-center">
                  <span class="text-red-500 mr-1">*</span>客户档案
                </span>
                <button 
                  type="button"
                  @click="navigateToClientList"
                  class="text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium ml-auto flex items-center"
                >
                  + 新建档案
                </button>
              </div>

              <!-- 已选择客户信息显示 -->
              <div v-if="selectedClient" class="mb-3">
                <div class="flex items-center p-3 bg-[#4F46E5]/5 rounded-lg border border-[#4F46E5]/20">
                  <div class="w-8 h-8 rounded-full bg-[#4F46E5] text-white flex items-center justify-center text-xs font-medium mr-3">
                    {{ selectedClient.name?.charAt(0)?.toUpperCase() || '?' }}
                  </div>
                  <div class="flex-1">
                    <div class="font-medium text-gray-800 text-sm">{{ selectedClient.name }}</div>
                    <div class="text-xs text-gray-500">
                      {{ selectedClient.phone || '暂无联系方式' }}
                      {{ selectedClient.location ? ' · ' + selectedClient.location : '' }}
                    </div>
                  </div>
                  <button 
                    @click="clearSelectedClient"
                    class="text-gray-500 hover:text-gray-700 p-1 rounded-md hover:bg-gray-100 transition-colors"
                  >
                    <span class="material-icons-outlined text-sm">clear</span>
                  </button>
                </div>
              </div>

              <!-- 客户选择搜索框 -->
              <div v-else class="relative client-selector">
                <AnimatedInput
                  v-model="clientSearchQuery"
                  label="搜索客户"
                  placeholder="输入客户姓名、联系方式或学生ID"
                  type="input"
                  @input="handleClientSearch"
                  @focus="handleClientSearchFocus"
                />

                <!-- 搜索结果下拉框 -->
                <Teleport to="body">
                  <div 
                    v-if="showClientSelector && (clientSearchResults.length > 0 || clientSearchLoading)" 
                    ref="clientDropdown"
                    class="fixed z-[9999] bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto client-dropdown"
                    :style="dropdownStyle"
                  >
                    <!-- 搜索结果 -->
                    <div v-if="clientSearchResults.length > 0" class="p-1">
                      <div 
                        v-for="client in clientSearchResults" 
                        :key="client.id_hashed"
                        @click="selectClient(client)"
                        class="flex items-center p-2 hover:bg-gray-50 rounded-md cursor-pointer transition-colors"
                      >
                        <div class="w-6 h-6 rounded-full bg-[#4F46E5]/10 text-[#4F46E5] flex items-center justify-center text-xs font-medium mr-2">
                          {{ client.name?.charAt(0)?.toUpperCase() || '?' }}
                        </div>
                        <div class="flex-1">
                          <div class="font-medium text-gray-800 text-xs">{{ client.name }}</div>
                          <div class="text-xs text-gray-500">
                            {{ client.phone || '暂无联系方式' }}
                            {{ client.location ? ' · ' + client.location : '' }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 无搜索结果 -->
                    <div v-else-if="clientSearchQuery && !clientSearchLoading && clientSearchResults.length === 0" class="p-3 text-center text-gray-500 text-xs">
                      未找到匹配的客户
                    </div>
                    
                    <!-- 默认提示（首次点击且没有搜索内容时） -->
                    <div v-else-if="!clientSearchQuery && !clientSearchLoading && clientSearchResults.length === 0" class="p-3 text-center text-gray-500 text-xs">
                      输入客户姓名、联系方式或学生ID进行搜索
                    </div>

                    <!-- 加载状态 -->
                    <div v-else-if="clientSearchLoading" class="p-3 text-center text-gray-500 text-xs">
                      <span class="material-icons-outlined animate-spin mr-1 text-sm">refresh</span>
                      搜索中...
                    </div>
                  </div>
                </Teleport>
              </div>
            </div>

            <!-- 申请院校与专业 -->
            <div>
              <div class="flex items-center space-x-2 mb-3">
                <span class="material-icons-outlined text-[#4F46E5] text-lg">school</span>
                <span class="text-sm font-medium text-gray-700 flex items-center">
                  <span class="text-red-500 mr-1">*</span>申请院校与专业
                </span>
              </div>

              <!-- 已选择的院校专业信息显示 -->
              <div v-if="formData.selectedProgram" class="mb-3">
                <div class="flex items-start p-3 bg-[#4F46E5]/5 rounded-lg border border-[#4F46E5]/20">
                  <div class="w-8 h-8 rounded-full bg-white border border-gray-200 flex items-center justify-center mr-3 mt-1 overflow-hidden">
                    <img 
                      :src="getSchoolLogo(formData.selectedProgram.school_name_cn)"
                      :alt="formData.selectedProgram.school_name_cn || '学校'"
                      class="w-full h-full object-contain"
                      @error="handleImageError"
                    />
                  </div>
                  <div class="flex-1">
                    <div class="font-medium text-gray-800 text-sm mb-1">
                      {{ formData.selectedProgram.school_name_cn }}
                      <span v-if="formData.selectedProgram.school_qs_rank" class="text-xs text-gray-500 ml-2">
                        QS排名: {{ formData.selectedProgram.school_qs_rank }}
                      </span>
                    </div>
                    <div class="text-sm text-[#4F46E5] font-medium mb-1">
                      {{ formData.selectedProgram.program_name_cn }}
                    </div>
                    <div class="text-xs text-gray-500">
                      {{ formData.selectedProgram.degree || '学位未知' }}
                      <span v-if="formData.selectedProgram.school_region">
                        · {{ formData.selectedProgram.school_region }}
                      </span>
                    </div>
                  </div>
                  <button 
                    @click="clearSelectedProgram"
                    class="text-gray-500 hover:text-gray-700 p-1 rounded-md hover:bg-gray-100 transition-colors"
                  >
                    <span class="material-icons-outlined text-sm">clear</span>
                  </button>
                </div>
              </div>

              <!-- 院校专业下拉选择 -->
              <div v-else class="relative program-selector" :class="{ 'disabled': !selectedClient }">
                <AnimatedInput
                  v-model="programSearchQuery"
                  :label="selectedClient ? '选择申请院校与专业' : '请先选择客户档案'"
                  :placeholder="selectedClient ? '选择具体要写的院校专业信息' : '请先选择客户档案'"
                  type="input"
                  :disabled="!selectedClient"
                  @input="handleProgramSearch"
                  @focus="handleProgramSearchFocus"
                />

                <!-- 搜索结果下拉框 -->
                <Teleport to="body">
                  <div 
                    v-if="showProgramSelector && selectedClient" 
                    ref="programDropdown"
                    class="fixed z-[9999] bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto program-dropdown"
                    :style="programDropdownStyle"
                  >
                    <!-- 暂无定校书数据时的提示 -->
                    <div v-if="!programsLoading && clientPrograms.length === 0" class="p-3 text-center">
                      <div class="text-gray-500 text-xs mb-2">暂无定校书数据</div>
                      <button 
                        @click="navigateToClientSchools"
                        class="text-[#4F46E5] text-xs hover:text-[#4338CA] transition-colors duration-200 font-medium"
                      >
                        前往添加定校书
                      </button>
                    </div>

                    <!-- 有定校书数据时显示搜索结果 -->
                    <div v-else-if="clientPrograms.length > 0">
                      <!-- 搜索结果 -->
                      <div v-if="filteredPrograms.length > 0" class="p-1">
                        <div 
                          v-for="program in filteredPrograms" 
                          :key="program.id"
                          @click="selectProgram(program.program_details)"
                          class="flex items-center p-2 hover:bg-gray-50 rounded-md cursor-pointer transition-colors"
                        >
                          <div class="w-6 h-6 rounded-full bg-white border border-gray-200 flex items-center justify-center mr-2 overflow-hidden">
                            <img 
                              :src="getSchoolLogo(program.program_details?.school_name_cn)"
                              :alt="program.program_details?.school_name_cn || '学校'"
                              class="w-full h-full object-contain"
                              @error="handleImageError"
                            />
                          </div>
                          <div class="flex-1">
                            <div class="font-medium text-gray-800 text-xs">
                              {{ program.program_details?.school_name_cn || '学校未知' }}
                              <span v-if="program.program_details?.school_qs_rank" class="text-xs text-gray-500 ml-2">
                                QS{{ program.program_details.school_qs_rank }}
                              </span>
                            </div>
                            <div class="text-xs text-[#4F46E5]">
                              {{ program.program_details?.program_name_cn || '专业未知' }} - {{ program.program_details?.degree || '学位未知' }}
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 无搜索结果 -->
                      <div v-else-if="programSearchQuery && !programsLoading && filteredPrograms.length === 0" class="p-3 text-center text-gray-500 text-xs">
                        未找到匹配的院校专业
                      </div>

                      <!-- 首次点击时显示所有项目 -->
                      <div v-else-if="!programSearchQuery && !programsLoading" class="p-3 text-center text-gray-500 text-xs">
                        输入院校或专业名称进行搜索，或浏览下方所有项目
                      </div>
                    </div>

                    <!-- 加载状态 -->
                    <div v-if="programsLoading" class="p-3 text-center text-gray-500 text-xs">
                      <span class="material-icons-outlined animate-spin mr-1 text-sm">refresh</span>
                      加载中...
                    </div>
                  </div>
                </Teleport>
              </div>
            </div>




            <!-- 学生自身经历描述 -->
            <div>
              <label class="form-label text-red-500">
                <span class="text-red-500 mr-1">*</span>学生自身经历描述
                <button class="ml-2 text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium">
                  查看范例
                </button>
                <button class="ml-2 text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium">
                  上传
                </button>
              </label>
              <el-input
                v-model="formData.personalExperience"
                type="textarea"
                :rows="8"
                placeholder="1. 你为什么要选择这个专业？&#10;2. 你对该专业的哪个领域感兴趣？（举例说明）&#10;3. 你未来的职业/人生规划是什么？&#10;..."
                class="form-textarea"
              ></el-input>
            </div>

            <!-- 院校要求及其他说明 -->
            <div>
              <label class="form-label">
                院校要求及其他说明
                <button class="ml-2 text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium">
                  上传
                </button>
              </label>
              <el-input
                v-model="formData.schoolRequirements"
                type="textarea"
                :rows="6"
                placeholder="请输入院校要求和其他需要说明的信息"
                class="form-textarea"
              ></el-input>
            </div>

            <!-- 目标字数 -->
            <div>
              <div class="flex items-center justify-between mb-3">
                <label class="form-label mb-0">目标字数</label>
                <span class="text-sm font-medium text-[#4F46E5]">{{ getWordLimitDisplay(formData.wordLimit) }}</span>
              </div>
              <div class="px-2">
                <el-slider
                  v-model="formData.wordLimit"
                  :marks="wordLimitMarks"
                  :step="1"
                  :min="0"
                  :max="20"
                  :show-tooltip="false"
                  class="custom-slider"
                />
              </div>
            </div>

            <!-- 段落设置 -->
            <div>
              <div class="flex items-center justify-between mb-3">
                <label class="form-label mb-0">段落设置</label>
                <span class="text-sm font-medium text-[#4F46E5]">{{ getParagraphDisplay(paragraphValue) }}</span>
              </div>
              <div class="px-2">
                <el-slider
                  v-model="paragraphValue"
                  :marks="paragraphMarks"
                  :step="1"
                  :min="0"
                  :max="6"
                  :show-tooltip="false"
                  @input="handleParagraphInput"
                  @change="handleParagraphChange"
                  class="custom-slider"
                />
              </div>
            </div>

            <!-- 底部按钮区域 -->
            <div class="pt-6 border-t border-gray-100 mt-6">
              <button 
                @click="handleGeneratePS" 
                :disabled="isGenerating || !isFormValid"
                :class="[
                  'w-full py-3 px-4 rounded-lg font-medium text-white transition-colors duration-200 flex items-center justify-center',
                  isFormValid && !isGenerating
                    ? 'bg-[#4F46E5] hover:bg-[#4338CA] shadow-sm cursor-pointer'
                    : 'bg-gray-400 cursor-not-allowed'
                ]"
              >
                <span v-if="isGenerating">
                  <span class="material-icons-outlined animate-spin mr-2 text-lg">refresh</span>
                  生成中...
                </span>
                <span v-else>
                  <span class="material-icons-outlined mr-2 text-lg">auto_fix_high</span>
                  提交生成
                </span>
              </button>
            </div>
          </div>
        </div>
    </div>

    <!-- 右侧编辑器区域 -->
    <div class="flex-1 bg-white h-full overflow-hidden">
      <TiptapEditor
        v-model="psContent"
        document-type="ps"
        placeholder="开始编写您的PS，或点击左侧'提交生成'获取AI建议..."
        @save="handleSave"
        @export="handleExport"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, onBeforeUnmount, Teleport } from 'vue'
import { ElMessage, ElSlider } from 'element-plus'
import { useRouter, onBeforeRouteLeave } from 'vue-router'
import TiptapEditor from '@/components/writing/TiptapEditor.vue'
import AnimatedInput from '@/components/common/AnimatedInput.vue'
import { searchClients, getClientPrograms } from '@/api/client'
import { getSchoolLogo } from '@/utils/schoolLogos'
import { Document, Packer, Paragraph, TextRun, HeadingLevel } from 'docx'
import html2pdf from 'html2pdf.js'
import { saveAs } from 'file-saver'

const router = useRouter()

// 客户档案相关状态
const selectedClient = ref(null)
const showClientSelector = ref(false)
const clientSearchQuery = ref('')
const clientSearchResults = ref([])
const clientSearchLoading = ref(false)
const clientDropdown = ref(null)
const dropdownStyle = ref({})

// 院校专业选择相关状态
const showProgramSelector = ref(false)
const programSearchQuery = ref('')
const filteredPrograms = ref([])
const programDropdown = ref(null)
const programDropdownStyle = ref({})

// 表单数据
const formData = reactive({
  selectedProgram: null, // 选择的院校专业信息
  documentType: 'ps',
  personalExperience: '',
  schoolRequirements: '',
  wordLimit: 0, // 滑动器数值：0=不限制, 100的倍数表示字数(如5=500词, 10=1000词)
  paragraphSetting: 0 // 滑动器数值：0=不限制, 1=智能分段, 2=4段, 3=5段, 4=6段, 5=7段, 6=8段
})

// 定校书相关状态
const clientPrograms = ref([])
const programsLoading = ref(false)



// 目标字数滑动器标记
const wordLimitMarks = {
  0: '不限制',
  5: '500',
  10: '1000', 
  15: '1500',
  20: '2000'
}

// 段落设置滑动器标记
const paragraphMarks = {
  0: '不限制',
  1: '智能分段',
  2: '4段',
  3: '5段',
  4: '6段',
  5: '7段',
  6: '8段'
}

// PS内容
const psContent = ref('')
const isGenerating = ref(false)

// 独立的段落设置变量
const paragraphValue = ref(0)

// 获取字数限制显示文本
const getWordLimitDisplay = (value) => {
  if (value === 0) return '不限制'
  return `${value * 100}词`
}

// 获取段落设置显示文本
const getParagraphDisplay = (value) => {
  const displays = ['不限制', '智能分段', '4段', '5段', '6段', '7段', '8段']
  return displays[value] || '不限制'
}

// 段落设置输入处理
const handleParagraphInput = (value) => {
  console.log('段落设置实时变化:', value)
  formData.paragraphSetting = value
}

// 段落设置变化处理
const handleParagraphChange = (value) => {
  console.log('段落设置变化完成:', value, getParagraphDisplay(value))
  formData.paragraphSetting = value
}

// 验证表单是否有效
const isFormValid = computed(() => {
  return selectedClient.value && 
         formData.selectedProgram && 
         formData.personalExperience
})

// 导航到客户列表页面
const navigateToClientList = () => {
  router.push('/clients')
}

// 导航到客户档案页面的定校书部分
const navigateToClientProfile = () => {
  if (selectedClient.value) {
    router.push(`/clients/${selectedClient.value.id_hashed}`)
  } else {
    ElMessage.warning('请先选择客户档案')
  }
}

// 导航到客户档案页面的定校书标签页
const navigateToClientSchools = () => {
  if (selectedClient.value) {
    router.push(`/clients/${selectedClient.value.id_hashed}?tab=schools`)
  } else {
    ElMessage.warning('请先选择客户档案')
  }
}

// 处理图片加载错误
const handleImageError = (event) => {
  // 如果图片加载失败，使用默认logo
  event.target.src = getSchoolLogo('')
}

// 计算下拉框位置
const calculateDropdownPosition = () => {
  const inputElement = document.querySelector('.client-selector .animated-input-container')
  if (inputElement) {
    const rect = inputElement.getBoundingClientRect()
    dropdownStyle.value = {
      position: 'fixed',
      top: `${rect.bottom + 4}px`,
      left: `${rect.left}px`,
      width: `${rect.width}px`,
      zIndex: 9999
    }
  }
}

// 计算院校专业下拉框位置
const calculateProgramDropdownPosition = () => {
  const inputElement = document.querySelector('.program-selector .animated-input-container')
  if (inputElement) {
    const rect = inputElement.getBoundingClientRect()
    programDropdownStyle.value = {
      position: 'fixed',
      top: `${rect.bottom + 4}px`,
      left: `${rect.left}px`,
      width: `${rect.width}px`,
      zIndex: 9999
    }
  }
}

// 处理客户搜索输入框 focus 事件
const handleClientSearchFocus = async () => {
  showClientSelector.value = true
  calculateDropdownPosition()
  
  // 如果没有搜索内容，加载默认客户列表
  if (!clientSearchQuery.value || clientSearchQuery.value.trim() === '') {
    await loadDefaultClientList()
  }
}

// 加载默认客户列表
const loadDefaultClientList = async () => {
  clientSearchLoading.value = true
  try {
    const response = await searchClients('')
    let clients = response.data || response || []
    
    // 按最近修改时间排序
    clients.sort((a, b) => {
      const dateA = new Date(a.updated_at || a.created_at || 0)
      const dateB = new Date(b.updated_at || b.created_at || 0)
      return dateB - dateA // 倒序排列，最新的在前面
    })
    
    clientSearchResults.value = clients
  } catch (error) {
    console.error('加载客户列表失败:', error)
    clientSearchResults.value = []
  } finally {
    clientSearchLoading.value = false
  }
}

// 处理客户搜索
const handleClientSearch = async (query) => {
  if (!query || query.trim() === '') {
    if (showClientSelector.value) {
      await loadDefaultClientList()
    } else {
      clientSearchResults.value = []
    }
    return
  }

  clientSearchLoading.value = true
  try {
    const response = await searchClients(query.trim())
    let clients = response.data || response || []
    
    // 按最近修改时间排序搜索结果
    clients.sort((a, b) => {
      const dateA = new Date(a.updated_at || a.created_at || 0)
      const dateB = new Date(b.updated_at || b.created_at || 0)
      return dateB - dateA // 倒序排列，最新的在前面
    })
    
    clientSearchResults.value = clients
  } catch (error) {
    console.error('搜索客户失败:', error)
    clientSearchResults.value = []
    ElMessage.error('搜索客户失败')
  } finally {
    clientSearchLoading.value = false
  }
}

// 选择客户 - 优化版本，添加加载状态
const selectClient = async (client) => {
  // 显示加载状态
  const loadingMessage = ElMessage({
    type: 'info',
    message: '正在加载客户信息...',
    duration: 0 // 不自动关闭
  })

  try {
    selectedClient.value = client
    showClientSelector.value = false
    clientSearchQuery.value = ''
    clientSearchResults.value = []

    // 清空院校专业搜索框
    programSearchQuery.value = ''

    // 自动加载定校书
    await loadClientPrograms()

    loadingMessage.close()
    ElMessage.success(`已选择客户：${client.name}`)
  } catch (error) {
    loadingMessage.close()
    console.error('选择客户时发生错误:', error)
    ElMessage.error('选择客户失败，请重试')
  }
}

// 清除选中的客户
const clearSelectedClient = () => {
  selectedClient.value = null
  psContent.value = ''
  // 清除定校书数据
  clientPrograms.value = []
  formData.selectedProgram = null
}

// 加载客户定校书
const loadClientPrograms = async () => {
  if (!selectedClient.value) {
    ElMessage.warning('请先选择客户档案')
    return
  }

  programsLoading.value = true
  try {
    const response = await getClientPrograms(selectedClient.value.id_hashed)
    clientPrograms.value = response.data || response || []
    
    if (clientPrograms.value.length === 0) {
      ElMessage.info('该客户暂无定校书数据')
    } else {
      ElMessage.success(`成功加载 ${clientPrograms.value.length} 个定校书项目`)
    }
  } catch (error) {
    console.error('加载定校书失败:', error)
    ElMessage.error('加载定校书失败')
    clientPrograms.value = []
  } finally {
    programsLoading.value = false
  }
}

// 选择院校专业
const selectProgram = (programDetails) => {
  if (!programDetails) {
    ElMessage.error('程序信息无效')
    return
  }

  formData.selectedProgram = programDetails
  showProgramSelector.value = false
  programSearchQuery.value = ''
  filteredPrograms.value = []
  ElMessage.success(`已选择：${programDetails.school_name_cn} - ${programDetails.program_name_cn}`)
}

// 处理院校专业搜索输入框 focus 事件
const handleProgramSearchFocus = async () => {
  if (!selectedClient.value) {
    // 没有选择客户档案时，不响应任何操作
    return
  }

  showProgramSelector.value = true
  calculateProgramDropdownPosition()

  // 如果没有搜索内容，显示所有定校书项目
  if (!programSearchQuery.value || programSearchQuery.value.trim() === '') {
    filteredPrograms.value = clientPrograms.value
  }
}

// 处理院校专业搜索
const handleProgramSearch = async (query) => {
  if (!selectedClient.value) {
    return
  }

  if (!query || query.trim() === '') {
    filteredPrograms.value = clientPrograms.value
    return
  }

  const searchTerm = query.trim().toLowerCase()
  filteredPrograms.value = clientPrograms.value.filter(program => {
    const schoolName = program.program_details?.school_name_cn?.toLowerCase() || ''
    const programName = program.program_details?.program_name_cn?.toLowerCase() || ''
    const degree = program.program_details?.degree?.toLowerCase() || ''
    
    return schoolName.includes(searchTerm) || 
           programName.includes(searchTerm) || 
           degree.includes(searchTerm)
  })
}

// 处理下拉框选择变化
const handleProgramChange = (programDetails) => {
  if (programDetails) {
    ElMessage.success(`已选择：${programDetails.school_name_cn} - ${programDetails.program_name_cn}`)
  }
}

// 清除选择的院校专业
const clearSelectedProgram = () => {
  formData.selectedProgram = null
  filteredPrograms.value = []
}



// 点击外部关闭下拉框
const handleClickOutside = (event) => {
  // 客户档案下拉框
  const dropdown = clientDropdown.value
  const clientSelector = document.querySelector('.client-selector')
  
  if (dropdown && clientSelector && 
      !dropdown.contains(event.target) && 
      !clientSelector.contains(event.target)) {
    showClientSelector.value = false
  }

  // 院校专业下拉框
  const programDropdownEl = programDropdown.value
  const programSelector = document.querySelector('.program-selector')
  
  if (programDropdownEl && programSelector && 
      !programDropdownEl.contains(event.target) && 
      !programSelector.contains(event.target)) {
    showProgramSelector.value = false
  }
}

// 监听窗口滚动和调整大小，更新下拉框位置
const handleWindowEvent = () => {
  if (showClientSelector.value) {
    calculateDropdownPosition()
  }
  if (showProgramSelector.value) {
    calculateProgramDropdownPosition()
  }
}

// 页面切换时自动取消生成
onBeforeRouteLeave((to, from, next) => {
  if (isGenerating.value) {
    isGenerating.value = false
    ElMessage.info('检测到页面切换，已自动取消生成')
  }
  next()
})

// 组件卸载时自动取消生成
onBeforeUnmount(() => {
  if (isGenerating.value) {
    isGenerating.value = false
  }
})

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  window.addEventListener('scroll', handleWindowEvent, true)
  window.addEventListener('resize', handleWindowEvent)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('scroll', handleWindowEvent, true)
  window.removeEventListener('resize', handleWindowEvent)
})

// 生成PS初稿
const handleGeneratePS = async () => {
  if (!isFormValid.value) {
    ElMessage.warning('请先选择客户档案并完成所有必填项目')
    return
  }

  isGenerating.value = true
  try {
    ElMessage.success('正在生成PS初稿，请稍候...')
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 生成的PS模板
    const generatedPS = generatePSTemplate()
    psContent.value = generatedPS
    
    ElMessage.success('PS初稿生成完成，您可以继续编辑')
  } catch (error) {
    ElMessage.error('生成失败，请重试')
    console.error('Generate PS error:', error)
  } finally {
    isGenerating.value = false
  }
}

// 生成PS模板
const generatePSTemplate = () => {
  const currentDate = new Date().toLocaleDateString('zh-CN')
  const docTypeTitle = formData.documentType === 'ps' ? 'Personal Statement' : 'Statement of Purpose'
  const wordLimitText = getWordLimitDisplay(formData.wordLimit)
  const paragraphText = getParagraphDisplay(formData.paragraphSetting)
  
  const schoolName = formData.selectedProgram?.school_name_cn || '目标院校'
  const programName = formData.selectedProgram?.program_name_cn || '目标专业'
  const degree = formData.selectedProgram?.degree || '硕士'
  const region = formData.selectedProgram?.school_region || ''
  
  return `
    <div style="margin-bottom: 30px; color: #374151;">
      <p style="margin-bottom: 20px; font-weight: 500; color: #4F46E5; font-size: 16px; text-align: center;">
        ${docTypeTitle}
      </p>
      <p style="margin-bottom: 10px;">日期: ${currentDate}</p>
      <p style="margin-bottom: 10px;">申请院校: <strong style="color: #4F46E5;">${schoolName}</strong>${region ? ` (${region})` : ''}</p>
      <p style="margin-bottom: 10px;">申请学位: <strong style="color: #4F46E5;">${degree}</strong></p>
      <p style="margin-bottom: 10px;">申请专业: <strong style="color: #4F46E5;">${programName}</strong></p>
      <p style="margin-bottom: 10px;">文书类型: <strong style="color: #4F46E5;">${formData.documentType.toUpperCase()}</strong></p>
      <p style="margin-bottom: 10px;">目标字数: ${wordLimitText}</p>
      <p style="margin-bottom: 20px;">段落设置: ${paragraphText}</p>
    </div>
    
    <h1 style="text-align: center; font-size: 20px; font-weight: bold; margin-bottom: 30px; color: #1F2937;">
      ${docTypeTitle}
    </h1>
    
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      ${formData.personalExperience}
    </p>
    
    ${formData.schoolRequirements ? `
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      <strong>院校要求及其他说明：</strong><br>
      ${formData.schoolRequirements.replace(/\n/g, '<br>')}
    </p>
    ` : ''}
    
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      我对<strong style="color: #4F46E5;">${programName}</strong>专业的学术兴趣和热情使我坚定地选择了这个领域。通过深入的学习和实践，我相信自己具备了在贵校继续深造的能力和决心。
    </p>
    
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      我特别向往<strong style="color: #4F46E5;">${schoolName}</strong>，因为该校在<strong style="color: #4F46E5;">${programName}</strong>领域的卓越声誉和丰富的研究机会与我的学术目标完美契合。
    </p>
    
    <p style="line-height: 1.6; color: #374151;">
      我确信我的学术背景、实践经验和对该领域的热情使我成为贵校项目的优秀候选人。我期待为学术社区做出贡献，并向杰出的教师和同学学习。
    </p>
  `
}

// 将HTML内容转换为docx文档段落
const htmlToDocxParagraphs = (htmlContent) => {
  // 简单的HTML解析，提取文本和基本格式
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = htmlContent
  
  const paragraphs = []
  
  const processNode = (node) => {
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent.trim()
      if (text) {
        return new TextRun({
          text: text,
          bold: false,
          italics: false
        })
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const tagName = node.tagName.toLowerCase()
      const text = node.textContent.trim()
      
      if (text) {
        switch (tagName) {
          case 'h1':
            paragraphs.push(new Paragraph({
              text: text,
              heading: HeadingLevel.HEADING_1
            }))
            return null
          case 'h2':
            paragraphs.push(new Paragraph({
              text: text,
              heading: HeadingLevel.HEADING_2
            }))
            return null
          case 'h3':
            paragraphs.push(new Paragraph({
              text: text,
              heading: HeadingLevel.HEADING_3
            }))
            return null
          case 'p':
            paragraphs.push(new Paragraph({
              children: [new TextRun(text)]
            }))
            return null
          case 'strong':
          case 'b':
            return new TextRun({
              text: text,
              bold: true
            })
          case 'em':
          case 'i':
            return new TextRun({
              text: text,
              italics: true
            })
          case 'li':
            paragraphs.push(new Paragraph({
              children: [new TextRun(`• ${text}`)]
            }))
            return null
          default:
            return new TextRun(text)
        }
      }
    }
    return null
  }
  
  const walkNodes = (node) => {
    const textRuns = []
    for (let child of node.childNodes) {
      const result = processNode(child)
      if (result) {
        textRuns.push(result)
      } else if (child.nodeType === Node.ELEMENT_NODE) {
        walkNodes(child)
      }
    }
    if (textRuns.length > 0) {
      paragraphs.push(new Paragraph({
        children: textRuns
      }))
    }
  }
  
  walkNodes(tempDiv)
  
  // 如果没有解析出段落，创建一个默认段落
  if (paragraphs.length === 0) {
    const plainText = tempDiv.textContent || ''
    const lines = plainText.split('\n').filter(line => line.trim())
    lines.forEach(line => {
      paragraphs.push(new Paragraph({
        children: [new TextRun(line.trim())]
      }))
    })
  }
  
  return paragraphs
}

// 保存处理
const handleSave = (data) => {
  console.log('Saving PS:', data)
  // 这里实现保存到后端的逻辑
  ElMessage.info('保存功能正在开发中...');
}

// 导出处理
const handleExport = async (data) => {
  const { content, textContent, format } = data;
  const clientName = selectedClient.value?.name || 'PS';
  // 使用 yyyy-mm-dd 格式的日期
  const dateStr = new Date().toLocaleDateString('sv');
  const fileName = `${clientName}_PS_${dateStr}`;

  if (!content) {
    ElMessage.warning('没有可导出的内容');
    return;
  }

  try {
    if (format === 'pdf') {
      ElMessage.info('正在生成PDF文件...')
      
      // 创建一个临时容器用于PDF生成
      const element = document.createElement('div')
      element.innerHTML = content
      element.style.cssText = `
        font-family: Arial, sans-serif;
        line-height: 1.6;
        max-width: 210mm;
        margin: 0 auto;
        padding: 20mm;
        color: #333;
        background: white;
      `
      
      // 添加PDF专用样式
      const style = document.createElement('style')
      style.textContent = `
        h1, h2, h3 { margin-top: 1.5em; margin-bottom: 0.5em; page-break-after: avoid; }
        h1 { font-size: 24px; }
        h2 { font-size: 20px; }
        h3 { font-size: 16px; }
        p { margin: 0.75em 0; }
        ul, ol { margin: 0.75em 0; padding-left: 2em; }
        li { margin: 0.25em 0; }
        blockquote { 
          border-left: 4px solid #ddd; 
          padding-left: 1em; 
          margin: 1em 0; 
          font-style: italic; 
        }
        strong { font-weight: bold; }
        em { font-style: italic; }
      `
      element.appendChild(style)
      
      const opt = {
        margin: [10, 10, 10, 10],
        filename: `${fileName}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { 
          scale: 2,
          useCORS: true,
          letterRendering: true
        },
        jsPDF: { 
          unit: 'mm', 
          format: 'a4', 
          orientation: 'portrait' 
        }
      }
      
      await html2pdf().set(opt).from(element).save()
      ElMessage.success(`PDF文件 ${fileName}.pdf 已下载完成`)

    } else if (format === 'txt') {
      // 导出为纯文本文件
      const finalFileName = `${fileName}.txt`;
      const finalTextContent = textContent || content.replace(/<[^>]*>/g, '').replace(/\n\s*\n/g, '\n\n').trim();
      const blob = new Blob([finalTextContent], { type: 'text/plain;charset=utf-8' });
      saveAs(blob, finalFileName);
      ElMessage.success(`TXT文件 ${finalFileName} 已下载完成`);
      
    } else if (format === 'docx') {
      ElMessage.info('正在生成DOCX文件...')
      
      // 使用docx库创建真正的Word文档
      const paragraphs = htmlToDocxParagraphs(content)
      
      const doc = new Document({
        sections: [{
          properties: {},
          children: paragraphs
        }]
      })
      
      const buffer = await Packer.toBlob(doc)
      saveAs(buffer, `${fileName}.docx`)
      ElMessage.success(`DOCX文件 ${fileName}.docx 已下载完成`)
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error(`导出${format.toUpperCase()}文件失败，请重试`)
  }
}
</script> 

<style scoped>
/* 表单标签样式 */
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* 表单输入框样式 - 统一边框颜色与AnimatedInput一致 */
.form-input :deep(.el-input__wrapper) {
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.form-input :deep(.el-input__wrapper:focus-within) {
  border-color: #4F46E5 !important;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1) !important;
}

.form-textarea :deep(.el-textarea__inner) {
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.form-textarea :deep(.el-textarea__inner:focus) {
  border-color: #4F46E5 !important;
  box-shadow: none !important;
}

/* 选择框样式 */
.form-input :deep(.el-select) {
  @apply w-full;
}

.form-input :deep(.el-select__wrapper) {
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.form-input :deep(.el-select__wrapper:focus-within) {
  border-color: #4F46E5 !important;
  box-shadow: none !important;
}

/* 客户选择器样式 */
.client-selector {
  position: relative;
}

.client-dropdown {
  min-width: 250px;
  max-width: 400px;
}

/* 院校专业选择器样式 */
.program-selector {
  position: relative;
}

.program-dropdown {
  min-width: 250px;
  max-width: 400px;
}

/* 禁用状态的输入框样式 */
.program-selector.disabled {
  pointer-events: none !important;
  cursor: not-allowed !important;
}

.program-selector.disabled :deep(.animated-input-wrapper) {
  background-color: #f9fafb !important;
  border-color: #e5e7eb !important;
  opacity: 0.8 !important;
  pointer-events: none !important;
  cursor: not-allowed !important;
}

.program-selector.disabled :deep(.animated-input) {
  background-color: transparent !important;
  color: #9ca3af !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}

.program-selector.disabled :deep(.animated-label) {
  color: #9ca3af !important;
}

.program-selector.disabled :deep(.animated-input-wrapper:hover) {
  border-color: #e5e7eb !important;
  box-shadow: none !important;
  background-color: #f9fafb !important;
}

.program-selector.disabled :deep(.animated-input-wrapper:focus) {
  border-color: #e5e7eb !important;
  box-shadow: none !important;
  background-color: #f9fafb !important;
}

.program-selector.disabled :deep(.animated-input-wrapper:active) {
  border-color: #e5e7eb !important;
  box-shadow: none !important;
  background-color: #f9fafb !important;
}



/* 单选按钮样式 */
input[type="radio"] {
  @apply w-4 h-4;
  accent-color: #4F46E5;
}

/* Material Icons */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  line-height: 1;
  transition: color 0.2s ease;
}

/* 按钮悬停效果 */
button:hover {
  @apply transform transition-transform duration-200;
}

/* 覆盖 Element Plus 主题色 */
:deep(.el-input) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-textarea) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-select) {
  --el-color-primary: #4F46E5 !important;
}

/* Element Plus 下拉选择框紫色主题 */
:deep(.el-select-dropdown__item.is-hovering) {
  background-color: rgba(79, 70, 229, 0.1) !important;
  color: #4F46E5 !important;
}

:deep(.el-select-dropdown__item.is-selected) {
  color: #4F46E5 !important;
  font-weight: 500 !important;
}

/* 滑动器样式 */
.custom-slider {
  margin: 15px 0 35px 0;
  height: 50px;
  position: relative;
}

.custom-slider :deep(.el-slider__runway) {
  background-color: #E5E7EB;
  height: 6px;
  border-radius: 3px;
  cursor: pointer;
}

.custom-slider :deep(.el-slider__bar) {
  background-color: #4F46E5;
  height: 6px;
  border-radius: 3px;
}

.custom-slider :deep(.el-slider__button) {
  background-color: #4F46E5;
  border: 2px solid #FFFFFF;
  width: 20px;
  height: 20px;
  box-shadow: 0 2px 6px rgba(79, 70, 229, 0.3);
  cursor: grab;
  transition: all 0.2s ease;
  position: relative;
  z-index: 10;
}

.custom-slider :deep(.el-slider__button:hover) {
  background-color: #4338CA;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.custom-slider :deep(.el-slider__button:active) {
  cursor: grabbing;
  transform: scale(0.95);
}

.custom-slider :deep(.el-slider__button-wrapper) {
  cursor: pointer;
  z-index: 10;
  position: relative;
}

.custom-slider :deep(.el-slider__marks) {
  top: 20px;
  position: absolute;
  width: 100%;
}

.custom-slider :deep(.el-slider__marks-text) {
  font-size: 12px;
  color: #6B7280;
  white-space: nowrap;
  cursor: pointer;
  user-select: none;
  position: absolute;
  transform: translateX(-50%);
  line-height: 1;
  margin-top: 5px;
}

.custom-slider :deep(.el-slider__marks .el-slider__marks-text:hover) {
  color: #4F46E5;
  font-weight: 500;
}

/* 确保滑动器可以正常交互 */
.custom-slider :deep(.el-slider) {
  pointer-events: all;
  height: 20px;
  position: relative;
}

.custom-slider :deep(.el-slider__runway) {
  margin: 0;
}

/* 强制确保滑动器交互 */
.custom-slider :deep(.el-slider__button-wrapper) {
  z-index: 999 !important;
  pointer-events: auto !important;
}

.custom-slider :deep(.el-slider__runway) {
  pointer-events: auto !important;
}

.custom-slider :deep(.el-slider) {
  z-index: 10 !important;
}

/* 禁用任何可能阻止交互的父级样式 */
.custom-slider {
  overflow: visible !important;
  z-index: 10;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .w-96 {
    @apply w-full;
  }
  
  .flex {
    @apply flex-col;
  }
  
  .h-screen {
    @apply min-h-screen;
  }
  
  /* 移动端调整 */
  .w-96 {
    @apply w-full;
  }
  
  .px-6 {
    @apply px-4;
  }
  
  .p-6 {
    @apply p-4;
  }
}
</style> 

<style scoped>
/* 移除了重复的样式块 */



/* 单选按钮样式 */
input[type="radio"] {
  @apply w-4 h-4;
  accent-color: #4F46E5;
}

/* Material Icons */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  line-height: 1;
  transition: color 0.2s ease;
}

/* 按钮悬停效果 */
button:hover {
  @apply transform transition-transform duration-200;
}

/* 覆盖 Element Plus 主题色 */
:deep(.el-input) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-textarea) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-select) {
  --el-color-primary: #4F46E5 !important;
}

/* Element Plus 下拉选择框紫色主题 */
:deep(.el-select-dropdown__item.is-hovering) {
  background-color: rgba(79, 70, 229, 0.1) !important;
  color: #4F46E5 !important;
}

:deep(.el-select-dropdown__item.is-selected) {
  color: #4F46E5 !important;
  font-weight: 500 !important;
}

/* 滑动器样式 */
.custom-slider {
  margin: 15px 0 35px 0;
  height: 50px;
  position: relative;
}

.custom-slider :deep(.el-slider__runway) {
  background-color: #E5E7EB;
  height: 6px;
  border-radius: 3px;
  cursor: pointer;
}

.custom-slider :deep(.el-slider__bar) {
  background-color: #4F46E5;
  height: 6px;
  border-radius: 3px;
}

.custom-slider :deep(.el-slider__button) {
  background-color: #4F46E5;
  border: 2px solid #FFFFFF;
  width: 20px;
  height: 20px;
  box-shadow: 0 2px 6px rgba(79, 70, 229, 0.3);
  cursor: grab;
  transition: all 0.2s ease;
  position: relative;
  z-index: 10;
}

.custom-slider :deep(.el-slider__button:hover) {
  background-color: #4338CA;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.custom-slider :deep(.el-slider__button:active) {
  cursor: grabbing;
  transform: scale(0.95);
}

.custom-slider :deep(.el-slider__button-wrapper) {
  cursor: pointer;
  z-index: 10;
  position: relative;
}

.custom-slider :deep(.el-slider__marks) {
  top: 20px;
  position: absolute;
  width: 100%;
}

.custom-slider :deep(.el-slider__marks-text) {
  font-size: 12px;
  color: #6B7280;
  white-space: nowrap;
  cursor: pointer;
  user-select: none;
  position: absolute;
  transform: translateX(-50%);
  line-height: 1;
  margin-top: 5px;
}

.custom-slider :deep(.el-slider__marks .el-slider__marks-text:hover) {
  color: #4F46E5;
  font-weight: 500;
}

/* 确保滑动器可以正常交互 */
.custom-slider :deep(.el-slider) {
  pointer-events: all;
  height: 20px;
  position: relative;
}

.custom-slider :deep(.el-slider__runway) {
  margin: 0;
}

/* 强制确保滑动器交互 */
.custom-slider :deep(.el-slider__button-wrapper) {
  z-index: 999 !important;
  pointer-events: auto !important;
}

.custom-slider :deep(.el-slider__runway) {
  pointer-events: auto !important;
}

.custom-slider :deep(.el-slider) {
  z-index: 10 !important;
}

/* 禁用任何可能阻止交互的父级样式 */
.custom-slider {
  overflow: visible !important;
  z-index: 10;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .w-96 {
    @apply w-full;
  }
  
  .flex {
    @apply flex-col;
  }
  
  .h-screen {
    @apply min-h-screen;
  }
  
  /* 移动端调整 */
  .w-96 {
    @apply w-full;
  }
  
  .px-6 {
    @apply px-4;
  }
  
  .p-6 {
    @apply p-4;
  }
}
</style> 