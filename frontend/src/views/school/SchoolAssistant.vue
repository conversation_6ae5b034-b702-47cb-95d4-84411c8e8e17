<template>
  <div class="school-assistant-container">
    <!-- 1. 顶部导航区: 仿照专业库页面的设计 -->
    <div class="page-header">
      <div class="container">
        <div class="header-content">
          <div class="title-section">
            <h1 class="page-title">选校匹配</h1>
            <p class="page-subtitle">通过填写学生信息，获取精准的院校匹配方案</p>
          </div>
          <div class="stats-section">
            <div class="stat-item">
              <div class="stat-number">AI</div>
              <div class="stat-label">智能匹配</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">1,000+</div>
              <div class="stat-label">精选院校</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">7</div>
              <div class="stat-label">热门地区</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 2. 主要内容区域 -->
    <div class="main-content">
      <div class="container">
        <!-- 主要内容区域 -->
        <div class="flex flex-col xl:flex-row gap-6">
          <!-- 左侧：信息输入表单 -->
          <div 
            class="xl:w-96 xl:flex-shrink-0 transition-all duration-300"
            :class="{
              'lg:w-full': !sidebarCollapsed || !isLargeScreen,
              'lg:w-80': sidebarCollapsed && isLargeScreen,
              'lg:flex-shrink-0': sidebarCollapsed && isLargeScreen
            }"
          >
            <div class="pro-card">
              <!-- 表单标题 -->
              <div class="pro-card-header flex justify-between items-center">
                <h3 class="pro-card-title">填写申请信息</h3>
                <!-- 在大屏幕上显示折叠按钮 -->
                <button 
                  v-if="isLargeScreen && !isExtraLargeScreen"
                  @click="toggleSidebar"
                  class="lg:block xl:hidden text-gray-500 hover:text-gray-700 p-1 rounded-md hover:bg-gray-100 transition-colors"
                >
                  <span class="material-icons-outlined text-sm">
                    {{ sidebarCollapsed ? 'keyboard_arrow_right' : 'keyboard_arrow_left' }}
                  </span>
                </button>
                             </div>

              <!-- 表单内容 -->
              <form 
                @submit.prevent="handleSubmit" 
                class="divide-y divide-gray-100"
                :class="{ 'collapsed-form': sidebarCollapsed && isLargeScreen && !isExtraLargeScreen }"
              >
                <!-- 客户档案 -->
                <div class="px-4 py-3">
                  <div 
                    class="flex items-center space-x-2 mb-2 cursor-pointer"
                    @click="toggleSection('client')"
                  >
                    <span class="material-icons-outlined text-[#4F46E5] text-lg">person</span>
                    <h4 class="text-sm font-semibold text-gray-600">客户档案</h4>

                    <span class="material-icons-outlined text-gray-400 text-sm transition-transform duration-300" 
                      :class="sectionStates.client ? 'rotate-180' : ''">
                      expand_more
                    </span>
                  </div>
                  
                  <div 
                    class="space-y-2 form-section-content client-section-content" 
                    :style="{ 
                      maxHeight: sectionStates.client ? '1000px' : '0px', 
                      opacity: sectionStates.client ? 1 : 0,
                      visibility: sectionStates.client ? 'visible' : 'hidden'
                    }"
                  >
                    <!-- 已选择客户信息显示 -->
                    <div v-if="selectedClient" class="mb-3">
                      <div class="flex items-center p-3 bg-[#4F46E5]/5 rounded-lg border border-[#4F46E5]/20">
                        <div class="w-8 h-8 rounded-full bg-[#4F46E5] text-white flex items-center justify-center text-xs font-medium mr-3">
                          {{ selectedClient.name?.charAt(0)?.toUpperCase() || '?' }}
                        </div>
                        <div class="flex-1">
                          <div class="font-medium text-gray-800 text-sm">{{ selectedClient.name }}</div>
                          <div class="text-xs text-gray-500">
                            {{ selectedClient.phone || '暂无联系方式' }}
                            {{ selectedClient.location ? ' · ' + selectedClient.location : '' }}
                          </div>
                        </div>
                        <button 
                          @click.stop="clearSelectedClient"
                          class="text-gray-500 hover:text-gray-700 p-1 rounded-md hover:bg-gray-100 transition-colors"
                        >
                          <span class="material-icons-outlined text-sm">clear</span>
                        </button>
                      </div>
                    </div>

                    <!-- 客户选择区域 -->
                    <div v-else>


                      <!-- 搜索框 -->
                      <div class="relative client-selector">
                        <AnimatedInput
                          v-model="clientSearchQuery"
                          label="搜索客户"
                          placeholder="输入客户姓名、联系方式或学生ID"
                          type="input"
                          @input="handleClientSearch"
                          @focus="handleClientSearchFocus"
                        />

                        <!-- 搜索结果下拉框 -->
                        <Teleport to="body">
                          <div 
                            v-if="showClientSelector && (clientSearchResults.length > 0 || clientSearchLoading)" 
                            ref="clientDropdown"
                            class="fixed z-[9999] bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto client-dropdown"
                            :style="dropdownStyle"
                          >
                          <!-- 搜索结果 -->
                          <div v-if="clientSearchResults.length > 0" class="p-1">
                            <div 
                              v-for="client in clientSearchResults" 
                              :key="client.id_hashed"
                              @click="selectClient(client)"
                              class="flex items-center p-2 hover:bg-gray-50 rounded-md cursor-pointer transition-colors"
                            >
                              <div class="w-6 h-6 rounded-full bg-[#4F46E5]/10 text-[#4F46E5] flex items-center justify-center text-xs font-medium mr-2">
                                {{ client.name?.charAt(0)?.toUpperCase() || '?' }}
                              </div>
                              <div class="flex-1">
                                <div class="font-medium text-gray-800 text-xs">{{ client.name }}</div>
                                <div class="text-xs text-gray-500">
                                  {{ client.phone || '暂无联系方式' }}
                                  {{ client.location ? ' · ' + client.location : '' }}
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- 无搜索结果 -->
                          <div v-else-if="clientSearchQuery && !clientSearchLoading && clientSearchResults.length === 0" class="p-3 text-center text-gray-500 text-xs">
                            未找到匹配的客户
                          </div>
                          
                          <!-- 默认提示（首次点击且没有搜索内容时） -->
                          <div v-else-if="!clientSearchQuery && !clientSearchLoading && clientSearchResults.length === 0" class="p-3 text-center text-gray-500 text-xs">
                            输入客户姓名、联系方式或学生ID进行搜索
                          </div>

                                                      <!-- 加载状态 -->
                            <div v-else-if="clientSearchLoading" class="p-3 text-center text-gray-500 text-xs">
                              <span class="material-icons-outlined animate-spin mr-1 text-sm">refresh</span>
                              搜索中...
                            </div>
                          </div>
                        </Teleport>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 学术背景 -->
                <div class="px-4 py-3">
                  <div 
                    class="flex items-center space-x-2 mb-2 cursor-pointer"
                    @click="toggleSection('academic')"
                  >
                    <span class="material-icons-outlined text-[#4F46E5] text-lg">school</span>
                    <h4 class="text-sm font-semibold text-gray-600">学术背景</h4>

                    <span class="material-icons-outlined text-gray-400 text-sm transition-transform duration-300" 
                      :class="sectionStates.academic ? 'rotate-180' : ''">
                      expand_more
                    </span>
                  </div>
                  
                  <div 
                    class="space-y-2 overflow-hidden form-section-content" 
                    :style="{ 
                      maxHeight: sectionStates.academic ? '1000px' : '0px', 
                      opacity: sectionStates.academic ? 1 : 0,
                      visibility: sectionStates.academic ? 'visible' : 'hidden'
                    }"
                  >
                    <!-- 使用动画输入组件的表单项 -->
                    <AnimatedInput
                      v-model="profileForm.academic.school"
                      label="本科学校"
                      placeholder="请输入本科学校"
                      type="autocomplete"
                      required
                      :hasError="formValidation.hasValidated && formValidation.errors.academic.school"
                      :fetchSuggestions="querySchools"
                      @input="clearFieldError('academic', 'school'); checkSectionCompletion('academic')"
                      @change="clearFieldError('academic', 'school'); checkSectionCompletion('academic')"
                    />
                  
                    <AnimatedInput
                      v-model="profileForm.academic.major"
                      label="本科专业"
                      placeholder="请输入本科专业"
                      type="input"
                      required
                      :hasError="formValidation.hasValidated && formValidation.errors.academic.major"
                      @input="clearFieldError('academic', 'major'); checkSectionCompletion('academic')"
                      @change="clearFieldError('academic', 'major'); checkSectionCompletion('academic')"
                    />
                  
                    <div class="grid grid-cols-2 gap-x-4 mb-0">
                      <AnimatedInput
                        v-model="profileForm.academic.gpaScale"
                        label="成绩制式"
                        placeholder="请选择成绩计算制度"
                        type="select"
                        :options="[
                          { label: '百分制', value: '100' },
                          { label: '4.0制', value: '4.0' },
                          { label: '5.0制', value: '5.0' }
                        ]"
                        required
                        :hasError="formValidation.hasValidated && formValidation.errors.academic.gpaScale"
                        @input="handleGpaScaleChange"
                        @change="handleGpaScaleChange"
                      />
                      
                      <AnimatedInput
                        v-model="profileForm.academic.gpa"
                        label="成绩"
                        :placeholder="gpaPlaceholder"
                        type="input"
                        required
                        :hasError="formValidation.hasValidated && (formValidation.errors.academic.gpa || gpaValidationError)"
                        @input="handleGpaInput"
                        @change="handleGpaChange"
                      />
                    </div>
                  
                    <!-- 班级排名选项暂时注释，业务代码中未使用
                    <AnimatedInput
                      v-model="profileForm.academic.ranking"
                      label="班级排名"
                      placeholder="请选择班级排名"
                      type="select"
                      :options="[
                        { label: '前5%', value: '5%' },
                        { label: '前10%', value: '10%' },
                        { label: '前20%', value: '20%' },
                        { label: '前50%', value: '50%' }
                      ]"
                      @change="checkSectionCompletion('academic')"
                    />
                    -->
                  </div>
                </div>

                <!-- 留学意向 - 优化布局 -->
                <div class="px-4 py-3">
                  <div 
                    class="flex items-center space-x-2 mb-2 cursor-pointer"
                    @click="toggleSection('intention')"
                  >
                    <span class="material-icons-outlined text-[#4F46E5] text-lg">flight_takeoff</span>
                    <h4 class="text-sm font-semibold text-gray-600">留学意向</h4>

                    <span class="material-icons-outlined text-gray-400 text-sm transition-transform duration-300" 
                      :class="sectionStates.intention ? 'rotate-180' : ''">
                      expand_more
                    </span>
                  </div>
                  
                  <div 
                    class="space-y-2 overflow-hidden form-section-content" 
                    :style="{ 
                      maxHeight: sectionStates.intention ? '1000px' : '0px', 
                      opacity: sectionStates.intention ? 1 : 0,
                      visibility: sectionStates.intention ? 'visible' : 'hidden'
                    }"
                  >
                    <AnimatedInput
                      v-model="profileForm.intention.countries"
                      label="意向国家/地区"
                      placeholder="请选择意向的国家或地区（可多选）"
                      type="select"
                      :options="[
                        { label: '中国香港', value: 'Hong Kong' },
                        { label: '新加坡', value: 'Singapore' },
                        { label: '英国', value: 'UK' },
                        { label: '美国', value: 'USA' },
                        { label: '澳大利亚', value: 'Australia' },
                        { label: '中国澳门', value: 'Macau' },
                        { label: '马来西亚', value: 'Malaysia' }
                      ]"
                      multiple
                      required
                      :hasError="formValidation.hasValidated && formValidation.errors.intention.countries"
                      @change="clearFieldError('intention', 'countries'); checkSectionCompletion('intention')"
                    />
                  
                    <AnimatedInput
                      v-model="profileForm.intention.majors"
                      label="意向专业/领域"
                      placeholder="请选择意向专业领域（可多选）"
                      type="select"
                      :hasError="formValidation.hasValidated && formValidation.errors.intention.majors"
                      :optionGroups="[
                        {
                          label: '商科领域',
                          options: [
                            { label: '金工金数', value: 'financial_engineering' },
                            { label: '金融', value: 'finance' },
                            { label: '商业分析', value: 'business_analytics' },
                            { label: '经济', value: 'economics' },
                            { label: '会计', value: 'accounting' },
                            { label: '市场营销', value: 'marketing' },
                            { label: '信息系统', value: 'information_systems' },
                            { label: '管理', value: 'management' },
                            { label: '人力资源管理', value: 'hrm' },
                            { label: '供应链管理', value: 'scm' },
                            { label: '创业与创新', value: 'entrepreneurship' },
                            { label: '房地产', value: 'real_estate' },
                            { label: '旅游酒店管理', value: 'hospitality' },
                            { label: '工商管理', value: 'business_admin' },
                            { label: '其他商科', value: 'other_business' }
                          ]
                        },
                        {
                          label: '社科领域',
                          options: [
                            { label: '教育', value: 'education' },
                            { label: '建筑', value: 'architecture' },
                            { label: '法律', value: 'law' },
                            { label: '社会学与社工', value: 'sociology' },
                            { label: '国际关系', value: 'international_relations' },
                            { label: '哲学', value: 'philosophy' },
                            { label: '历史', value: 'history' },
                            { label: '公共政策与事务', value: 'public_policy' },
                            { label: '艺术', value: 'arts' },
                            { label: '公共卫生', value: 'public_health' },
                            { label: '心理学', value: 'psychology' },
                            { label: '体育', value: 'sports' },
                            { label: '药学', value: 'pharmacy' },
                            { label: '医学', value: 'medicine' },
                            { label: '新闻', value: 'journalism' },
                            { label: '影视', value: 'film' },
                            { label: '文化', value: 'culture' },
                            { label: '媒体与传播', value: 'media_communication' },
                            { label: '新媒体', value: 'new_media' },
                            { label: '媒介与社会', value: 'media_society' },
                            { label: '科学传播', value: 'science_communication' },
                            { label: '策略传播', value: 'strategic_communication' },
                            { label: '媒体产业', value: 'media_industry' },
                            { label: '语言', value: 'language' },
                            { label: '其他社科', value: 'other_social_science' }
                          ]
                        },
                        {
                          label: '工科领域',
                          options: [
                            { label: '计算机', value: 'computer_science' },
                            { label: '电气电子', value: 'electrical_engineering' },
                            { label: '机械工程', value: 'mechanical_engineering' },
                            { label: '材料', value: 'materials' },
                            { label: '化工', value: 'chemical_engineering' },
                            { label: '生物工程', value: 'bioengineering' },
                            { label: '土木工程', value: 'civil_engineering' },
                            { label: '工程管理', value: 'engineering_management' },
                            { label: '环境工程', value: 'environmental_engineering' },
                            { label: '工业工程', value: 'industrial_engineering' },
                            { label: '能源', value: 'energy' },
                            { label: '航空工程', value: 'aerospace' },
                            { label: '地球科学', value: 'earth_science' },
                            { label: '交通运输', value: 'transportation' },
                            { label: '海洋技术', value: 'marine_technology' },
                            { label: '食品科学', value: 'food_science' },
                            { label: '其他工科', value: 'other_engineering' }
                          ]
                        },
                        {
                          label: '理科领域',
                          options: [
                            { label: '物理', value: 'physics' },
                            { label: '化学', value: 'chemistry' },
                            { label: '数学', value: 'mathematics' },
                            { label: '生物', value: 'biology' },
                            { label: '数据科学', value: 'data_science' }
                          ]
                        }
                      ]"
                      multiple
                      required
                      @change="clearFieldError('intention', 'majors'); checkSectionCompletion('intention')"
                    />
                    <!-- End of Selection -->
                  
                    <!-- 期望项目时长已隐藏，默认为空（不限） -->
                    <AnimatedInput
                      v-model="profileForm.intention.duration"
                      label="期望项目时长"
                      placeholder="请选择期望的项目时长（可选）"
                      type="select"
                      :options="[
                        { label: '1年', value: '1' },
                        { label: '1.5年', value: '1.5' },
                        { label: '2年', value: '2' }
                      ]"
                      @change="checkSectionCompletion('intention')"
                    />

                  </div>
                </div>

                <!-- 软实力与偏好 -->
                <div class="px-4 py-3">
                  <div 
                    class="flex items-center space-x-2 mb-2 cursor-pointer"
                    @click="toggleSection('strength')"
                  >
                    <span class="material-icons-outlined text-[#4F46E5] text-lg">stars</span>
                    <h4 class="text-sm font-semibold text-gray-600">软实力与偏好</h4>
                    <span class="text-xs text-gray-500">(可选)</span>
                    <span class="material-icons-outlined text-gray-400 text-sm transition-transform duration-300" 
                      :class="sectionStates.strength ? 'rotate-180' : ''">
                      expand_more
                    </span>
                  </div>
                  
                  <div 
                    class="space-y-2 overflow-hidden form-section-content" 
                    :style="{ 
                      maxHeight: sectionStates.strength ? '1000px' : '0px', 
                      opacity: sectionStates.strength ? 1 : 0,
                      visibility: sectionStates.strength ? 'visible' : 'hidden'
                    }"
                  >
                    <AnimatedInput
                      v-model="profileForm.strength.competition"
                      label="竞赛经历"
                      placeholder="请选择获得的竞赛奖项级别"
                      type="select"
                      :options="[
                        { label: '省级', value: 'provincial' },
                        { label: '国家级', value: 'national' },
                        { label: '国际级', value: 'international' }
                      ]"
                      multiple
                    />
                  
                    <AnimatedInput
                      v-model="profileForm.strength.internship"
                      label="实习经历"
                      placeholder="请选择实习经历类型"
                      type="select"
                      :options="[
                        { label: '互联网大厂', value: 'internet' },
                        { label: '头部金融机构', value: 'finance' },
                        { label: '4A', value: '4a' },
                        { label: '四大会计事务所', value: 'big4' },
                        { label: '世界500强', value: 'fortune500' },
                        { label: '上市公司', value: 'listed' },
                        { label: '其他', value: 'other' }
                      ]"
                      multiple
                    />
                  
                    <AnimatedInput
                      v-model="profileForm.strength.research"
                      label="科研经历"
                      placeholder="请选择科研成果类型"
                      type="select"
                      :options="[
                        { label: '普通刊物发表论文', value: 'normal-paper' },
                        { label: '核心刊物发表论文', value: 'core-paper' },
                        { label: 'SCI刊物发表论文', value: 'sci-paper' },
                        { label: '实用新型专利', value: 'utility-patent' },
                        { label: '发明专利', value: 'invention-patent' }
                      ]"
                      multiple
                    />
                  
                    <!-- 院校排名偏好选项暂时注释，让其默认为不选择
                    <AnimatedInput
                      v-model="profileForm.strength.rankingPreference"
                      label="院校排名偏好"
                      placeholder="请选择排名范围"
                      type="select"
                      :options="[
                        { label: 'QS Top 10', value: 'QS-10' },
                        { label: 'QS Top 50', value: 'QS-50' },
                        { label: 'QS Top 100', value: 'QS-100' }
                      ]"
                      multiple
                    />
                    -->
                  
                    <AnimatedInput
                      v-model="profileForm.strength.preferenceType"
                      label="申请偏好类型"
                      placeholder="请选择申请偏好类型（可选）"
                      type="select"
                      :options="[
                        { label: '排名导向型（更看重学校排名）', value: 'ranking_focused' },
                        { label: '专业导向型（更看重专业匹配）', value: 'major_focused' }
                      ]"
                      @change="checkSectionCompletion('strength')"
                    />
                  </div>
                </div>

                <!-- 提交按钮 -->
                <div class="pro-card-footer bg-gray-50 !flex !justify-end !items-center !gap-4">
                  <div class="flex items-center space-x-2">
                    <el-checkbox v-model="enableAiSelection" size="small" class="ai-selection-checkbox">
                      <span class="text-xs font-medium text-[#4F46E5]">启用AI智能匹配</span>
                    </el-checkbox>
                    <el-tooltip content="关闭后按照专业库匹配，快速查找所有符合条件的专业" placement="top">
                      <span class="material-icons-outlined text-gray-400 text-sm cursor-pointer hover:text-[#4F46E5] transition-colors">
                        help_outline
                      </span>
                    </el-tooltip>
                  </div>
                  
                  <el-button 
                    type="primary" 
                    @click="handleSubmit"
                    :loading="isLoading"
                    class="!bg-[#4F46E5] !border-[#4F46E5] !h-7 !px-3 !text-xs"
                  >
                    <span class="material-icons-outlined mr-1 !text-xs">send</span>
                    开始匹配
                  </el-button>
                </div>
              </form>
            </div>
          </div>
          
          <!-- 右侧：匹配结果展示 -->
          <div class="flex-1 relative">
            <div class="pro-card">
              <div class="pro-card-header flex justify-between items-center">
                <h2 class="pro-card-title">
                  {{ currentResultMode === 'hard_filter' ? '匹配院校' : 
                     currentResultMode === 'ai' ? '匹配院校' : 
                     enableAiSelection ? '匹配院校' : '匹配院校' }}
                </h2>
                
                <!-- 排序选项 -->
                <div v-if="hasSubmitted && recommendations.length > 0" class="flex space-x-3">
                  <!-- 排序方式 -->
                  <el-select v-model="sortBy" placeholder="排序方式" size="small" class="w-36">
                    <template #prefix>
                      <span class="material-icons-outlined text-gray-400 text-sm">sort</span>
                    </template>
                    <el-option label="推荐排名" value="rank-asc" />
                    <el-option label="院校排名↓" value="ranking-desc" />
                    <el-option label="院校排名↑" value="ranking-asc" />
                  </el-select>
                </div>
              </div>
              
              <!-- 匹配结果内容区域 -->
              <div class="pro-card-body">
                <!-- 推荐进行中 -->
                <div v-if="streamingStatus === 'connecting' || streamingStatus === 'streaming'" class="mb-6">
                  
                  <!-- 进度条显示 -->
                  <div class="max-w-lg mx-auto">
                    <!-- 连接状态指示器 -->
                    <div class="flex items-center justify-center mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                      <div class="flex items-center space-x-4">
                        <div class="relative">
                          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-[#4F46E5]"></div>
                          <div class="absolute inset-0 rounded-full border-2 border-[#4F46E5]/20"></div>
                        </div>
                        <div class="flex flex-col">
                          <span class="text-sm font-medium text-[#4F46E5]">
                            {{ streamingStatus === 'connecting' ? '正在连接匹配系统' : currentStage }}
                          </span>
                          <span class="text-xs text-gray-600 mt-1">
                            {{ streamingStatus === 'connecting' ? '建立服务连接中' : 
                               streamingStatus === 'streaming' ? '正在分析申请条件' : '数据处理中' }}
                          </span>
                        </div>
                      </div>
                    </div>

                    <!-- 进度条 -->
                    <div class="w-full bg-gray-200 rounded-full h-3 mb-4 shadow-inner">
                      <div class="bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 h-3 rounded-full transition-all duration-700 ease-out relative overflow-hidden"
                           :style="{ width: `${recommendationProgress}%` }">
                        <!-- 流光效果 -->
                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 -skew-x-12 animate-pulse"></div>
                      </div>
                    </div>
                    
                    <!-- 进度文字和百分比 -->
                    <div class="flex items-center justify-between mb-4">
                      <div class="text-sm text-gray-700 font-medium">
                        {{ progressMessage }}
                      </div>
                      <div class="text-sm font-bold text-indigo-600">
                        {{ recommendationProgress }}%
                      </div>
                    </div>

                  </div>
                </div>

                <!-- 空状态展示 -->
                <div v-else-if="!hasSubmitted" class="py-6">
                  <!-- 顶部说明区域 -->
                  <div class="flex flex-col items-center justify-center py-0">
                    <div class="relative">
                      <!-- 背景装饰 -->
                      <div class="absolute inset-0 bg-gradient-to-br from-[#4F46E5]/10 to-[#7C3AED]/10 rounded-full blur-3xl animate-pulse"></div>
                      
                      <!-- 图标容器 -->
                      <div class="relative bg-gradient-to-br from-[#4F46E5]/5 to-[#7C3AED]/5 p-8 rounded-2xl">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-[#4F46E5] mx-auto animate-float" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                      </div>
                    </div>
                    
                    <div class="text-center mb-6">
                                          <h3 class="text-base font-semibold text-gray-800 mb-2">
                      {{ enableAiSelection ? 'AI智能匹配，精准推荐' : '专业库匹配，快速查找' }}
                    </h3>
                    <p class="text-sm text-gray-500">
                      {{ enableAiSelection 
                        ? '填写学生的学术背景和留学意向，系统将为您智能匹配最适合的院校方案' 
                        : '根据您的留学意向进行专业库精确匹配，快速找到所有符合条件的专业项目' 
                      }}
                    </p>
                    </div>

                    <!-- 底部统计信息 -->
                    <div class="pt-6 border-t border-gray-100">
                      <div class="grid grid-cols-3 gap-28 text-center">
                        <div>
                          <div class="text-xl font-bold text-[#4F46E5]">1,000+</div>
                          <div class="text-xs text-gray-500">院校数据</div>
                        </div>
                        <div>
                          <div class="text-xl font-bold text-[#4F46E5]">15,000+</div>
                          <div class="text-xs text-gray-500">录取案例</div>
                        </div>
                        <div>
                          <div class="text-xl font-bold text-[#4F46E5]">
                            {{ enableAiSelection ? '95%' : '100%' }}
                          </div>
                          <div class="text-xs text-gray-500">
                            {{ enableAiSelection ? '智能匹配准确率' : '数据覆盖率' }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 匹配结果列表 -->
                <div v-else class="space-y-4">
                  <p v-if="filteredRecommendations.length === 0" class="text-gray-500 text-center py-8">
                    暂无匹配结果
                  </p>



                  <!-- 专业库匹配模式：按学校分组显示 -->
                  <div v-if="currentResultMode === 'hard_filter' && schoolGroups.length > 0" class="space-y-4">
                    <!-- 结果统计 -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                      <div class="flex items-center text-sm text-blue-800">
                        <span class="material-icons-outlined text-blue-600 text-base mr-2">info</span>
                        共找到 <span class="font-semibold mx-1">{{ schoolGroups.length }}</span> 所学校的 
                        <span class="font-semibold mx-1">{{ filteredRecommendations.length }}</span> 个匹配专业
                      </div>
                    </div>

                    <!-- 学校分组列表 -->
                    <div 
                      v-for="(schoolGroup, index) in schoolGroups" 
                      :key="schoolGroup.schoolKey"
                      class="school-group-card border border-gray-200 rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-md transition-all duration-300"
                    >
                      <!-- 学校概览卡片（点击展开/收起） -->
                      <div 
                        @click="toggleSchoolGroup(schoolGroup)"
                        class="school-group-header cursor-pointer p-4 bg-gradient-to-r from-gray-50 via-white to-gray-50 border-b border-gray-100 hover:bg-gray-50 transition-colors"
                      >
                        <div class="flex items-center justify-between">
                          <!-- 学校基本信息 -->
                          <div class="flex items-center">
                            <!-- 学校Logo -->
                            <div class="flex-shrink-0 w-12 h-12 bg-white rounded-lg flex items-center justify-center overflow-hidden mr-3 border border-gray-100 shadow-sm">
                              <img 
                                :src="getSchoolLogo(schoolGroup.programs[0])"
                                :alt="schoolGroup.schoolName"
                                class="h-8 w-8 object-contain"
                                @error="handleLogoError($event, schoolGroup.programs[0])"
                              />
                            </div>
                            
                            <!-- 学校名称和基本信息 -->
                            <div class="flex-grow">
                              <h3 class="text-base font-semibold text-gray-800">{{ schoolGroup.schoolName }}</h3>
                              <p class="text-xs text-gray-500 mt-0.5">{{ schoolGroup.englishName }}</p>
                              <div class="flex items-center mt-1 space-x-4">
                                <span class="text-xs text-gray-600 flex items-center">
                                  <span class="material-icons-outlined text-gray-400 text-xs mr-1">location_on</span>
                                  {{ schoolGroup.location }}
                                </span>
                                <span class="text-xs text-gray-600 flex items-center">
                                  <span class="material-icons-outlined text-gray-400 text-xs mr-1">school</span>
                                  {{ schoolGroup.programs.length }} 个匹配专业
                                </span>
                              </div>
                            </div>
                          </div>

                          <!-- 学校排名和展开图标 -->
                          <div class="flex items-center space-x-3">
                            <!-- 学校排名 -->
                            <div class="ranking-badge">
                              <div class="ranking-label">QS</div>
                              <div class="ranking-number">{{ schoolGroup.qsRank || 'N/A' }}</div>
                            </div>
                            
                            <!-- 展开图标 -->
                            <span 
                              class="material-icons-outlined text-gray-400 transition-transform duration-300"
                              :class="{ 'rotate-180': schoolGroup.expanded }"
                            >
                              expand_more
                            </span>
                          </div>
                        </div>
                      </div>

                      <!-- 专业列表（展开时显示） -->
                      <div 
                        class="school-programs-container overflow-hidden transition-all duration-400 ease-in-out"
                        :style="{ 
                          maxHeight: schoolGroup.expanded ? schoolGroup.contentHeight + 'px' : '0px',
                          opacity: schoolGroup.expanded ? 1 : 0 
                        }"
                      >
                        <div 
                          class="school-programs-content p-4 space-y-3 bg-gray-25"
                          :ref="el => setProgramsContentRef(schoolGroup.schoolKey, el)"
                        >
                          <!-- 专业卡片列表 -->
                          <div 
                            v-for="(program, pIndex) in schoolGroup.programs" 
                            :key="program.id"
                            :data-program-id="program.program_id || program.id"
                            :data-program-name="program.专业中文名"
                            class="program-card bg-white border border-gray-150 rounded-lg overflow-hidden hover:shadow-sm transition-all duration-300"
                          >
                            <!-- 专业卡片头部 -->
                            <div class="px-4 py-3 bg-gradient-to-r from-gray-50 via-white to-gray-50 border-b border-gray-100">
                              <div class="flex items-start">
                                <!-- 学校Logo区域 -->
                                <div class="flex-shrink-0 w-14 h-14 bg-white rounded-lg flex items-center justify-center overflow-hidden mr-4 border border-gray-100 shadow-sm">
                                  <img 
                                    :src="getSchoolLogo(program)"
                                    :alt="program.学校中文名"
                                    class="h-10 w-10 object-contain"
                                    @error="handleLogoError($event, program)"
                                  />
                                </div>

                                <!-- 专业名称和基本信息 -->
                                <div class="flex-grow">
                                  <h3 class="text-base font-semibold text-gray-800">{{ program.专业中文名 }}</h3>
                                  <p class="text-xs text-gray-500 mt-0.5">{{ program.专业英文名 }}</p>
                                  <p class="text-xs text-gray-600 mt-1 flex items-center">
                                    <span class="material-icons-outlined text-gray-400 text-xs mr-1">category</span>
                                    {{ program.专业大类 }}
                                  </p>
                                </div>
                              </div>
                            </div>

                            <!-- 卡片主体内容：专业信息 -->
                            <div class="px-4 py-3">
                              <!-- 申请信息 -->
                              <div class="grid grid-cols-3 gap-x-3 gap-y-2 text-xs">
                                <div class="flex flex-col">
                                  <span class="text-gray-400 text-xs mb-0.5">专业大类</span> 
                                  <span class="font-medium text-gray-700">{{ program.专业大类 }}</span>
                                </div>
                                <div class="flex flex-col">
                                  <span class="text-gray-400 text-xs mb-0.5">专业方向</span> 
                                  <span class="font-medium text-gray-700">{{ program.专业方向 }}</span>
                                </div>
                                <div class="flex flex-col">
                                  <span class="text-gray-400 text-xs mb-0.5">所在学院</span> 
                                  <span class="font-medium text-gray-700">{{ program.所在学院 }}</span>
                                </div>
                                <div class="flex flex-col">
                                  <span class="text-gray-400 text-xs mb-0.5">入学时间</span> 
                                  <span class="font-medium text-gray-700">{{ program.入学时间 }}</span>
                                </div>
                                <div class="flex flex-col">
                                  <span class="text-gray-400 text-xs mb-0.5">项目时长</span> 
                                  <span class="font-medium text-gray-700">{{ program.项目时长 }}</span>
                                </div>
                                <div class="flex flex-col">
                                  <span class="text-gray-400 text-xs mb-0.5">学费</span> 
                                  <span class="font-medium text-gray-700">{{ program.tuitionRange }}</span>
                                </div>
                                <div class="flex flex-col col-span-3">
                                  <span class="text-gray-400 text-xs mb-0.5">申请截止</span> 
                                  <span class="font-medium text-gray-700">{{ program.deadline }}</span>
                                </div>
                              </div>

                              <!-- 亮点标签 -->
                              <div class="mt-3 flex flex-wrap gap-1.5">
                                <span 
                                  v-for="(highlight, hIndex) in program.highlights" 
                                  :key="hIndex"
                                  class="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-full bg-gray-50 text-gray-600 border border-gray-100"
                                >
                                  {{ highlight }}
                                </span>
                              </div>
                            </div>

                            <!-- 操作按钮区域 -->
                            <div class="px-4 py-3 bg-gray-50 border-b border-gray-100 flex justify-between items-center">
                              <a 
                                v-if="program.项目官网" 
                                :href="program.项目官网" 
                                target="_blank" 
                                class="text-indigo-600 text-xs flex items-center group hover:text-indigo-700 transition-colors"
                              >
                                <span class="material-icons-outlined mr-1 !text-sm">open_in_new</span>
                                <span class="group-hover:underline">查看官网</span>
                              </a>
                              <div v-else class="text-xs text-gray-400">官网信息待查询</div>
                              
                              <div class="flex items-center space-x-2">
                                <el-button
                                  :class="program.isFavorite ? '!text-red-600 hover:!text-red-700' : '!text-gray-600 hover:!text-red-600'"
                                  @click.stop="toggleProgramFavorite(program)"
                                  size="small"
                                  class="!h-7 !text-xs !rounded-md !px-2.5 transition-colors"
                                  v-if="!selectedClient"
                                >
                                  <span class="material-icons-outlined mr-1 !text-sm">{{ program.isFavorite ? 'favorite' : 'favorite_border' }}</span>
                                  <span class="!text-xs">{{ program.isFavorite ? '已收藏' : '收藏' }}</span>
                                </el-button>
                                
                                <!-- 定校书操作按钮（当选择客户时显示） -->
                                <el-button
                                  v-if="selectedClient"
                                  :class="isInClientPrograms(program) ? '!text-green-600 hover:!text-green-700' : '!text-[#4F46E5] hover:!text-[#4338CA]'"
                                  @click.stop="toggleClientProgram(program)"
                                  :disabled="addingToClientPrograms"
                                  size="small"
                                  class="!h-7 !text-xs !rounded-md !px-2.5 transition-colors"
                                >
                                  <span class="material-icons-outlined mr-1 !text-sm">{{ isInClientPrograms(program) ? 'check_circle' : 'add_circle' }}</span>
                                  <span class="!text-xs">{{ isInClientPrograms(program) ? '已添加' : '添加到定校书' }}</span>
                                </el-button>
                                
                                <el-button 
                                  type="primary"
                                  @click.stop="toggleProgramDetails(program)"
                                  class="!bg-indigo-600 !border-indigo-600 hover:!bg-indigo-700 !h-7 !text-xs !rounded-md !px-2.5 !transition-colors"
                                >
                                  <span class="material-icons-outlined mr-1 !text-xs">{{ program.showDetails ? 'expand_less' : 'expand_more' }}</span>
                                  <span class="!text-xs">{{ program.showDetails ? '收起详情' : '查看详情' }}</span>
                                </el-button>
                              </div>
                            </div>

                            <!-- 专业详细信息（展开时显示） -->
                            <div 
                              class="details-container w-full overflow-hidden" 
                              :style="{ 
                                height: program.detailsHeight + 'px', 
                                opacity: program.showDetails ? 1 : 0,
                                visibility: program.showDetails ? 'visible' : 'hidden',
                                transition: 'height 0.4s ease, opacity 0.4s ease, visibility 0.1s',
                                transitionDelay: program.showDetails ? '0s' : '0.3s, 0.1s, 0.4s'
                              }"
                            >
                              <div class="details-content p-4 bg-gray-50 border-t border-gray-100">
                                <!-- 详情信息，分类显示 -->
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-3">
                                  <!-- 申请要求 -->
                                  <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
                                    <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                                      <span class="material-icons-outlined text-gray-400 text-sm mr-1">assignment</span>
                                      申请要求
                                    </h4>
                                    <p class="text-xs text-gray-600 leading-relaxed">{{ program.申请要求 || '申请要求信息待查询' }}</p>
                                  </div>
                                  
                                  <!-- 语言要求 -->
                                  <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
                                    <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                                      <span class="material-icons-outlined text-gray-400 text-sm mr-1">translate</span>
                                      语言要求
                                    </h4>
                                    <p class="text-xs text-gray-600 leading-relaxed">{{ program.语言要求 || '语言要求信息待查询' }}</p>
                                  </div>
                                  
                                  <!-- 项目培养目标 -->
                                  <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm lg:col-span-2">
                                    <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                                      <span class="material-icons-outlined text-gray-400 text-sm mr-1">lightbulb</span>
                                      培养目标
                                    </h4>
                                    <p class="text-xs text-gray-600 leading-relaxed">{{ program.培养目标 || '培养目标信息待查询' }}</p>
                                  </div>
                                  
                                  <!-- 课程设置 -->
                                  <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm lg:col-span-2">
                                    <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                                      <span class="material-icons-outlined text-gray-400 text-sm mr-1">school</span>
                                      课程设置
                                    </h4>
                                    <p class="text-xs text-gray-600 leading-relaxed">{{ program.课程设置 || '课程设置信息待查询' }}</p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- AI推荐模式：原有的单个学校卡片显示 -->
                  <div v-else-if="currentResultMode === 'ai' && filteredRecommendations.length > 0" class="space-y-4">
                    <!-- 学校推荐卡片 - 保持原有布局 -->
                    <div 
                      v-for="(school, index) in filteredRecommendations" 
                      :key="index" 
                      class="school-card relative border border-gray-200 rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-md transition-all duration-300"
                      :data-school-id="school.id"
                    >
                      <!-- 保持原有的学校卡片内容... -->
                      <!-- 卡片头部：学校基本信息 -->
                      <div class="px-4 py-3 bg-gradient-to-r from-gray-50 via-white to-gray-50 border-b border-gray-100">
                        <div class="flex items-start">
                          <!-- 学校Logo区域 -->
                          <div class="flex-shrink-0 w-14 h-14 bg-white rounded-lg flex items-center justify-center overflow-hidden mr-4 border border-gray-100 shadow-sm">
                            <img 
                              :src="getSchoolLogo(school)"
                              :alt="school.学校中文名"
                              class="h-10 w-10 object-contain"
                              @error="handleLogoError($event, school)"
                            />
                          </div>

                          <!-- 学校名称和基本信息 -->
                          <div class="flex-grow">
                            <h3 class="text-base font-semibold text-gray-800">{{ school.学校中文名 }} - {{ school.专业中文名 }}</h3>
                            <p class="text-xs text-gray-500 mt-0.5">{{ school.学校英文名 }}{{ school.专业英文名 ? ' - ' + school.专业英文名 : '' }}</p>
                            <p class="text-xs text-gray-600 mt-1 flex items-center">
                              <span class="material-icons-outlined text-gray-400 text-xs mr-1">location_on</span>
                              {{ school.location }}
                            </p>
                          </div>

                          <!-- 评分、等级和排名 -->
                          <div class="flex items-center space-x-2 sm:space-x-3 ml-auto self-center">
                            <!-- 学校排名 - 现代化设计 -->
                            <div class="ranking-badge">
                              <div class="ranking-label">QS</div>
                              <div class="ranking-number">{{ school.ranking.match(/\d+/)?.[0] || 'N/A' }}</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 卡片主体内容：专业信息 -->
                      <div class="px-4 py-3">
                        <!-- 申请信息 -->
                        <div class="grid grid-cols-3 gap-x-3 gap-y-2 text-xs">
                          <div class="flex flex-col">
                            <span class="text-gray-400 text-xs mb-0.5">专业大类</span> 
                            <span class="font-medium text-gray-700">{{ school.专业大类 }}</span>
                          </div>
                          <div class="flex flex-col">
                            <span class="text-gray-400 text-xs mb-0.5">专业方向</span> 
                            <span class="font-medium text-gray-700">{{ school.专业方向 }}</span>
                          </div>
                          <div class="flex flex-col">
                            <span class="text-gray-400 text-xs mb-0.5">所在学院</span> 
                            <span class="font-medium text-gray-700">{{ school.所在学院 }}</span>
                          </div>
                          <div class="flex flex-col">
                            <span class="text-gray-400 text-xs mb-0.5">入学时间</span> 
                            <span class="font-medium text-gray-700">{{ school.入学时间 }}</span>
                          </div>
                          <div class="flex flex-col">
                            <span class="text-gray-400 text-xs mb-0.5">项目时长</span> 
                            <span class="font-medium text-gray-700">{{ school.项目时长 }}</span>
                          </div>
                          <div class="flex flex-col">
                            <span class="text-gray-400 text-xs mb-0.5">学费</span> 
                            <span class="font-medium text-gray-700">{{ school.tuitionRange }}</span>
                          </div>
                          <div class="flex flex-col col-span-3">
                            <span class="text-gray-400 text-xs mb-0.5">申请截止</span> 
                            <span class="font-medium text-gray-700">{{ school.deadline }}</span>
                          </div>
                        </div>

                        <!-- 亮点标签 -->
                        <div class="mt-3 flex flex-wrap gap-1.5">
                          <span 
                            v-for="(highlight, hIndex) in school.highlights" 
                            :key="hIndex"
                            class="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-full bg-gray-50 text-gray-600 border border-gray-100"
                          >
                            {{ highlight }}
                          </span>
                        </div>
                      </div>

                      <!-- 操作按钮区域 -->
                      <div class="px-4 py-2.5 bg-gray-50 border-t border-gray-100 flex justify-between items-center">
                        <a 
                          v-if="school.项目官网" 
                          :href="school.项目官网" 
                          target="_blank" 
                          class="text-indigo-600 text-xs flex items-center group"
                        >
                          <span class="material-icons-outlined mr-1 !text-sm">open_in_new</span>
                          <span class="group-hover:underline">查看官网</span>
                        </a>
                        <div class="flex items-center space-x-2">
                          <el-button 
                            type="primary"
                            @click="toggleDetails(school)"
                            class="!bg-indigo-600 !border-indigo-600 hover:!bg-indigo-700 !h-7 !text-xs !rounded-md !px-2.5 !py-1 !transition-colors"
                          >
                            <span class="material-icons-outlined mr-1 !text-xs">{{ school.showDetails ? 'expand_less' : 'expand_more' }}</span>
                            <span class="!text-xs">{{ school.showDetails ? '收起详情' : '查看详情' }}</span>
                          </el-button>
                          <el-button
                            :class="school.isFavorite ? '!text-indigo-600 !h-7 !text-xs !rounded-md !px-2.5' : '!h-7 !text-xs !rounded-md !px-2.5'"
                            @click="toggleFavorite(school)"
                            v-if="!selectedClient"
                          >
                            <span class="material-icons-outlined mr-1 !text-xs">{{ school.isFavorite ? 'favorite' : 'favorite_border' }}</span>
                            <span class="!text-xs">{{ school.isFavorite ? '已收藏' : '收藏' }}</span>
                          </el-button>
                          <!-- 定校书操作按钮（当选择客户时显示） -->
                          <el-button
                            v-if="selectedClient"
                            :class="isInClientPrograms(school) ? '!text-green-600 hover:!text-green-700' : '!text-[#4F46E5] hover:!text-[#4338CA]'"
                            @click="toggleClientProgram(school)"
                            :disabled="addingToClientPrograms"
                            class="!h-7 !text-xs !rounded-md !px-2.5 transition-colors"
                          >
                            <span class="material-icons-outlined mr-1 !text-xs">{{ isInClientPrograms(school) ? 'check_circle' : 'add_circle' }}</span>
                            <span class="!text-xs">{{ isInClientPrograms(school) ? '已添加' : '添加到定校书' }}</span>
                          </el-button>
                        </div>
                      </div>

                      <!-- 详细信息区域 -->
                      <div 
                        class="details-container w-full overflow-hidden" 
                        :style="{ 
                          height: school.detailsHeight + 'px', 
                          opacity: school.showDetails ? 1 : 0,
                          transition: 'height 0.4s ease, opacity 0.4s ease',
                          transitionDelay: school.showDetails ? '0s' : '0s'
                        }"
                      >
                        <div class="details-content p-4 bg-gray-50 border-t border-gray-100">
                          <!-- 详情信息，分类显示 -->
                          <div class="grid grid-cols-1 lg:grid-cols-2 gap-3">
                            <!-- 申请要求 -->
                            <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
                              <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                                <span class="material-icons-outlined text-gray-400 text-sm mr-1">assignment</span>
                                申请要求
                              </h4>
                              <p class="text-xs text-gray-600 leading-relaxed">{{ school.申请要求 }}</p>
                            </div>
                            
                            <!-- 语言要求 -->
                            <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
                              <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                                <span class="material-icons-outlined text-gray-400 text-sm mr-1">translate</span>
                                语言要求
                              </h4>
                              <p class="text-xs text-gray-600 leading-relaxed">{{ school.语言要求 }}</p>
                            </div>
                            
                            <!-- 项目培养目标 -->
                            <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm lg:col-span-2">
                              <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                                <span class="material-icons-outlined text-gray-400 text-sm mr-1">lightbulb</span>
                                培养目标
                              </h4>
                              <p class="text-xs text-gray-600 leading-relaxed">{{ school.培养目标 }}</p>
                            </div>
                            
                            <!-- 课程设置 -->
                            <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm lg:col-span-2">
                              <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                                <span class="material-icons-outlined text-gray-400 text-sm mr-1">school</span>
                                课程设置
                              </h4>
                              <p class="text-xs text-gray-600 leading-relaxed">{{ school.课程设置 }}</p>
                            </div>
                            
                            <!-- 推荐理由 / 匹配说明 -->
                            <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm lg:col-span-2">
                              <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                                <span class="material-icons-outlined text-gray-400 text-sm mr-1">thumb_up</span>
                                {{ currentResultMode === 'ai' ? '推荐理由' : '匹配说明' }}
                              </h4>
                              <p class="text-xs text-gray-600 leading-relaxed">{{ school.reason }}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 其他情况：有推荐数据但分组/模式不匹配时的处理 -->
                  <div v-else-if="filteredRecommendations.length > 0" class="space-y-4">
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                      <div class="flex items-center text-sm text-yellow-800">
                        <span class="material-icons-outlined text-yellow-600 text-base mr-2">info</span>
                        当前显示的是{{ currentResultMode === 'ai' ? 'AI智能匹配' : 
                                     currentResultMode === 'hard_filter' ? '专业库匹配' : '匹配' }}结果，
                        点击"开始匹配"获取最新的{{ enableAiSelection ? 'AI智能匹配' : '专业库匹配' }}结果
                      </div>
                    </div>
                    
                    <!-- 简单列表显示 -->
                    <div class="space-y-3">
                      <div 
                        v-for="(school, index) in filteredRecommendations" 
                        :key="index"
                        class="border border-gray-200 rounded-lg p-3 bg-white"
                      >
                        <div class="flex items-center justify-between">
                          <div>
                            <h4 class="text-sm font-medium text-gray-800">{{ school.学校中文名 }}</h4>
                            <p class="text-xs text-gray-500">{{ school.专业中文名 }}</p>
                          </div>
                          <div class="text-xs text-gray-400">
                            {{ currentResultMode === 'ai' ? 'AI推荐数据' : 
                               currentResultMode === 'hard_filter' ? '专业库匹配数据' : '推荐数据' }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 定校书悬浮框 -->
            <div 
              v-if="selectedClient && clientPrograms.size > 0" 
              class="fixed bottom-6 right-6 w-80 bg-white border border-gray-200 rounded-xl shadow-lg z-50 client-programs-float"
            >
              <!-- 悬浮框头部 -->
              <div class="px-4 py-3 bg-gradient-to-r from-[#4F46E5]/5 to-[#7C3AED]/5 border-b border-gray-100 rounded-t-xl">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full bg-[#4F46E5] text-white flex items-center justify-center text-xs font-medium mr-3">
                      {{ selectedClient.name?.charAt(0)?.toUpperCase() || '?' }}
                    </div>
                    <div>
                      <h3 class="text-sm font-semibold text-gray-800">{{ selectedClient.name }}的定校书</h3>
                      <p class="text-xs text-gray-500">{{ clientPrograms.size }} 个院校项目</p>
                    </div>
                  </div>
                  <button 
                    @click="toggleClientProgramsFloat"
                    class="text-gray-500 hover:text-gray-700 p-1 rounded-md hover:bg-gray-100 transition-all duration-300 hover:scale-110"
                  >
                    <span 
                      class="material-icons-outlined text-sm transition-transform duration-300 ease-in-out"
                      :class="{ 'rotate-180': !showClientProgramsFloat }"
                    >
                      expand_less
                    </span>
                  </button>
                </div>
              </div>

              <!-- 悬浮框内容 -->
              <div 
                class="overflow-hidden transition-all duration-500 ease-in-out transform-gpu"
                :class="{
                  'float-content-expanded': showClientProgramsFloat,
                  'float-content-collapsed': !showClientProgramsFloat
                }"
              >
                <div class="max-h-80 overflow-y-auto">
                  <div class="p-4 space-y-3">
                    <div 
                      v-for="(program, index) in clientProgramsList" 
                      :key="program.id"
                      class="flex items-start justify-between p-3 bg-gray-50 rounded-lg border border-gray-100 hover:bg-gray-100 transition-all duration-300 hover:shadow-md"
                      :style="{ 
                        animation: showClientProgramsFloat ? `slide-in-item 0.4s ease-out ${index * 0.1}s both` : 'none' 
                      }"
                    >
                      <div class="flex items-start flex-1 min-w-0">
                        <!-- 学校Logo -->
                        <div class="flex-shrink-0 mr-3">
                          <img 
                            :src="getSchoolLogo({ 学校中文名: program.school_name_cn, 学校英文名: program.school_name_en, 项目官网: program.program_website })"
                            :alt="program.school_name_cn"
                            class="w-10 h-10 rounded-lg object-cover border border-gray-200 bg-white"
                            @error="handleImageError($event, { 学校中文名: program.school_name_cn, 学校英文名: program.school_name_en })"
                          />
                        </div>
                        
                        <!-- 项目信息 -->
                        <div class="flex-1 min-w-0">
                          <div class="flex items-center mb-1">
                            <h4 class="text-sm font-medium text-gray-800 truncate">{{ program.school_name_cn }}</h4>
                            <span v-if="program.school_qs_rank" class="ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full flex-shrink-0">
                              QS #{{ program.school_qs_rank }}
                            </span>
                          </div>
                          <p class="text-xs text-gray-600 truncate mb-1">{{ program.program_name_cn }}</p>
                          <div class="flex items-center space-x-2">
                            <span class="text-xs text-gray-500 flex items-center">
                              <span class="material-icons-outlined text-gray-400 text-xs mr-1">location_on</span>
                              {{ program.school_region || '未知地区' }}
                            </span>
                            <span class="text-xs text-gray-500 flex items-center">
                              <span class="material-icons-outlined text-gray-400 text-xs mr-1">school</span>
                              {{ program.degree || '未知学位' }}
                            </span>
                          </div>
                        </div>
                      </div>
                      <button 
                        @click="removeFromClientProgramsFloat(program)"
                        :class="[
                          'p-1 rounded-md transition-all duration-300 ml-2 flex-shrink-0',
                          removingFromClientPrograms.has(program.program_id) 
                            ? 'text-gray-400 cursor-not-allowed' 
                            : 'text-red-500 hover:text-red-600 hover:bg-red-50 hover:scale-110'
                        ]"
                        :disabled="removingFromClientPrograms.has(program.program_id)"
                      >
                        <span v-if="!removingFromClientPrograms.has(program.program_id)" class="material-icons-outlined text-sm">delete_outline</span>
                        <svg v-else class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </button>
                    </div>

                    <!-- 空状态 -->
                    <div v-if="clientProgramsList.length === 0" class="text-center py-4">
                      <span class="material-icons-outlined text-gray-400 text-2xl mb-2">school</span>
                      <p class="text-sm text-gray-500">暂无定校书项目</p>
                    </div>
                  </div>
                </div>

                <!-- 悬浮框底部操作 -->
                <div class="px-4 py-3 bg-gray-50 border-t border-gray-100 rounded-b-xl">
                  <div class="flex items-center justify-between">
                    <span class="text-xs text-gray-500">
                      快速查看和管理定校书
                    </span>
                    <router-link 
                      :to="`/clients/${selectedClient.id_hashed}?tab=schools`"
                      class="text-[#4F46E5] hover:text-[#4338CA] text-xs font-medium flex items-center transition-all duration-300 hover:translate-x-1 group"
                    >
                      查看完整定校书
                      <span class="material-icons-outlined text-xs ml-1 transition-transform duration-300 group-hover:translate-x-1">arrow_forward</span>
                    </router-link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import AnimatedInput from '@/components/common/AnimatedInput.vue';
import { getHomeSchoolList } from '@/api/programs';
// 导入客户相关API
import { getClientList, searchClients, addClientProgram, removeClientProgram, getClientPrograms, getClientById } from '@/api/client';

/**
 * 选校匹配助手组件
 * 
 * 该组件提供了一个交互式界面，让教育工作者输入学生信息，
 * 获取个性化的院校匹配方案，辅助教育工作者为学生提供精准的留学规划指导。
 * 
 * 主要功能：
 * 1. 多步骤表单收集学生关键信息（基本信息、学术背景、留学意向、软实力）
 * 2. 基于学生数据生成个性化院校匹配方案
 * 3. 匹配结果可筛选和排序
 * 4. 展示院校匹配度和推荐理由
 */

// 创建响应式状态
const isLoading = ref(false);
const activeTab = ref('basic'); // 控制表单标签页
const sortBy = ref('rank-asc'); // 排序方式，默认为推荐排名

// AI选校开关
const enableAiSelection = ref(false);

// 响应式布局状态
const sidebarCollapsed = ref(false);
const isLargeScreen = ref(false);
const isExtraLargeScreen = ref(false);

// 检查屏幕尺寸
const checkScreenSize = () => {
  const width = window.innerWidth;
  isLargeScreen.value = width >= 1024; // lg断点
  isExtraLargeScreen.value = width >= 1280; // xl断点
  
  // 在xl以下的大屏幕上默认收起侧边栏以节省空间
  if (isLargeScreen.value && !isExtraLargeScreen.value && !sidebarCollapsed.value) {
    sidebarCollapsed.value = true;
  }
  // 在xl及以上或小屏幕上展开侧边栏
  else if (!isLargeScreen.value || isExtraLargeScreen.value) {
    sidebarCollapsed.value = false;
  }
};

// 切换侧边栏状态
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
};

// 表单折叠状态控制
const sectionStates = reactive({
  client: true,    // 默认展开客户档案
  academic: true,  // 默认展开学术背景
  intention: true, // 默认展开留学意向
  strength: false  // 默认折叠软实力与偏好
});

// 表单完成状态追踪
const sectionCompletion = reactive({
  academic: false,
  intention: false,
  strength: false  // 软实力部分现在也需要检查，默认未完成
});

const userHasInteractedWithSection = reactive({
  academic: false,
  intention: false,
  strength: false
});

// 表单验证状态
const formValidation = reactive({
  errors: {
    academic: {
      school: false,
      major: false,
      gpa: false,
      gpaScale: false
    },
    intention: {
      countries: false,
      majors: false
    }
  },
  hasValidated: false
});

// 切换表单区域展开/折叠状态
const toggleSection = (section) => {
  const originallyCollapsed = !sectionStates[section];
  sectionStates[section] = !sectionStates[section];

  // 如果用户手动展开了一个原本是折叠的区域
  if (sectionStates[section] && originallyCollapsed) {
    userHasInteractedWithSection[section] = true;
  }
  // 如果用户手动折叠，则重置交互状态，允许后续的自动折叠/展开
  if (!sectionStates[section]) {
    userHasInteractedWithSection[section] = false;
  }
};

// 检查表单区域完成状态（仅用于状态记录，不自动收缩）
const checkSectionCompletion = (section) => {
  if (section === 'academic') {
    // 安全检查：确保 profileForm 和 profileForm.academic 存在
    if (!profileForm || !profileForm.academic) {
      console.warn('profileForm.academic 不存在，跳过完成状态检查');
      return;
    }
    
    const { school, major, gpa, gpaScale } = profileForm.academic;
    const isComplete = !!school && !!major && !!gpa && !!gpaScale;
    sectionCompletion.academic = isComplete;
    
    // 实时清除错误状态 - 每个字段独立验证
    if (formValidation.hasValidated) {
      formValidation.errors.academic.school = !school;
      formValidation.errors.academic.major = !major;
      formValidation.errors.academic.gpa = !gpa;
      formValidation.errors.academic.gpaScale = !gpaScale;
    }
  } else if (section === 'intention') {
    // 安全检查：确保 profileForm 和 profileForm.intention 存在
    if (!profileForm || !profileForm.intention) {
      console.warn('profileForm.intention 不存在，跳过完成状态检查');
      return;
    }
    
    const { countries, majors } = profileForm.intention;
    const isComplete = countries.length > 0 && majors.length > 0;
    sectionCompletion.intention = isComplete;
    
    // 实时清除错误状态 - 每个字段独立验证
    if (formValidation.hasValidated) {
      formValidation.errors.intention.countries = !countries.length;
      formValidation.errors.intention.majors = !majors.length;
    }
  } else if (section === 'strength') {
    // 安全检查：确保 profileForm 和 profileForm.strength 存在
    if (!profileForm || !profileForm.strength) {
      console.warn('profileForm.strength 不存在，跳过完成状态检查');
      return;
    }
    
    const { competition, internship, research, rankingPreference, preferenceType } = profileForm.strength;
    // 软实力部分都是可选的，只要有任何一项填写就算完成
    const hasAnyValue = 
      (Array.isArray(competition) ? competition.length > 0 : !!competition) ||
      (Array.isArray(internship) ? internship.length > 0 : !!internship) ||
      (Array.isArray(research) ? research.length > 0 : !!research) ||
      (Array.isArray(rankingPreference) ? rankingPreference.length > 0 : !!rankingPreference) ||
      !!preferenceType;
    sectionCompletion.strength = hasAnyValue;
  }
};

// 添加单个字段验证清除函数
const clearFieldError = (section, field) => {
  if (formValidation.hasValidated && formValidation.errors[section] && formValidation.errors[section][field] !== undefined) {
    const value = profileForm[section][field];
    if (Array.isArray(value)) {
      // 对于数组类型（如多选），只要有值就清除错误
      formValidation.errors[section][field] = value.length === 0;
    } else {
      // 对于普通字段，只要有值就清除错误（包括字符串、数字等）
      formValidation.errors[section][field] = !value || value === '' || value === null || value === undefined;
    }
  }
};

// 表单标签页配置
const tabs = [
  { key: 'basic', name: '基本信息' },
  { key: 'academic', name: '学术背景' },
  { key: 'intention', name: '留学意向' },
  { key: 'strength', name: '软实力与偏好' },
];

// 表单数据
const profileForm = reactive({
  // 学术背景
  academic: {
    education: 'master', // 默认设置为硕士
    school: '',
    major: '',
    gpa: '',
    gpaScale: '100', // 默认设置为百分制
    ranking: '',
  },
  // 留学意向
  intention: {
    countries: [],
    majors: [],
    duration: '', // 隐藏字段，默认为空（不限）
  },
  // 软实力与偏好
  strength: {
    research: '',
    internship: '',
    papers: '',
    rankingPreference: [],
    preferenceType: '' // 可选项，默认为空（系统会使用平衡型）
  }
});

// 匹配结果
const recommendations = ref([]);
const hasSubmitted = ref(false);
const currentResultMode = ref(''); // 记录当前结果的实际来源模式：'ai' 或 'hard_filter'

// 专业库匹配模式的学校分组数据
const schoolGroups = ref([]);
const programsContentRefs = ref({});

// 学校Logo缓存
const schoolLogosCache = ref(new Map());

// GPA验证状态
const gpaValidationError = ref(false);
const gpaErrorMessage = ref('');

// 客户档案相关状态
const selectedClient = ref(null);
const showClientSelector = ref(false);
const clientSearchQuery = ref('');
const clientSearchResults = ref([]);
const clientSearchLoading = ref(false);
const clientPrograms = ref(new Set()); // 存储客户已有的专业ID集合
const clientDropdown = ref(null);
const dropdownStyle = ref({});
const defaultClientList = ref([]); // 缓存默认客户列表

// 定校书悬浮框相关状态
const showClientProgramsFloat = ref(true); // 悬浮框展开状态，默认展开
const clientProgramsList = ref([]); // 客户定校书程序列表
const removingFromClientPrograms = ref(new Set()); // 正在删除的项目ID集合

// 从悬浮框中删除定校书项目
const removeFromClientProgramsFloat = async (program) => {
  if (!selectedClient.value || removingFromClientPrograms.value.has(program.program_id)) return;
  
  try {
    removingFromClientPrograms.value.add(program.program_id);
    
    await removeClientProgram(selectedClient.value.id_hashed, program.program_id);
    
    // 从本地状态中移除
    clientPrograms.value.delete(program.program_id);
    clientProgramsList.value = clientProgramsList.value.filter(p => p.program_id !== program.program_id);
    
    ElMessage.success(`已从${selectedClient.value.name}的定校书中移除该专业`);
  } catch (error) {
    console.error('从定校书中删除项目失败:', error);
    ElMessage.error('删除失败，请重试');
  } finally {
    removingFromClientPrograms.value.delete(program.program_id);
  }
};

// 处理图片加载失败
const handleImageError = (event, school) => {
  // 当图片加载失败时，替换为文字logo
  const colors = [
    { bg: '4F46E5', color: 'FFFFFF' }, // 紫色
    { bg: '059669', color: 'FFFFFF' }, // 绿色
    { bg: 'DC2626', color: 'FFFFFF' }, // 红色
    { bg: '2563EB', color: 'FFFFFF' }, // 蓝色
    { bg: 'F59E0B', color: 'FFFFFF' }, // 橙色
    { bg: '7C2D12', color: 'FFFFFF' }, // 棕色
    { bg: '581C87', color: 'FFFFFF' }, // 深紫色
    { bg: '0F766E', color: 'FFFFFF' }  // 青色
  ];
  
  const schoolNameCn = school.学校中文名;
  const firstChar = schoolNameCn ? schoolNameCn.charAt(0) : '?';
  
  // 根据学校名称生成一致的颜色
  const colorIndex = schoolNameCn ? schoolNameCn.charCodeAt(0) % colors.length : 0;
  const selectedColor = colors[colorIndex];
  
  // 生成SVG文字logo
  const textLogo = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(`
    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">
      <rect width="40" height="40" fill="#${selectedColor.bg}" rx="8"/>
      <text x="20" y="26" font-family="system-ui, -apple-system, sans-serif" font-size="16" font-weight="600" text-anchor="middle" fill="#${selectedColor.color}">
        ${firstChar}
      </text>
    </svg>
  `)}`;
  
  event.target.src = textLogo;
};

// 计算下拉框位置
const updateDropdownPosition = () => {
  nextTick(() => {
    const searchInput = document.querySelector('.client-selector .animated-input-wrapper');
    if (searchInput && showClientSelector.value) {
      const rect = searchInput.getBoundingClientRect();
      dropdownStyle.value = {
        position: 'fixed',
        top: `${rect.bottom + 4}px`,
        left: `${rect.left}px`,
        width: `${rect.width}px`,
        minWidth: '320px'
      };
    }
  });
};

/**
 * 从数据库获取学校Logo URL的方法
 */
const fetchSchoolLogoFromDB = async (schoolNameCn) => {
  try {
    // 检查缓存
    if (schoolLogosCache.value.has(schoolNameCn)) {
      return schoolLogosCache.value.get(schoolNameCn);
    }

    // 调用后端API获取学校logo信息
    const response = await fetch(`/api/ai-selection/data/abroad-schools?school_name=${encodeURIComponent(schoolNameCn)}&limit=1`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache',
      }
    });

    if (!response.ok) {
      return null;
    }

    const schools = await response.json();
    const school = schools.find(s => s.school_name_cn === schoolNameCn);
    
    if (school && school.school_logo_url) {
      // 缓存结果
      schoolLogosCache.value.set(schoolNameCn, school.school_logo_url);
      return school.school_logo_url;
    }

    return null;
  } catch (error) {
    console.warn(`获取学校logo失败: ${schoolNameCn}`, error);
    return null;
  }
};

/**
 * 批量预加载学校Logo
 */
const preloadSchoolLogos = async (schools) => {
  // 提取所有唯一的学校中文名
  const schoolNames = [...new Set(schools.map(school => school.学校中文名).filter(Boolean))];
  
  // 并发获取logo URLs
  const logoPromises = schoolNames.map(async (schoolName) => {
    try {
      const logoUrl = await fetchSchoolLogoFromDB(schoolName);
      return { schoolName, logoUrl };
    } catch (error) {
      console.warn(`预加载logo失败: ${schoolName}`, error);
      return { schoolName, logoUrl: null };
    }
  });

  const results = await Promise.allSettled(logoPromises);
  
  // 更新缓存
  results.forEach((result) => {
    if (result.status === 'fulfilled' && result.value.logoUrl) {
      schoolLogosCache.value.set(result.value.schoolName, result.value.logoUrl);
    }
  });
};

/**
 * 获取学校Logo的方法（新版本：优先使用数据库logo_url）
 */
const getSchoolLogo = (school) => {
  const schoolNameCn = school.学校中文名;
  
  // 方案1: 优先使用数据库中的logo URL（第一优先级）
  if (schoolNameCn && schoolLogosCache.value.has(schoolNameCn)) {
    const logoUrl = schoolLogosCache.value.get(schoolNameCn);
    if (logoUrl && logoUrl.trim() !== '') {
      return logoUrl;
    }
  }

  // 方案2: 使用学校官方域名获取favicon（保留原有逻辑作为fallback）
  const domainMapping = {
    '麻省理工学院': 'mit.edu',
    '新加坡国立大学': 'nus.edu.sg',
    '香港大学': 'hku.hk',
    '帝国理工学院': 'imperial.ac.uk',
    '伦敦大学学院': 'ucl.ac.uk',
    '哈佛大学': 'harvard.edu',
    '斯坦福大学': 'stanford.edu',
    '剑桥大学': 'cam.ac.uk',
    '牛津大学': 'ox.ac.uk',
    '清华大学': 'tsinghua.edu.cn',
    '北京大学': 'pku.edu.cn',
    '复旦大学': 'fudan.edu.cn',
    '上海交通大学': 'sjtu.edu.cn',
    '浙江大学': 'zju.edu.cn',
    '南京大学': 'nju.edu.cn',
    '中国人民大学': 'ruc.edu.cn',
    '北京航空航天大学': 'buaa.edu.cn',
    '同济大学': 'tongji.edu.cn',
    '天津大学': 'tju.edu.cn',
    '华中科技大学': 'hust.edu.cn',
    '西安交通大学': 'xjtu.edu.cn',
    '中山大学': 'sysu.edu.cn',
    '哈尔滨工业大学': 'hit.edu.cn',
    '武汉大学': 'whu.edu.cn',
    '东南大学': 'seu.edu.cn',
    '中南大学': 'csu.edu.cn',
    '大连理工大学': 'dlut.edu.cn',
    '北京理工大学': 'bit.edu.cn',
    '厦门大学': 'xmu.edu.cn'
  };

  // 首先检查是否有预定义的域名映射
  if (domainMapping[school.学校中文名]) {
    const domain = domainMapping[school.学校中文名];
    // 使用Google的favicon服务，更可靠
    return `https://www.google.com/s2/favicons?domain=${domain}&sz=128`;
  }

  // 方案3: 尝试从项目官网获取favicon
  if (school.项目官网) {
    try {
      const url = new URL(school.项目官网);
      const domain = url.hostname.replace('www.', '');
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=128`;
    } catch (e) {
      console.warn(`Invalid URL for ${school.学校中文名}: ${school.项目官网}`);
    }
  }

  // 方案4: 根据学校英文名尝试推断域名
  const englishName = school.学校英文名 || '';
  const englishDomainMapping = {
    'Massachusetts Institute of Technology': 'mit.edu',
    'MIT': 'mit.edu',
    'Stanford University': 'stanford.edu',
    'University of Oxford': 'ox.ac.uk',
    'University of Cambridge': 'cam.ac.uk',
    'Imperial College London': 'imperial.ac.uk',
    'University College London': 'ucl.ac.uk',
    'National University of Singapore': 'nus.edu.sg',
    'The University of Hong Kong': 'hku.hk',
    'Chinese University of Hong Kong': 'cuhk.edu.hk',
    'Nanyang Technological University': 'ntu.edu.sg',
    'University of Toronto': 'utoronto.ca',
    'University of Melbourne': 'unimelb.edu.au',
    'Harvard University': 'harvard.edu',
    'Yale University': 'yale.edu',
    'Princeton University': 'princeton.edu',
    'California Institute of Technology': 'caltech.edu',
    'University of Chicago': 'uchicago.edu',
    'Columbia University': 'columbia.edu',
    'University of Pennsylvania': 'upenn.edu'
  };

  for (const [name, domain] of Object.entries(englishDomainMapping)) {
    if (englishName.toLowerCase().includes(name.toLowerCase()) || 
        englishName.toLowerCase() === name.toLowerCase()) {
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=128`;
    }
  }

  // 方案5: 生成美观的文字Logo（最终fallback）
  const colors = [
    { bg: '4F46E5', color: 'FFFFFF' }, // 紫色
    { bg: '059669', color: 'FFFFFF' }, // 绿色
    { bg: 'DC2626', color: 'FFFFFF' }, // 红色
    { bg: '2563EB', color: 'FFFFFF' }, // 蓝色
    { bg: 'F59E0B', color: 'FFFFFF' }, // 橙色
    { bg: '7C2D12', color: 'FFFFFF' }, // 棕色
    { bg: '581C87', color: 'FFFFFF' }, // 深紫色
    { bg: '0F766E', color: 'FFFFFF' }  // 青色
  ];
  
  // 使用学校名称的第一个字符来选择颜色
  const firstChar = (school.学校中文名 || school.学校英文名 || 'U').charAt(0);
  const colorIndex = firstChar.charCodeAt(0) % colors.length;
  const { bg, color } = colors[colorIndex];
  
  // 使用学校名称的前两个字符（如果是中文）或第一个字符（如果是英文）
  let initials = school.学校中文名 || school.学校英文名|| 'U';
  if (/[\u4e00-\u9fa5]/.test(initials)) {
    // 中文：取前两个字符
    initials = initials.substring(0, 2);
  } else {
    // 英文：取第一个字符
    initials = initials.charAt(0).toUpperCase();
  }
  
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=${bg}&color=${color}&size=128&font-size=0.6&bold=true&format=svg`;
};

/**
 * 模拟数据
 * 注意: 在实际项目中，这些数据应从API获取
 */

// 模拟数据已删除
const mockRecommendations = [];

// 搜索建议 - 境内院校（使用home-schools接口）
const querySchools = async (queryString, callback) => {
  try {
    // 如果没有输入内容，不显示任何下拉选项
    if (!queryString || !queryString.trim()) {
      callback([]);
      return;
    }
    
    // 学校简称到全称的映射表
    const schoolAliasMap = {
      '北大': '北京大学',
      '清华': '清华大学',
      '南大': '南京大学',
      '复旦': '复旦大学',
      '上交': '上海交通大学',
      '浙大': '浙江大学',
      '中大': '中山大学',
      '华科': '华中科技大学',
      '哈工大': '哈尔滨工业大学',
      '西交': '西安交通大学',
      '同济': '同济大学',
      '北航': '北京航空航天大学',
      '北理工': '北京理工大学',
      '东大': '东南大学',
      '天大': '天津大学',
      '大工': '大连理工大学',
      '西工大': '西北工业大学',
      '中南': '中南大学',
      '华南理工': '华南理工大学',
      '电子科大': '电子科技大学',
      '重大': '重庆大学',
      '兰大': '兰州大学',
      '厦大': '厦门大学',
      '武大': '武汉大学',
      '人大': '中国人民大学',
      '师大': '北京师范大学',
      '农大': '中国农业大学',
      '北外': '北京外国语大学',
      '上外': '上海外国语大学',
      '央财': '中央财经大学',
      '上财': '上海财经大学',
      '对外经贸': '对外经济贸易大学',
      '华东师大': '华东师范大学',
      '华中师大': '华中师范大学',
      '东北师大': '东北师范大学',
      '南开': '南开大学',
      '吉大': '吉林大学',
      '山大': '山东大学',
      '川大': '四川大学',
      '中科大': '中国科学技术大学'
    };
    
    const searchTerm = queryString.trim();
    const searchLower = searchTerm.toLowerCase();
    
    // 检查是否为简称，如果是则获取对应的全称
    const fullName = schoolAliasMap[searchTerm];
    
    // 如果有输入内容，使用搜索关键词查询
    // 如果是简称，同时搜索简称和全称以确保能找到目标学校
    const searchKeyword = fullName || searchTerm;
    const response = await getHomeSchoolList({ search: searchKeyword, limit: 2000 });
    let schools = response.data || response;
    
    // 前端二次筛选和排序：提高匹配精度
    
    // 首先筛选出匹配的学校
    schools = schools.filter(school => {
      const schoolName = school.school_name || '';
      const schoolNameLower = schoolName.toLowerCase();
      
      // 完全匹配简称对应的全称
      if (fullName && schoolName === fullName) return true;
      
      // 模糊匹配
      return schoolNameLower.includes(searchLower);
    });
    

    
    // 然后排序
    schools.sort((a, b) => {
      const schoolName = a.school_name || '';
      const schoolNameLower = schoolName.toLowerCase();
      
      const schoolNameB = b.school_name || '';
      const schoolNameLowerB = schoolNameB.toLowerCase();
      
      // 第一优先级：完全匹配简称对应的全称
      if (fullName) {
        const aExactMatch = schoolName === fullName;
        const bExactMatch = schoolNameB === fullName;
        
        // 备用匹配：如果完全匹配失败，尝试部分匹配
        const aPartialMatch = !aExactMatch && schoolName.includes(fullName.slice(0, 3));
        const bPartialMatch = !bExactMatch && schoolNameB.includes(fullName.slice(0, 3));
        
        // 优先完全匹配
        if (aExactMatch && !bExactMatch) return -1;
        if (!aExactMatch && bExactMatch) return 1;
        
        // 然后是部分匹配
        if (aPartialMatch && !bPartialMatch) return -1;
        if (!aPartialMatch && bPartialMatch) return 1;
        

      }
      
      // 第二优先级：开头匹配原始搜索词
      const aStartsMatch = schoolNameLower.startsWith(searchLower);
      const bStartsMatch = schoolNameLowerB.startsWith(searchLower);
      if (aStartsMatch && !bStartsMatch) return -1;
      if (!aStartsMatch && bStartsMatch) return 1;
      
      // 第三优先级：包含匹配
      const aContainsMatch = schoolNameLower.includes(searchLower);
      const bContainsMatch = schoolNameLowerB.includes(searchLower);
      if (aContainsMatch && !bContainsMatch) return -1;
      if (!aContainsMatch && bContainsMatch) return 1;
      
      // 第四优先级：按软科排名排序
      const rankA = a.ranking_ruanke || 9999;
      const rankB = b.ranking_ruanke || 9999;
      return rankA - rankB;
    });
    
    // 限制搜索结果数量
    schools = schools.slice(0, 15);
    
    // 格式化返回结果
    const results = schools.map(school => ({
      value: school.school_name,
      label: school.school_name,
      ranking: school.ranking_ruanke,
      location: school.location
    }));
    
    callback(results);
  } catch (error) {
    console.error("Error fetching home schools:", error);
    callback([]);
  }
};

/* 
// 原国外院校搜索逻辑（已注释）
const querySchoolsInternational = async (queryString, callback) => {
  try {
    let schools = [];
    
    if (!queryString) {
      // 如果没有输入内容，获取所有学校并按QS排名排序显示热门学校
      const response = await fetch(`/api/ai-selection/data/schools`);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      schools = await response.json();
      
      // 按QS排名排序显示热门学校
      schools = schools
        .filter(school => school.school_qs_rank) // 只显示有排名的学校
        .sort((a, b) => {
          const rankA = parseInt(a.school_qs_rank) || 9999;
          const rankB = parseInt(b.school_qs_rank) || 9999;
          return rankA - rankB;
        })
        .slice(0, 15); // 限制显示15个热门学校
    } else {
      // 如果有输入内容，使用地区搜索 + 前端筛选的策略
      const searchTerm = queryString.trim();
      
      // 使用并行搜索策略：全量获取 + 地区筛选
      const searchPromises = [
        // 1. 获取所有学校
        fetch(`/api/ai-selection/data/schools`)
      ];
      
      // 2. 对于地区性搜索（如"香港"），也尝试按地区精确搜索
      if (['香港', '北京', '上海', '广州', '深圳', '新加坡', '伦敦'].includes(searchTerm)) {
        searchPromises.push(
          fetch(`/api/ai-selection/data/schools?region=${encodeURIComponent(searchTerm)}`)
        );
      }
      
      // 执行所有搜索
      const responses = await Promise.all(searchPromises);
      
      // 合并所有搜索结果
      const allSchools = [];
      for (const response of responses) {
        if (response.ok) {
          const data = await response.json();
          allSchools.push(...data);
        }
      }
      
      // 去重：基于school_name_cn
      const uniqueSchools = allSchools.filter((school, index, self) => 
        index === self.findIndex(s => s.school_name_cn === school.school_name_cn)
      );
      
      schools = uniqueSchools;
      
      // 前端筛选：支持中英文模糊匹配
      const searchLower = searchTerm.toLowerCase();
      schools = schools.filter(school => {
        const cnMatch = school.school_name_cn && school.school_name_cn.toLowerCase().includes(searchLower);
        const enMatch = school.school_name_en && school.school_name_en.toLowerCase().includes(searchLower);
        
        // 特殊处理香港学校的英文名称变体
        let hongkongMatch = false;
        if (searchLower.includes('hong') && school.school_name_en) {
          const schoolNameLower = school.school_name_en.toLowerCase();
          hongkongMatch = schoolNameLower.includes('hong kong') || 
                         schoolNameLower.includes('hongkong') ||
                         schoolNameLower.includes('hong');
        }
        
        return cnMatch || enMatch || hongkongMatch;
      });
      
      // 按匹配度排序：优先显示开头匹配的结果
      schools.sort((a, b) => {
        const aStartsCn = a.school_name_cn && a.school_name_cn.toLowerCase().startsWith(searchLower);
        const bStartsCn = b.school_name_cn && b.school_name_cn.toLowerCase().startsWith(searchLower);
        const aStartsEn = a.school_name_en && a.school_name_en.toLowerCase().startsWith(searchLower);
        const bStartsEn = b.school_name_en && b.school_name_en.toLowerCase().startsWith(searchLower);
        
        // 优先级：中文开头匹配 > 英文开头匹配 > 中文包含匹配 > 英文包含匹配
        if (aStartsCn && !bStartsCn) return -1;
        if (!aStartsCn && bStartsCn) return 1;
        if (aStartsEn && !bStartsEn) return -1;
        if (!aStartsEn && bStartsEn) return 1;
        
        // 如果都是包含匹配，则按QS排名排序
        const rankA = parseInt(a.school_qs_rank) || 9999;
        const rankB = parseInt(b.school_qs_rank) || 9999;
        return rankA - rankB;
      });
    }
    
    // 格式化返回结果
    const results = schools.map(school => ({
      value: school.school_name_cn,
      label: school.school_name_cn,
      secondaryText: school.school_name_en,
      ranking: school.school_qs_rank
    }));
    
    callback(results);
  } catch (error) {
    console.error("Error fetching schools:", error);
    callback([]);
  }
};
*/



/**
 * 方法定义
 */

// SSE相关状态
const sseConnection = ref(null);
const progressLogs = ref([]);
const currentPreviews = ref([]);
const streamingStatus = ref(''); // 'connecting', 'streaming', 'completed', 'error'
const totalElapsed = ref(0);
const currentStage = ref('');

// 进度条相关状态
const recommendationProgress = ref(0);
const progressMessage = ref('');

// 获取匹配结果的方法 (使用SSE流式接口)
const getRecommendations = async () => {
  isLoading.value = true;
  streamingStatus.value = 'connecting';
  progressLogs.value = [];
  currentPreviews.value = [];
  recommendations.value = [];
  schoolGroups.value = []; // 清空分组数据
  currentResultMode.value = ''; // 重置结果模式
  totalElapsed.value = 0;
  currentStage.value = '';
  recommendationProgress.value = 0; // 重置进度条
  progressMessage.value = '正在连接匹配系统';
  
  // 构建符合API要求的请求体
  const requestData = {
    academic: {
      education: profileForm.academic.education,
      school: profileForm.academic.school,
      major: profileForm.academic.major,
      gpa: profileForm.academic.gpa || "0",  // 保持字符串格式
      gpaScale: profileForm.academic.gpaScale,  // 使用camelCase匹配后端
      ranking: profileForm.academic.ranking || null
    },
    intention: {
      countries: Array.isArray(profileForm.intention.countries) ? profileForm.intention.countries : [],
      majors: Array.isArray(profileForm.intention.majors) ? profileForm.intention.majors : [],
      duration: ""  // 隐藏字段，固定传空字符串（不限）
    },
    strength: {
      competition: Array.isArray(profileForm.strength.competition) ? profileForm.strength.competition : [],
      internship: Array.isArray(profileForm.strength.internship) ? profileForm.strength.internship : [],
      research: Array.isArray(profileForm.strength.research) ? profileForm.strength.research : [],
      rankingPreference: Array.isArray(profileForm.strength.rankingPreference) ? profileForm.strength.rankingPreference : [],
      preference_type: profileForm.strength.preferenceType || "balanced",  // 如果用户未选择则使用默认值平衡型
      enable_ai_selection: enableAiSelection.value
    }
  };

  // 清理空值和无效数据
  Object.keys(requestData.academic).forEach(key => {
    if (requestData.academic[key] === '' || requestData.academic[key] === null) {
      delete requestData.academic[key];
    }
  });

  // 建立SSE连接
  const apiUrl = '/api/ai-selection/recommendation/recommend/stream';
  
  try {
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API错误响应:', errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }
    
    streamingStatus.value = 'streaming';
    recommendationProgress.value = 8; // 连接成功，进度8%
    progressMessage.value = '已连接匹配服务，开始分析';
    
    // 处理流式响应
    const reader = response.body.getReader();
    const decoder = new TextDecoder('utf-8');
    let buffer = '';

    const processBuffer = async () => {
      const lines = buffer.split('\n');
      buffer = lines.pop() || ''; // 保留最后一个可能不完整的行
      
      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith('data: ')) {
          const dataStr = trimmedLine.slice(6).trim();
          if (dataStr && dataStr !== '[DONE]') {
            try {
              const eventData = JSON.parse(dataStr);
              await handleSSEEvent(eventData);
            } catch (e) {
              console.warn('解析SSE事件失败:', e, dataStr);
            }
          }
        }
      }
    };

    const readStream = async () => {
      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) {
            // 处理缓冲区中剩余的数据
            if (buffer.trim()) {
              await processBuffer();
            }
            break;
          }
          
          buffer += decoder.decode(value, { stream: true });
          await processBuffer();
        }
      } catch (streamError) {
        console.error('流读取错误:', streamError);
        throw streamError;
      }
    };
    
    await readStream();
    
  } catch (error) {
    console.error('SSE连接失败:', error);
    streamingStatus.value = 'error';
    isLoading.value = false;
    recommendationProgress.value = 0;
          progressMessage.value = '匹配失败';
    
    let errorMessage = '匹配服务连接失败，请稍后重试';
    
    if (error.message.includes('HTTP 404')) {
      errorMessage = 'API端点不存在，请检查后端服务是否正确启动';
    } else if (error.message.includes('HTTP 422')) {
      errorMessage = '请求参数格式错误，请检查表单数据';
    } else if (error.message.includes('HTTP 4')) {
      errorMessage = '请求参数有误，请检查填写的信息';
    } else if (error.message.includes('HTTP 5')) {
      errorMessage = '服务器内部错误，请稍后重试';
    } else if (error.name === 'TypeError' || error.message.includes('Failed to fetch')) {
      errorMessage = '无法连接到后端服务，请确认后端是否启动';
    }
    
    console.error('详细错误信息:', error);
    
    ElMessage.error(errorMessage);
  } finally {
    if (streamingStatus.value === 'streaming') {
      streamingStatus.value = 'completed';
      isLoading.value = false;
      hasSubmitted.value = true;
    }
  }
};

// 处理SSE事件
const handleSSEEvent = async (eventData) => {
  const { type, message, elapsed, stage, preview_schools, recommendation, rank, total_elapsed, error } = eventData;
  
  switch (type) {
    case 'mode_selection':
      // 记录实际使用的推荐模式
      currentResultMode.value = eventData.mode === 'hard_filter' ? 'hard_filter' : 'ai';
      currentStage.value = eventData.mode === 'hard_filter' ? '专业库匹配模式' : 'AI智能匹配模式';
      progressMessage.value = eventData.message;
      recommendationProgress.value = 10;
      break;

    case 'start':
      recommendationProgress.value = 15;
      progressMessage.value = getProgressMessage('start');
      currentStage.value = '启动匹配引擎';
      break;
      
    case 'progress':
      const progressMap = {
        'profile': 25, 'matching': 45, 'scoring': 65, 'ranking': 80, 'reasoning': 92,
        'filtering': 35, 'analyzing': 20, 'preprocessing': 10, 'calculating': 70, 'finalizing': 95,
        'hard_filter_start': 15, 'filter_criteria': 25, 'hard_filter_complete': 90
      };
      if (stage && progressMap[stage]) recommendationProgress.value = progressMap[stage];
      if (stage) {
        currentStage.value = getStageDisplayName(stage);
        progressMessage.value = getProgressMessage(stage, eventData);
      }
      break;
      
    case 'recommendation_batch_start':
      recommendationProgress.value = 88;
      progressMessage.value = getProgressMessage('recommendation_batch_start');
      currentStage.value = '撰写专业分析';
      break;
      
    case 'recommendation_complete':
      if (recommendation && typeof rank === 'number') {
        handleRecommendationWithProgramQuery(recommendation, rank);
      }
      break;
      
    case 'recommendation_error':
      console.warn(`第${rank}个推荐生成失败: ${error || '未知错误'}`);
      break;
    
    case 'matching_start':
      recommendationProgress.value = 40;
      progressMessage.value = getProgressMessage('matching_start', eventData);
      currentStage.value = '深度评估专业';
      break;
      
    case 'matching_progress':
      const processed = eventData.processed || 0;
      const total = eventData.total || 20;
      recommendationProgress.value = Math.min(82, 40 + (processed / total) * 40);
      progressMessage.value = getProgressMessage('matching_progress', eventData);
      break;
      
    case 'ranking_start':
      recommendationProgress.value = 82;
      progressMessage.value = getProgressMessage('ranking_start');
      currentStage.value = '生成专属排名';
      break;
      
    case 'hard_filter_start':
      recommendationProgress.value = 15;
      progressMessage.value = '开始专业库匹配';
      currentStage.value = '专业库条件筛选';
      currentResultMode.value = 'hard_filter'; // 提前设置模式，确保监听器能正确工作
      break;

    case 'filter_criteria':
      recommendationProgress.value = 25;
      progressMessage.value = '应用筛选条件';
      currentStage.value = '条件匹配中';
      break;

    case 'hard_filter_batch':
              // 处理批量专业库匹配结果
      if (eventData.results && Array.isArray(eventData.results)) {
        
        // 并发获取完整的专业详情信息
        const enrichResults = await Promise.all(
          eventData.results.map(async (result) => {
            try {
              // 调用后端API获取完整专业信息
              const response = await fetch(`/api/ai-selection/data/programs/${result.program_id}`, {
                method: 'GET',
                headers: {
                  'Accept': 'application/json',
                  'Cache-Control': 'no-cache',
                }
              });

              if (!response.ok) {
                throw new Error(`获取专业详情失败: ${response.status}`);
              }

              const programDetail = await response.json();
              
                              // 使用数据库完整信息替换专业库匹配的不完整数据
              return {
                id: result.rank,
                rank: result.rank,
                program_id: result.program_id,
                
                // 学校信息（使用数据库完整数据）
                学校中文名: programDetail.school_name_cn,
                学校英文名: programDetail.school_name_en,
                
                // 专业信息（使用数据库完整数据）
                专业中文名: programDetail.program_name_cn,
                专业英文名: programDetail.program_name_en,
                专业大类: programDetail.program_category,
                专业方向: programDetail.program_direction,
                所在学院: programDetail.faculty,
                
                // 申请和学制信息（使用数据库完整数据）
                入学时间: programDetail.enrollment_time,
                项目时长: programDetail.program_duration,
                项目官网: programDetail.program_website,
                
                // 费用和要求信息（使用数据库完整数据）
                tuitionRange: programDetail.program_tuition,
                申请要求: programDetail.application_requirements,
                GPA要求: programDetail.gpa_requirements,
                语言要求: programDetail.language_requirements,
                课程设置: programDetail.courses,
                培养目标: programDetail.program_objectives,
                其他费用: programDetail.other_cost,
                学位认证: programDetail.degree_evaluation,
                
                // 地理和排名信息（使用数据库完整数据）
                location: programDetail.school_region,
                ranking: programDetail.school_qs_rank ? 
                         `QS排名 #${programDetail.school_qs_rank}` : 
                         '排名信息暂无',
                
                // 专业库匹配特有信息
                matchSource: result.match_source || 'database_match',
                
                // 默认UI状态
                showDetails: false,
                isFavorite: false,
                detailsHeight: 0,
                detailsLoaded: false,
                originalHeight: 0,
                
                // 专业库匹配模式下的推荐信息
                reason: '基于您的留学意向进行专业库精确匹配',
                highlights: ['专业库匹配'],
                deadline: programDetail.application_time || '申请截止时间请查看官网'
              };
            } catch (error) {
              console.error(`获取program_id: ${result.program_id} 详情失败:`, error);
              
              // 获取失败时使用原始数据，但标记为数据不完整
              return {
                id: result.rank,
                rank: result.rank,
                program_id: result.program_id,
                
                // 学校信息
                学校中文名: result.school_name_cn || '学校信息获取失败',
                学校英文名: result.school_name_en || '',
                
                // 专业信息
                专业中文名: result.program_name_cn || '专业信息获取失败',
                专业英文名: result.program_name_en || '',
                专业大类: result.program_category || '信息获取失败',
                专业方向: result.program_direction || '信息获取失败',
                所在学院: result.faculty || '信息获取失败',
                
                // 基本信息
                location: result.school_region || '地区未知',
                ranking: result.school_qs_rank ? 
                         `QS排名 #${result.school_qs_rank}` : 
                         '排名信息获取失败',
                tuitionRange: result.program_tuition || '费用信息获取失败',
                
                // 专业库匹配特有信息
                matchSource: result.match_source || 'database_match',
                
                // 默认UI状态
                showDetails: false,
                isFavorite: false,
                detailsHeight: 0,
                detailsLoaded: false,
                originalHeight: 0,
                
                // 默认信息
                reason: '基于您的留学意向进行专业库精确匹配（详细信息获取失败）',
                highlights: ['专业库匹配', '信息获取失败'],
                deadline: '申请截止时间获取失败',
                申请要求: '要求信息获取失败',
                语言要求: '语言要求获取失败',
                课程设置: '课程信息获取失败',
                培养目标: '培养目标获取失败'
              };
            }
          })
        );
        
        // 添加到推荐列表
        recommendations.value.push(...enrichResults);
        
        // 更新进度
        const progressIncrement = (eventData.batch_number / (eventData.total_batches || 1)) * 60;
        recommendationProgress.value = Math.min(90, 25 + progressIncrement);
        progressMessage.value = `处理第${eventData.batch_number}批匹配结果`;
      }
      break;

    case 'hard_filter_complete':
      recommendationProgress.value = 100;
      progressMessage.value = '专业库匹配完成';
      currentStage.value = '匹配完成';
      streamingStatus.value = 'completed';
      isLoading.value = false;
      hasSubmitted.value = true;
      currentResultMode.value = 'hard_filter'; // 确保标记为专业库匹配模式
      
      // 专业库匹配模式下进行学校分组
      groupRecommendationsBySchool();
      
      // 预加载所有学校的Logo
      if (recommendations.value.length > 0) {
        preloadSchoolLogos(recommendations.value);
      }
      
      // 额外检查，如果分组失败，使用备用逻辑
      if (schoolGroups.value.length === 0 && recommendations.value.length > 0) {
        setTimeout(() => {
          groupRecommendationsBySchool();
        }, 100);
      }
      
      ElMessage.success(`专业库匹配完成，共找到 ${eventData.total_results || recommendations.value.length} 个匹配专业`);
      break;
      
    case 'candidates':
      recommendationProgress.value = 30;
      progressMessage.value = getProgressMessage('candidates', eventData);
      currentStage.value = '智能筛选专业';
      break;
      
    case 'school_matching':
      recommendationProgress.value = 50;
      progressMessage.value = getProgressMessage('school_matching', eventData);
      currentStage.value = '院校适配分析';
      break;
      
    case 'complete':
      totalElapsed.value = total_elapsed || 0;
      currentStage.value = '匹配完成';
      streamingStatus.value = 'completed';
      isLoading.value = false;
      hasSubmitted.value = true;
      recommendationProgress.value = 100;
      progressMessage.value = getProgressMessage('complete');
      currentPreviews.value = [];
      
      // 只有在当前不是专业库匹配模式时，才设置为AI模式
      // 防止覆盖已经设置的专业库匹配模式
      if (currentResultMode.value !== 'hard_filter') {
        currentResultMode.value = 'ai';
      }
      
      // 预加载所有学校的Logo
      if (recommendations.value.length > 0) {
        preloadSchoolLogos(recommendations.value);
      }
      break;
      
    case 'error':
      streamingStatus.value = 'error';
      isLoading.value = false;
      recommendationProgress.value = 0;
      progressMessage.value = '匹配服务异常';
      console.error('后端匹配错误:', message, eventData);
      ElMessage.error(message || '匹配过程中出现错误，请稍后重试');
      break;
      
    default:
      console.warn('收到未知SSE事件类型:', type, eventData);
  }
};

// 获取阶段显示名称
const getStageDisplayName = (stage) => {
  const stageMap = {
    'profile': '学术背景分析',
    'matching': '院校匹配', 
    'scoring': '适配度计算',
    'ranking': '排名生成',
    'reasoning': '推荐分析',
    'filtering': '结果筛选',
    'analyzing': '条件评估',
    'preprocessing': '信息处理',
    'calculating': '算法执行',
    'finalizing': '方案完善',
    'hard_filter_start': '专业库匹配启动',
    'filter_criteria': '应用筛选条件',
    'hard_filter_complete': '专业库匹配完成'
  };
  return stageMap[stage] || `${stage}处理`;
};

// 获取专业化的进度消息
const getProgressMessage = (stage, eventData = {}) => {
  const messageMap = {
    'start': '初始化匹配系统',
    'profile': '分析学术背景',
    'matching': '检索院校数据库',
    'scoring': '计算匹配度',
    'ranking': '生成匹配排名',
    'reasoning': '生成匹配理由',
    'filtering': '筛选匹配结果',
    'analyzing': '评估申请条件',
    'preprocessing': '处理申请信息',
    'calculating': '执行匹配算法',
    'finalizing': '完成推荐方案',
    'candidates': '筛选候选专业',
    'school_matching': '匹配院校数据',
    'matching_start': '开始评估专业项目',
    'matching_progress': '评估专业项目中',
    'ranking_start': '生成匹配排名',
    'recommendation_batch_start': '生成详细推荐报告',
    'complete': '匹配完成',
    'recommendation_complete': '完善推荐详情',
    'recommendation_error': '重新处理推荐项',
    'hard_filter_start': '启动专业库匹配',
    'filter_criteria': '应用筛选条件',
    'hard_filter_complete': '专业库匹配完成'
  };
  return messageMap[stage] || `正在${getStageDisplayName(stage)}`;
};



// 表单验证逻辑
const validateForm = () => {
  formValidation.hasValidated = true;
  let isValid = true;
  
  // 验证学术背景必填项
  const academic = profileForm.academic;
  formValidation.errors.academic.school = !academic.school;
  formValidation.errors.academic.major = !academic.major;
  formValidation.errors.academic.gpa = !academic.gpa;
  formValidation.errors.academic.gpaScale = !academic.gpaScale;
  
  // 特别验证GPA范围
  if (academic.gpa && academic.gpaScale) {
    const gpaValidation = validateGpaValue(academic.gpa, academic.gpaScale);
    gpaValidationError.value = !gpaValidation.isValid;
    gpaErrorMessage.value = gpaValidation.message;
    if (!gpaValidation.isValid) {
      isValid = false;
    }
  }
  
  // 验证留学意向必填项
  const intention = profileForm.intention;
  formValidation.errors.intention.countries = !intention.countries.length;
  formValidation.errors.intention.majors = !intention.majors.length;
  
  // 检查是否有任何错误
  const academicErrors = Object.values(formValidation.errors.academic);
  const intentionErrors = Object.values(formValidation.errors.intention);
  const hasBasicErrors = academicErrors.some(error => error) || intentionErrors.some(error => error);
  const hasGpaError = gpaValidationError.value;
  
  isValid = !hasBasicErrors && !hasGpaError;
  
  return isValid;
};

// 获取错误字段数量
const getErrorCount = (section) => {
  return Object.values(formValidation.errors[section]).filter(error => error).length;
};

// 提交表单
const handleSubmit = () => {
  if (!validateForm()) {
    const academicErrorCount = getErrorCount('academic');
    const intentionErrorCount = getErrorCount('intention');
    const hasGpaError = gpaValidationError.value;
    
    // 根据错误类型提供不同的提示信息
    let errorMessage = '请完善必填信息后再开始匹配';
    if (hasGpaError) {
      errorMessage = '成绩超出正常范围，请检查后重试';
    } else if (academicErrorCount > 0 || intentionErrorCount > 0) {
      errorMessage = '请完善必填信息后再开始匹配';
    }
    
    ElMessage.error({
      message: errorMessage,
      duration: 3000,
      showClose: true
    });
    
    // 自动展开有错误的区域
    if (academicErrorCount > 0 || hasGpaError) {
      sectionStates.academic = true;
    }
    if (intentionErrorCount > 0) {
      sectionStates.intention = true;
    }
    
    return;
  }
  
  getRecommendations();
};

// 收藏/取消收藏学校
const toggleFavorite = (school) => {
  school.isFavorite = !school.isFavorite;
  const message = school.isFavorite 
    ? `已收藏 ${school.name}，方便后续查看` 
    : `已取消收藏 ${school.name}`;
  // ElMessage.success(message);
};

// 添加logo加载错误处理方法
const handleLogoError = (event, school) => {
  // 如果logo加载失败，显示学校名称首字母
  event.target.style.display = 'none';
  const container = event.target.parentElement;
  if (container) {
    const fallbackText = document.createElement('span');
    fallbackText.className = 'text-2xl font-bold text-gray-400';
    // 使用正确的字段名：学校中文名
    const schoolName = school.学校中文名 || school.name || 'U';
    fallbackText.textContent = schoolName.charAt(0);
    container.appendChild(fallbackText);
  }
};

// 动画处理方法
const handleEnter = (el, done) => {
  const height = el.scrollHeight;
  el.style.height = '0px';
  el.style.opacity = '0';
  
  // 触发重绘
  el.offsetHeight;
  
  // 设置过渡属性
  el.style.transition = 'height 0.7s ease, opacity 0.7s ease';
  el.style.height = `${height}px`;
  el.style.opacity = '1';
  
  el.addEventListener('transitionend', function onEnd() {
    el.style.height = 'auto';
    el.removeEventListener('transitionend', onEnd);
    done();
  });
};

const handleLeave = (el, done) => {
  const height = el.scrollHeight;
  el.style.height = `${height}px`;
  
  // 触发重绘
  el.offsetHeight;
  
  // 设置过渡属性
  el.style.transition = 'height 0.7s ease, opacity 0.7s ease';
  el.style.height = '0px';
  el.style.opacity = '0';
  
  el.addEventListener('transitionend', function onEnd() {
    el.style.height = '0px';
    el.removeEventListener('transitionend', onEnd);
    done();
  });
};

// 切换详情显示的方法
const toggleDetails = (school) => {
  if (!school.detailsLoaded) {
    // 第一次展开时计算高度
    school.detailsHeight = 250; // 初始估计高度，可以稍大一些以避免初次闪烁
    school.detailsLoaded = true;
    school.showDetails = true;
    
    // 在下一个渲染周期计算实际高度
    setTimeout(() => {
      const detailsEls = document.querySelectorAll('.details-content');
      for (const el of detailsEls) {
        if (el.closest(`[data-school-id="${school.id}"]`)) {
          school.originalHeight = el.scrollHeight; // 使用 scrollHeight 获取完整内容高度
          school.detailsHeight = school.originalHeight;
          break;
        }
      }
    }, 150); // 增加延迟确保内容渲染完毕
  } else {
    // 已经加载过，直接切换状态
    school.showDetails = !school.showDetails;
    school.detailsHeight = school.showDetails ? school.originalHeight : 0;
  }
};

// 初始化学校数据
const initRecommendations = (schools) => {
  return schools.map(school => {
    return {
      ...school,
      detailsHeight: 0,
      detailsLoaded: false,
      originalHeight: 0, // 添加原始高度字段
      showDetails: false
    };
  });
};

// 使用program_id查询专业详情并处理推荐数据
const handleRecommendationWithProgramQuery = async (recommendation, rank) => {
  try {
    
    // 调用后端API获取完整专业信息
    const response = await fetch(`/api/ai-selection/data/programs/${recommendation.program_id}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache',
      }
    });

    if (!response.ok) {
      throw new Error(`获取专业详情失败: ${response.status}`);
    }

    const programDetail = await response.json();

    // 构建匹配数据，组合推荐结果和专业详情
    const formattedRecommendation = {
      id: rank,
      rank: rank,
      program_id: recommendation.program_id,
      
      // 学校基本信息（来自专业详情）
      学校中文名: programDetail.school_name_cn,
      学校英文名: programDetail.school_name_en,
      
      // 专业信息（来自专业详情）
      专业中文名: programDetail.program_name_cn,
      专业英文名: programDetail.program_name_en,
      专业大类: programDetail.program_category,
      专业方向: programDetail.program_direction,
      所在学院: programDetail.faculty,
      
      // 申请和学制信息（来自专业详情）
      入学时间: programDetail.enrollment_time,
      项目时长: programDetail.program_duration,
      项目官网: programDetail.program_website,
      
      // 费用和要求信息（来自专业详情）
      tuitionRange: programDetail.program_tuition,
      申请要求: programDetail.application_requirements,
      GPA要求: programDetail.gpa_requirements,
      语言要求: programDetail.language_requirements,
      课程设置: programDetail.courses,
      培养目标: programDetail.program_objectives,
      其他费用: programDetail.other_cost,
      学位认证: programDetail.degree_evaluation,
      
      // 地理和排名信息（来自专业详情）
      location: programDetail.school_region,
      ranking: programDetail.school_qs_rank ? 
               `QS排名 #${programDetail.school_qs_rank}` : 
               '排名信息暂无',
      
      // 推荐相关信息（来自推荐结果）
      matchScore: Math.round((recommendation.scores?.total_match || 0.8) * 100),
      reason: recommendation.recommendation_reason,
      school_tier: recommendation.school_tier,
      matching_cases_count: recommendation.matching_cases_count || 0,
      
      // 院校特色
      highlights: [`${programDetail.program_direction}专业`],
      deadline: programDetail.application_time || '申请截止待查',
      
      // UI状态
      showDetails: false,
      isFavorite: false,
      detailsHeight: 0,
      detailsLoaded: false,
      originalHeight: 0
    };
    
    // 按排名插入到正确位置
    const existingIndex = recommendations.value.findIndex(r => r.rank === rank);
    if (existingIndex >= 0) {
      // 更新现有推荐
      recommendations.value[existingIndex] = formattedRecommendation;
    } else {
      // 添加新推荐并排序
      recommendations.value.push(formattedRecommendation);
      recommendations.value.sort((a, b) => a.rank - b.rank);
    }
    
    // 更新进度消息
    const completedCount = recommendations.value.length;
    const expectedTotal = 10; // 预期推荐数量
    recommendationProgress.value = Math.min(97, 88 + (completedCount / expectedTotal) * 9);
    progressMessage.value = '生成匹配方案';
    
  } catch (error) {
    console.error(`查询program_id: ${recommendation.program_id} 失败:`, error);
    
    // 查询失败时使用推荐数据本身的基本信息
    const fallbackRecommendation = {
      id: rank,
      rank: rank,
      program_id: recommendation.program_id,
      
      // 使用推荐数据中的基本信息
      学校中文名: recommendation.school_name || '学校信息获取失败',
      学校英文名: recommendation.school_name || '学校信息获取失败',
      专业中文名: recommendation.program_name_cn || '专业信息获取失败',
      专业英文名: recommendation.program_name_cn || '专业信息获取失败',
      专业大类: recommendation.program_direction || '信息获取失败',
      专业方向: recommendation.program_direction || '信息获取失败',
      所在学院: '信息获取失败',
      
      // 基本信息
      location: recommendation.region_name || '地区未知',
      ranking: '排名信息获取失败',
      matchScore: Math.round((recommendation.scores?.total_match || 0.8) * 100),
      reason: recommendation.recommendation_reason || '推荐理由获取失败',
      school_tier: recommendation.school_tier || 'Tier 1',
      matching_cases_count: recommendation.matching_cases_count || 0,
      
      // 默认信息
      highlights: ['专业信息获取中'],
      tuitionRange: '费用信息获取失败',
      deadline: '截止时间获取失败',
      申请要求: '要求信息获取失败',
      语言要求: '语言要求获取失败',
      
      // UI状态
      showDetails: false,
      isFavorite: false,
      detailsHeight: 0,
      detailsLoaded: false,
      originalHeight: 0
    };

    // 添加失败的推荐（显示错误信息）
    const existingIndex = recommendations.value.findIndex(r => r.rank === rank);
    if (existingIndex >= 0) {
      recommendations.value[existingIndex] = fallbackRecommendation;
    } else {
      recommendations.value.push(fallbackRecommendation);
      recommendations.value.sort((a, b) => a.rank - b.rank);
    }
  }
};

// 清理SSE连接
const cleanupSSEConnection = () => {
  if (sseConnection.value) {
    try {
      sseConnection.value.close();
    } catch (e) {
      console.warn('关闭SSE连接时出错:', e);
    }
    sseConnection.value = null;
  }
  streamingStatus.value = '';
  isLoading.value = false;
};

// 生命周期钩子
onMounted(async () => {
  
  // 初始化屏幕尺寸检查
  checkScreenSize();
  
  // 监听窗口大小变化，重新计算详情区域高度和屏幕尺寸
  window.addEventListener('resize', () => {
    recalculateHeights();
    checkScreenSize();
  });
  
  // 监听页面离开事件，清理SSE连接
  window.addEventListener('beforeunload', cleanupSSEConnection);
  
  // 页面可见性变化时的处理
  document.addEventListener('visibilitychange', () => {
    // 可以在这里处理页面可见性变化的逻辑
  });

  // 预加载一些常见学校的logo作为测试
  try {
    const commonSchools = [
      { 学校中文名: '香港大学' },
      { 学校中文名: '新加坡国立大学' },
      { 学校中文名: '帝国理工学院' },
      { 学校中文名: '伦敦大学学院' },
      { 学校中文名: '南洋理工大学' }
    ];
    await preloadSchoolLogos(commonSchools);
  } catch (error) {
    console.warn('预加载常见学校logo失败:', error);
  }
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', () => {
    recalculateHeights();
    checkScreenSize();
  });
  window.removeEventListener('beforeunload', cleanupSSEConnection);
  cleanupSSEConnection();
});

// 窗口大小变化时重新计算高度
const recalculateHeights = () => {
  if (!recommendations.value.length) return;
  
  setTimeout(() => {
    const detailsEls = document.querySelectorAll('.details-content');
    for (const el of detailsEls) {
      const schoolId = el.closest('[data-school-id]')?.getAttribute('data-school-id');
      if (schoolId) {
        const school = recommendations.value.find(s => s.id == schoolId);
        if (school && school.detailsLoaded) {
          school.originalHeight = el.scrollHeight; // 使用 scrollHeight
          if (school.showDetails) {
            school.detailsHeight = school.originalHeight;
          }
        }
      }
    }
  }, 150); // 增加延迟
};

// 排序后的匹配结果
const filteredRecommendations = computed(() => {
  let result = recommendations.value;
  
  // 排序逻辑
  const [field, order] = sortBy.value.split('-');
  
  return result.sort((a, b) => {
    // 推荐排名排序（默认）
    if (field === 'rank') {
      return order === 'asc' ? a.rank - b.rank : b.rank - a.rank;
    }
    
    // QS排名排序
    if (field === 'ranking') {
      const extractQSRank = (rankingStr) => {
        if (!rankingStr || rankingStr === '排名信息暂无' || rankingStr === '排名信息获取失败') return 9999;
        const match = rankingStr.match(/\d+/);
        return match ? parseInt(match[0], 10) : 9999;
      };
      
      const rankA = extractQSRank(a.ranking);
      const rankB = extractQSRank(b.ranking);
      return order === 'asc' ? rankA - rankB : rankB - rankA;
    }
    
    // 对于其他数值字段
    return order === 'asc' ? a[field] - b[field] : b[field] - a[field];
  });
});

// 监听表单字段变化，实时清除错误状态
watch(() => profileForm.academic.gpa, (newValue) => {
  if (formValidation.hasValidated && newValue && newValue.trim() !== '') {
    formValidation.errors.academic.gpa = false;
  }
});

watch(() => profileForm.academic.gpaScale, (newValue) => {
  if (formValidation.hasValidated && newValue && newValue !== '') {
    formValidation.errors.academic.gpaScale = false;
  }
});

watch(() => profileForm.academic.school, (newValue) => {
  if (formValidation.hasValidated && newValue && newValue.trim() !== '') {
    formValidation.errors.academic.school = false;
  }
});

watch(() => profileForm.academic.major, (newValue) => {
  if (formValidation.hasValidated && newValue && newValue.trim() !== '') {
    formValidation.errors.academic.major = false;
  }
});

watch(() => profileForm.intention.countries, (newValue) => {
  if (formValidation.hasValidated && Array.isArray(newValue) && newValue.length > 0) {
    formValidation.errors.intention.countries = false;
  }
});

watch(() => profileForm.intention.majors, (newValue) => {
  if (formValidation.hasValidated && Array.isArray(newValue) && newValue.length > 0) {
    formValidation.errors.intention.majors = false;
  }
});

// 监听匹配结果变化，自动进行学校分组（仅在专业库匹配模式下）
watch(() => recommendations.value, (newRecommendations, oldRecommendations) => {
  if (currentResultMode.value === 'hard_filter' && newRecommendations.length > 0) {
    groupRecommendationsBySchool();
  }
}, { deep: true, immediate: false });

// 监听AI智能匹配开关变化 - 仅影响标题显示，不改变现有结果
watch(() => enableAiSelection.value, () => {
  // AI智能匹配开关仅影响下次匹配时的模式，不改变当前已有的结果显示
});

// GPA计算属性 - 根据成绩制式动态生成placeholder和验证规则
const gpaPlaceholder = computed(() => {
  const scale = profileForm.academic.gpaScale;
  switch (scale) {
    case '4.0':
      return '请输入GPA（0.0-4.0）';
    case '5.0':
      return '请输入GPA（0.0-5.0）';
    case '100':
      return '请输入成绩（0-100）';
    default:
      return '请先选择成绩制式';
  }
});

// GPA范围配置
const gpaRanges = {
  '4.0': { min: 0, max: 4.0, step: 0.01, decimalPlaces: 2 },
  '5.0': { min: 0, max: 5.0, step: 0.01, decimalPlaces: 2 },
  '100': { min: 0, max: 100, step: 0.01, decimalPlaces: 2 }
};

// 验证GPA值是否在正确范围内
const validateGpaValue = (gpaValue, gpaScale) => {
  // 如果没有输入值或没有选择制式，不验证（避免初始状态报错）
  if (!gpaValue || !gpaScale) return { isValid: true, message: '' };
  
  // 去除空格
  const trimmedValue = gpaValue.toString().trim();
  if (!trimmedValue) return { isValid: true, message: '' };
  
  const numValue = parseFloat(trimmedValue);
  const range = gpaRanges[gpaScale];
  
  if (!range) return { isValid: false, message: '' };
  
  // 检查是否为有效数字
  if (isNaN(numValue)) {
    return { isValid: false, message: '' };
  }
  
  // 检查范围 - 超出范围时只标红，不显示错误信息
  if (numValue < range.min || numValue > range.max) {
    return { isValid: false, message: '' };
  }
  
  // 检查小数位数
  const decimalPlaces = (numValue.toString().split('.')[1] || '').length;
  if (decimalPlaces > range.decimalPlaces) {
    return { isValid: false, message: '' };
  }
  return { isValid: true, message: '' };
};

// GPA输入处理函数
const handleGpaInput = (value) => {
  profileForm.academic.gpa = value;
  
  // 实时验证
  const validation = validateGpaValue(value, profileForm.academic.gpaScale);
  gpaValidationError.value = !validation.isValid;
  gpaErrorMessage.value = validation.message;
  
  // 清除基础错误状态
  clearFieldError('academic', 'gpa');
  checkSectionCompletion('academic');
};

// GPA变更处理函数
const handleGpaChange = (value) => {
  profileForm.academic.gpa = value;
  
  // 完整验证
  const validation = validateGpaValue(value, profileForm.academic.gpaScale);
  gpaValidationError.value = !validation.isValid;
  gpaErrorMessage.value = validation.message;
  
  clearFieldError('academic', 'gpa');
  checkSectionCompletion('academic');
};

// 成绩制式变更处理函数  
const handleGpaScaleChange = (value) => {
  profileForm.academic.gpaScale = value;
  
  // 如果已有GPA值，重新验证
  if (profileForm.academic.gpa) {
    const validation = validateGpaValue(profileForm.academic.gpa, value);
    gpaValidationError.value = !validation.isValid;
    gpaErrorMessage.value = validation.message;
  } else {
    // 清除验证错误
    gpaValidationError.value = false;
    gpaErrorMessage.value = '';
  }
  
  clearFieldError('academic', 'gpaScale');
  checkSectionCompletion('academic');
};

// === 专业库匹配模式的学校分组功能 ===

// 将匹配结果按学校分组
const groupRecommendationsBySchool = () => {
  if (currentResultMode.value !== 'hard_filter') {
    schoolGroups.value = [];
    return;
  }
  
  if (!recommendations.value.length) {
    schoolGroups.value = [];
    return;
  }
  
  // 按学校名称分组
  const groupMap = new Map();
  
  // === 保留已存在学校组的展开/折叠状态，避免重组时丢失 ===
  const prevStateMap = new Map(
    schoolGroups.value.map(g => [g.schoolKey, { expanded: g.expanded, contentHeight: g.contentHeight }])
  );
  
  recommendations.value.forEach((program, index) => {
    const schoolKey = program.学校中文名 || 'unknown';
    
    if (!groupMap.has(schoolKey)) {
      const prev = prevStateMap.get(schoolKey) || { expanded: false, contentHeight: 0 };
      groupMap.set(schoolKey, {
        schoolKey,
        schoolName: program.学校中文名,
        englishName: program.学校英文名,
        location: program.location,
        qsRank: program.ranking?.match(/\d+/)?.[0],
        programs: [],
        expanded: prev.expanded,
        contentHeight: prev.contentHeight
      });
    }
    
    // 确保每个专业的UI状态都已正确初始化，但不覆盖已有状态
    if (program.showDetails === undefined) {
      program.showDetails = false;
    }
    if (program.isFavorite === undefined) {
      program.isFavorite = false;
    }
    if (program.detailsHeight === undefined) {
      program.detailsHeight = 0;
    }
    if (program.detailsLoaded === undefined) {
      program.detailsLoaded = false;
    }
    if (program.originalHeight === undefined) {
      program.originalHeight = 0;
    }
    groupMap.get(schoolKey).programs.push(program);
  });
  
  // 转换为数组并按QS排名排序
  const groupArray = Array.from(groupMap.values());
  
  // 排序
  groupArray.sort((a, b) => {
    const rankA = parseInt(a.qsRank) || 9999;
    const rankB = parseInt(b.qsRank) || 9999;
    return rankA - rankB;
  });
  
  schoolGroups.value = groupArray;
  
  // 强制触发响应式更新
  schoolGroups.value = [...schoolGroups.value];

  // 若展开的学校组 contentHeight 丢失，延迟重新计算确保高度正确
  setTimeout(() => {
    schoolGroups.value.forEach(group => {
      if (group.expanded && group.contentHeight === 0) {
        const el = programsContentRefs.value[group.schoolKey];
        if (el) group.contentHeight = el.scrollHeight;
      }
    });
  }, 60);
};

// 切换学校组的展开/收起状态
const toggleSchoolGroup = (schoolGroup) => {
  schoolGroup.expanded = !schoolGroup.expanded;
  
  if (schoolGroup.expanded) {
    // 展开时计算内容高度
    setTimeout(() => {
      const contentEl = programsContentRefs.value[schoolGroup.schoolKey];
      if (contentEl) {
        schoolGroup.contentHeight = contentEl.scrollHeight;
      }
    }, 50);
  } else {
    // 收起时重置高度
    schoolGroup.contentHeight = 0;
  }
};

// 切换专业详情显示
const toggleProgramDetails = (program) => {
  // 阻止事件冒泡
  event?.stopPropagation?.();
  
  // 确保program对象存在必要的属性
  if (program.detailsHeight === undefined) program.detailsHeight = 0;
  if (program.originalHeight === undefined) program.originalHeight = 0;
  if (program.detailsLoaded === undefined) program.detailsLoaded = false;
  
  if (!program.detailsLoaded) {
    // 第一次展开时初始化状态
    program.detailsLoaded = true;
    program.showDetails = true;
    program.detailsHeight = 300; // 设置初始高度，避免闪烁
    
    // 在下一个渲染周期计算实际高度
    setTimeout(() => {
      // 尝试多种选择器找到详情容器
      let detailsEl = null;
      
      // 1. 首先尝试使用program_id
      if (program.program_id) {
        const selector1 = `[data-program-id="${program.program_id}"] .details-content`;
        detailsEl = document.querySelector(selector1);
      }
      
      // 2. 如果找不到，尝试使用id
      if (!detailsEl && program.id) {
        const selector2 = `[data-program-id="${program.id}"] .details-content`;
        detailsEl = document.querySelector(selector2);
      }
      
      // 3. 最后尝试使用更通用的选择器
      if (!detailsEl) {
        // 找到所有详情容器
        const allDetailsEls = document.querySelectorAll('.details-content');
        // 遍历所有容器，找到最近刚展开的那个
        for (const el of allDetailsEls) {
          const programCard = el.closest('.program-card');
          if (programCard && !detailsEl) {
            detailsEl = el;
          }
        }
      }
      
      if (detailsEl) {
        const height = detailsEl.scrollHeight;
        program.originalHeight = height;
        program.detailsHeight = height;
      } else {
        // 如果所有选择器都失败，使用备用高度
        program.originalHeight = 350; // 使用默认高度
        program.detailsHeight = program.originalHeight;
      }
      
      // 重新计算父级学校组的内容高度
      updateSchoolGroupHeight(program);
    }, 300); // 增加延迟确保内容完全渲染
  } else {
    // 已经加载过，直接切换状态
    const newState = !program.showDetails;
    program.showDetails = newState;
    program.detailsHeight = newState ? program.originalHeight : 0;
    
    // 重新计算父级学校组的内容高度
    setTimeout(() => {
      updateSchoolGroupHeight(program);
    }, 100);
  }
};

// 更新学校组高度的辅助函数
const updateSchoolGroupHeight = (program) => {
  // 使用多种ID匹配方式，确保能找到正确的学校组
  const schoolGroup = schoolGroups.value.find(group => {
    return group.programs.some(p => 
      // 使用多种匹配方式：id、program_id、专业中文名+学校中文名
      (p.id === program.id) || 
      (p.program_id === program.program_id) || 
      (p.专业中文名 === program.专业中文名 && p.学校中文名 === program.学校中文名)
    );
  });
  
  if (schoolGroup && schoolGroup.expanded) {
    // 给DOM更新留出时间
    setTimeout(() => {
      const contentEl = programsContentRefs.value[schoolGroup.schoolKey];
      if (contentEl) {
        schoolGroup.contentHeight = contentEl.scrollHeight;
      }
    }, 50);
  }
};

// 切换专业收藏状态
const toggleProgramFavorite = (program) => {
  program.isFavorite = !program.isFavorite;
  const message = program.isFavorite 
    ? `已收藏专业 ${program.专业中文名}` 
    : `已取消收藏 ${program.专业中文名}`;
  ElMessage.success(message);
};

// 设置专业内容区域的引用
const setProgramsContentRef = (schoolKey, el) => {
  if (el) {
    programsContentRefs.value[schoolKey] = el;
  }
};

// 自动修正结果模式的逻辑 - 用于处理后端事件顺序问题
const autoCorrectResultMode = () => {
  // 检测数据类型不匹配的情况并自动修正
  const hasSchoolGroups = schoolGroups.value.length > 0;
  const hasRecommendations = recommendations.value.length > 0;
  const currentMode = currentResultMode.value;
  
  // 如果有学校分组数据但当前模式不是专业库匹配，说明模式被错误覆盖了
  if (hasSchoolGroups && currentMode !== 'hard_filter') {
    console.warn('检测到数据类型不匹配，自动修正显示模式为专业库匹配');
    currentResultMode.value = 'hard_filter';
    return true; // 表示进行了修正
  }
  
  return false; // 表示无需修正
};

// 在适当的时机调用自动修正
watch(() => [schoolGroups.value.length, recommendations.value.length, currentResultMode.value], 
  ([schoolGroupsLen, recommendationsLen, mode]) => {
    if (schoolGroupsLen > 0 && recommendationsLen > 0) {
      // 短暂延迟后检查是否需要自动修正
      setTimeout(() => {
        autoCorrectResultMode();
      }, 100);
    }
  }, 
  { immediate: true }
);

// 客户操作相关状态
const addingToClientPrograms = ref(false);

// 加载默认客户列表
const loadDefaultClientList = async () => {
  if (defaultClientList.value.length > 0) {
    // 如果已经有缓存的客户列表，直接使用
    clientSearchResults.value = defaultClientList.value;
    return;
  }
  
  clientSearchLoading.value = true;
  try {
    // 获取客户列表，限制为前20个，按最近修改时间排序
    const response = await getClientList({ 
      page: 1, 
      page_size: 20,
      ordering: '-updated_at'  // 按最近修改时间倒序排列
    });
    const clients = response.data?.clients || response.clients || response.data || response || [];
    
    // 缓存默认客户列表
    defaultClientList.value = clients;
    clientSearchResults.value = clients;
  } catch (error) {
    console.error('加载客户列表失败:', error);
    clientSearchResults.value = [];
  } finally {
    clientSearchLoading.value = false;
  }
};

// 处理客户搜索输入框 focus 事件
const handleClientSearchFocus = async () => {
  showClientSelector.value = true;
  updateDropdownPosition();
  
  // 如果没有搜索内容，显示默认客户列表
  if (!clientSearchQuery.value || clientSearchQuery.value.trim() === '') {
    await loadDefaultClientList();
  }
};

// 监听下拉框显示状态变化，更新位置
watch(showClientSelector, (newValue) => {
  if (newValue) {
    updateDropdownPosition();
    // 监听窗口滚动和大小变化，实时更新位置
    window.addEventListener('scroll', updateDropdownPosition);
    window.addEventListener('resize', updateDropdownPosition);
  } else {
    window.removeEventListener('scroll', updateDropdownPosition);
    window.removeEventListener('resize', updateDropdownPosition);
  }
});

// 处理客户搜索
const handleClientSearch = async (query) => {
  if (!query || query.trim() === '') {
    // 如果没有搜索内容，显示默认客户列表
    if (showClientSelector.value) {
      await loadDefaultClientList();
    } else {
      clientSearchResults.value = [];
    }
    return;
  }

  clientSearchLoading.value = true;
  try {
    const response = await searchClients(query.trim());
    let clients = response.data || response || [];
    
    // 按最近修改时间排序搜索结果
    clients.sort((a, b) => {
      const dateA = new Date(a.updated_at || a.created_at || 0);
      const dateB = new Date(b.updated_at || b.created_at || 0);
      return dateB - dateA; // 倒序排列，最新的在前面
    });
    
    clientSearchResults.value = clients;
  } catch (error) {
    console.error('搜索客户失败:', error);
    clientSearchResults.value = [];
    ElMessage.error('搜索客户失败');
  } finally {
    clientSearchLoading.value = false;
  }
};

// 选择客户
const selectClient = async (client) => {
  try {
    console.log('开始选择客户:', client);
    
    // 设置选中的客户
    selectedClient.value = client;
    showClientSelector.value = false;
    clientSearchQuery.value = '';
    clientSearchResults.value = [];
    
    // 确保 profileForm 已初始化
    if (!profileForm || !profileForm.academic) {
      console.error('profileForm 未正确初始化');
      ElMessage.error('系统初始化错误，请刷新页面重试');
      return;
    }
    
    console.log('开始获取客户详细信息，ID:', client.id_hashed);
    
    // 获取客户详细信息，包括教育背景
    const clientDetails = await getClientById(client.id_hashed);
    console.log('获取到客户详情:', clientDetails);
    
    // 自动填充学术背景信息
    await fillAcademicBackgroundFromClient(clientDetails);
    
    // 加载客户的定校书专业列表
    await loadClientPrograms();
    
            ElMessage.success(`已选择客户：${client.name}`);
  } catch (error) {
    console.error('选择客户时发生错误:', error);
    
    // 确保selectedClient已设置，以便定校书功能可用
    selectedClient.value = client;
    
    try {
      // 即使获取详情失败，也要加载定校书
      await loadClientPrograms();
    } catch (loadError) {
      console.error('加载客户定校书失败:', loadError);
    }
    
    ElMessage.success(`已选择客户：${client.name}`);
    
    // 根据错误类型给出不同的提示
    if (error.message && error.message.includes('Cannot read properties of undefined')) {
      ElMessage.warning('获取客户教育背景信息失败，数据格式可能有问题，请手动填写');
    } else {
      ElMessage.warning('获取客户详细信息失败，请手动填写学术背景信息');
    }
  }
};

// 从客户信息中自动填充学术背景
const fillAcademicBackgroundFromClient = async (clientDetailsResponse) => {
  try {
    // 处理不同的API响应格式
    const clientDetails = clientDetailsResponse?.data || clientDetailsResponse;
    
    console.log('客户详情响应:', clientDetails);
    
    // 检查客户详情是否存在
    if (!clientDetails) {
      console.log('客户详情为空');
      return;
    }
    
    // 检查是否有教育背景信息
    if (!clientDetails.education || !Array.isArray(clientDetails.education) || clientDetails.education.length === 0) {
      console.log('客户没有教育背景信息或格式不正确');
      return;
    }
    
    console.log('教育背景数据:', clientDetails.education);
    
    // 找到本科教育经历（优先选择学位为 bachelor 或本科的记录）
    let undergraduateEducation = clientDetails.education.find(edu => {
      if (!edu || !edu.degree) return false;
      const degree = edu.degree.toLowerCase();
      return degree.includes('bachelor') || degree.includes('本科') || degree.includes('学士');
    });
    
    // 如果没有找到本科记录，取最新的一条教育记录
    if (!undergraduateEducation && clientDetails.education.length > 0) {
      // 按创建时间或ID排序，取最新的
      const sortedEducation = [...clientDetails.education].sort((a, b) => {
        if (a.created_at && b.created_at) {
          return new Date(b.created_at) - new Date(a.created_at);
        }
        return (b.id || 0) - (a.id || 0);
      });
      undergraduateEducation = sortedEducation[0];
    }
    
    if (undergraduateEducation) {
      console.log('找到教育背景信息:', undergraduateEducation);
      
      // 确保 profileForm.academic 存在
      if (!profileForm.academic) {
        console.error('profileForm.academic 不存在');
        return;
      }
      
      // 填充学校信息
      if (undergraduateEducation.school) {
        profileForm.academic.school = undergraduateEducation.school;
        clearFieldError('academic', 'school');
        console.log('已填充学校:', undergraduateEducation.school);
      }
      
      // 填充专业信息
      if (undergraduateEducation.major) {
        profileForm.academic.major = undergraduateEducation.major;
        clearFieldError('academic', 'major');
        console.log('已填充专业:', undergraduateEducation.major);
      }
      
      // TODO: 填充GPA信息 - 暂时注释掉，待修复 profileForm.academic 访问问题
      // if (undergraduateEducation.gpa) {
      //   const gpaStr = undergraduateEducation.gpa.toString();
      //   console.log('原始GPA数据:', gpaStr);
      //   
      //   // 智能识别GPA格式并填充
      //   let gpaValue = '';
      //   let gpaScale = '';
      //   
      //   if (gpaStr.includes('/100')) {
      //     // 明确的百分制格式 "85/100"
      //     gpaScale = '100';
      //     gpaValue = gpaStr.replace('/100', '').trim();
      //   } else if (gpaStr.includes('/5.0') || gpaStr.includes('/5')) {
      //     // 5.0制格式 "4.5/5.0" 或 "4.5/5"
      //     gpaScale = '5.0';
      //     gpaValue = gpaStr.replace('/5.0', '').replace('/5', '').trim();
      //   } else if (gpaStr.includes('/4.0') || gpaStr.includes('/4')) {
      //     // 4.0制格式 "3.8/4.0" 或 "3.8/4"
      //     gpaScale = '4.0';
      //     gpaValue = gpaStr.replace('/4.0', '').replace('/4', '').trim();
      //   } else {
      //     // 没有明确分母的情况，需要智能判断
      //     const numericGpa = parseFloat(gpaStr);
      //     if (!isNaN(numericGpa)) {
      //       if (numericGpa > 5 && numericGpa <= 100) {
      //         // 数值在5-100之间，判断为百分制
      //         gpaScale = '100';
      //         gpaValue = gpaStr;
      //       } else if (numericGpa <= 4.0) {
      //         // 数值≤4.0，判断为4.0制
      //         gpaScale = '4.0';
      //         gpaValue = gpaStr;
      //       } else if (numericGpa <= 5.0) {
      //         // 数值在4.0-5.0之间，判断为5.0制
      //         gpaScale = '5.0';
      //         gpaValue = gpaStr;
      //       } else {
      //         // 无法判断，默认为百分制
      //         gpaScale = '100';
      //         gpaValue = gpaStr;
      //       }
      //     } else {
      //       // 非数字，默认原样填入百分制
      //       gpaScale = '100';
      //       gpaValue = gpaStr;
      //     }
      //   }
      //   
      //   // 设置GPA值和制式
      //   profileForm.academic.gpaScale = gpaScale;
      //   profileForm.academic.gpa = gpaValue;
      //   
      //   console.log(`已识别GPA: ${gpaValue}/${gpaScale}制`);
      //   
      //   // 清除GPA相关错误
      //   clearFieldError('academic', 'gpa');
      //   clearFieldError('academic', 'gpaScale');
      //   gpaValidationError.value = false;
      //   
      //   // 触发GPA验证
      //   handleGpaChange();
      // }
      
      // 检查学术背景部分完成状态
      checkSectionCompletion('academic');
      
      // 如果学术背景部分未展开，则自动展开
      if (!sectionStates.value.academic) {
        sectionStates.value.academic = true;
      }
      
      console.log('学术背景信息已自动填充（仅学校和专业）');
    } else {
      console.log('未找到合适的教育背景信息');
    }
  } catch (error) {
    console.error('填充学术背景信息时发生错误:', error);
  }
};

// 清除选择的客户
const clearSelectedClient = () => {
  selectedClient.value = null;
  clientPrograms.value.clear();
  
  // 清空客户搜索相关状态
  clientSearchQuery.value = '';
  clientSearchResults.value = [];
  showClientSelector.value = false;
  
  // 清除悬浮框相关状态
  clientProgramsList.value = [];
  showClientProgramsFloat.value = true;
  
  // 清空学术背景表单
  if (profileForm && profileForm.academic) {
    profileForm.academic.school = '';
    profileForm.academic.major = '';
    profileForm.academic.gpa = '';
    profileForm.academic.gpaScale = '100'; // 重置为默认值
    
    // 清除相关的表单验证错误状态
    if (formValidation.hasValidated) {
      formValidation.errors.academic.school = false;
      formValidation.errors.academic.major = false;
      formValidation.errors.academic.gpa = false;
      formValidation.errors.academic.gpaScale = false;
    }
    
    // 清除GPA验证错误
    gpaValidationError.value = false;
    gpaErrorMessage.value = '';
    
    // 重新检查学术背景部分完成状态
    checkSectionCompletion('academic');
  }
  
  ElMessage.info('已清除客户选择和学术背景信息');
};

// 加载客户的定校书专业列表
const loadClientPrograms = async () => {
  if (!selectedClient.value) return;
  
  try {
    const response = await getClientPrograms(selectedClient.value.id_hashed);
    const programs = response.data || response || [];
    
    // 将专业ID添加到Set中，便于快速查询
    clientPrograms.value = new Set(programs.map(p => p.program_id || p.id));
    
    // 处理悬浮框的程序列表，合并基本信息和详细信息
    clientProgramsList.value = programs.map(program => {
      // 如果有详细信息，则使用详细信息，否则使用基本信息
      const details = program.program_details || {};
      
      return {
        // 基本的client_programs信息
        id: program.id,
        client_id: program.client_id,
        program_id: program.program_id,
        created_at: program.created_at,
        notes: program.notes,
        
        // 从ai_selection_programs表关联的详细信息
        school_name_cn: details.school_name_cn || program.school_name_cn || '未知学校',
        school_name_en: details.school_name_en || program.school_name_en || '',
        program_name_cn: details.program_name_cn || program.program_name_cn || '未知专业',
        program_name_en: details.program_name_en || program.program_name_en || '',
        program_category: details.program_category || program.program_category || '未知类别',
        school_region: details.school_region || program.region || program.school_region || '未知地区',
        degree: details.degree || program.degree || '硕士',
        school_qs_rank: details.school_qs_rank || '',
        program_website: details.program_website || '',
        gpa_requirements: details.gpa_requirements || '',
        language_requirements: details.language_requirements || '',
        application_requirements: details.application_requirements || '',
        program_tuition: details.program_tuition || '',
        enrollment_time: details.enrollment_time || '',
        program_duration: details.program_duration || ''
      };
    });
  } catch (error) {
    console.error('加载客户定校书失败:', error);
    clientPrograms.value.clear();
    clientProgramsList.value = [];
  }
};

// 切换定校书悬浮框展开/收起状态
const toggleClientProgramsFloat = () => {
  showClientProgramsFloat.value = !showClientProgramsFloat.value;
};

// 从定校书悬浮框中删除项目
const removeFromClientPrograms = async (program) => {
  if (!selectedClient.value || removingFromClientPrograms.value) return;
  
  removingFromClientPrograms.value = true;
  
  try {
    const programId = program.program_id || program.id;
    await removeClientProgram(selectedClient.value.id_hashed, programId);
    
    // 更新本地状态
    clientPrograms.value.delete(programId);
    clientProgramsList.value = clientProgramsList.value.filter(p => 
      (p.program_id || p.id) !== programId
    );
    
    ElMessage.success(`已从${selectedClient.value.name}的定校书中移除 ${program.school_name_cn} - ${program.program_name_cn}`);
  } catch (error) {
    console.error('从定校书删除项目失败:', error);
    ElMessage.error('删除失败，请重试');
  } finally {
    removingFromClientPrograms.value = false;
  }
};

// 检查专业是否已在客户定校书中
const isInClientPrograms = (program) => {
  if (!selectedClient.value) return false;
  const programId = program.program_id || program.id;
  return clientPrograms.value.has(programId);
};

// 切换专业在客户定校书中的状态
const toggleClientProgram = async (program) => {
  if (!selectedClient.value || addingToClientPrograms.value) return;
  
  const programId = program.program_id || program.id;
  const isInPrograms = isInClientPrograms(program);
  
  addingToClientPrograms.value = true;
  
  try {
    if (isInPrograms) {
      // 从定校书中移除
      await removeClientProgram(selectedClient.value.id_hashed, programId);
      clientPrograms.value.delete(programId);
      
      // 更新悬浮框的程序列表
      clientProgramsList.value = clientProgramsList.value.filter(p => 
        (p.program_id || p.id) !== programId
      );
      
      ElMessage.success(`已从${selectedClient.value.name}的定校书中移除该专业`);
    } else {
      // 添加到定校书
      const programData = {
        program_id: programId,
        school_name_cn: program.学校中文名,
        school_name_en: program.学校英文名,
        program_name_cn: program.专业中文名,
        program_name_en: program.专业英文名,
        program_category: program.专业大类,
        school_region: program.location,
        degree: program.degree || '硕士',
        // 可以添加更多字段
        notes: currentResultMode.value === 'ai' ? program.reason : '专业库匹配推荐'
      };
      
      await addClientProgram(selectedClient.value.id_hashed, programData);
      clientPrograms.value.add(programId);
      
      // 为悬浮框创建完整的数据结构（包含所有字段）
      const floatBoxData = {
        id: null, // 服务器会分配
        client_id: null, // 服务器会分配
        program_id: programId,
        created_at: new Date().toISOString(),
        notes: programData.notes,
        
        // 使用真实的专业数据
        school_name_cn: program.学校中文名,
        school_name_en: program.学校英文名,
        program_name_cn: program.专业中文名,
        program_name_en: program.专业英文名,
        program_category: program.专业大类,
        school_region: program.location,
        degree: program.degree || '硕士',
        school_qs_rank: program.ranking?.replace(/QS排名 #/, '') || '',
        program_website: program.项目官网 || '',
        gpa_requirements: program.GPA要求 || '',
        language_requirements: program.语言要求 || '',
        application_requirements: program.申请要求 || '',
        program_tuition: program.tuitionRange || '',
        enrollment_time: program.入学时间 || '',
        program_duration: program.项目时长 || ''
      };
      
      // 更新悬浮框的程序列表
      clientProgramsList.value.push(floatBoxData);
      
      ElMessage.success(`已添加到${selectedClient.value.name}的定校书中`);
    }
  } catch (error) {
    console.error('定校书操作失败:', error);
    ElMessage.error(isInPrograms ? '移除失败，请重试' : '添加失败，请重试');
  } finally {
    addingToClientPrograms.value = false;
  }
};

// 点击外部关闭客户选择器
const handleClickOutside = (event) => {
  const clientSelector = event.target.closest('.client-selector');
  if (!clientSelector) {
    showClientSelector.value = false;
  }
};

// 在组件挂载时添加全局点击监听
onMounted(async () => {
  
  // 初始化屏幕尺寸检查
  checkScreenSize();
  
  // 监听窗口大小变化，重新计算详情区域高度和屏幕尺寸
  window.addEventListener('resize', () => {
    recalculateHeights();
    checkScreenSize();
  });
  
  // 监听页面离开事件，清理SSE连接
  window.addEventListener('beforeunload', cleanupSSEConnection);
  
  // 添加全局点击监听器，用于关闭客户选择器
  document.addEventListener('click', handleClickOutside);
  
  // 页面可见性变化时的处理
  document.addEventListener('visibilitychange', () => {
    // 可以在这里处理页面可见性变化的逻辑
  });

  // 预加载一些常见学校的logo作为测试
  try {
    const commonSchools = [
      { 学校中文名: '香港大学' },
      { 学校中文名: '新加坡国立大学' },
      { 学校中文名: '帝国理工学院' },
      { 学校中文名: '伦敦大学学院' },
      { 学校中文名: '南洋理工大学' }
    ];
    await preloadSchoolLogos(commonSchools);
  } catch (error) {
    console.warn('预加载常见学校logo失败:', error);
  }
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', () => {
    recalculateHeights();
    checkScreenSize();
  });
  window.removeEventListener('beforeunload', cleanupSSEConnection);
  document.removeEventListener('click', handleClickOutside);
  cleanupSSEConnection();
});
</script> 

<style scoped>
/* === 选校匹配页面样式 === */
/* 页面容器 */
.school-assistant-container {
  min-height: 100vh;
  background-color: #F9FAFB;
  margin: 0;
  padding: 0;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 1.5rem;
  width: 100%;
}

/* === 顶部导航区 === */
.page-header {
  background: linear-gradient(135deg, #4F46E5 0%, #4338CA 100%);
  color: white;
  padding: 2rem 0;
  margin: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #FFFFFF;
}

.page-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0;
  color: #FFFFFF;
}

.stats-section {
  display: flex;
  gap: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #FFFFFF;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.8;
  color: #FFFFFF;
}

/* === 主要内容区 === */
.main-content {
  padding: 2rem 0;
}

/* === 响应式设计 === */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .page-header {
    padding: 1.5rem 0;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .stats-section {
    gap: 1rem;
  }

  .main-content {
    padding: 1.5rem 0;
  }
}

/* === 原有样式保留 === */
/* 全局覆盖Element Plus的主题色 - 不使用scoped */

/* 调整表单控件样式 */
:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper) {
  @apply !shadow-none bg-slate-50 border border-gray-200 hover:border-gray-300 transition-colors duration-150 rounded-md;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-select .el-input__wrapper.is-focus) {
  @apply bg-white border-[#4F46E5] ring-1 ring-[#4F46E5]/50;
  box-shadow: none !important;
}

/* 表单区域折叠/展开动画效果 */
.cursor-pointer {
  @apply hover:text-primary transition-colors duration-200;
}

/* 扩展图标的旋转动画 */
.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.3s ease-in-out;
}
.material-icons-outlined.text-gray-400 {
  transition: transform 0.3s ease-in-out;
}

/* 折叠区域过渡效果 - 改进以防止抖动 */
.form-section-content {
  transition: max-height 0.4s ease-in-out, opacity 0.3s ease-in-out;
  will-change: max-height, opacity;
  overflow: hidden;
  position: relative;
}

/* 确保整个表单容器具有固定宽度，防止滚动条引起的布局变化 */
.pro-card {
  @apply overflow-x-hidden bg-white rounded-lg shadow-sm border border-gray-100;
  width: 100%;
  margin-bottom: 1rem;
}

.pro-card-header {
  @apply border-b border-gray-100 flex items-center;
  padding: 0 16px;
  height: 3.5rem;
}

.pro-card-title {
  @apply text-gray-800 font-medium flex items-center;
  font-weight: 600;
}

.pro-card-body {
  @apply p-4;
}

.pro-card-footer {
  @apply p-3.5 border-t border-gray-100 flex justify-end;
}

/* 学校Logo样式 */
.school-logo-container {
  @apply flex-shrink-0 w-14 h-14 bg-white rounded-lg flex items-center justify-center overflow-hidden mr-4 border border-gray-100 shadow-sm;
}

.school-logo-container img {
  @apply h-10 w-10 object-contain;
}

.school-logo-container span {
  @apply text-2xl font-bold text-gray-400;
  line-height: 1;
}

/* 优化Logo显示 */
.school-card .school-logo-container {
  @apply bg-gray-50;
}

.school-card .school-logo-container img {
  @apply max-w-full max-h-full p-1;
  filter: grayscale(0.1);
}

.school-card:hover .school-logo-container img {
  filter: grayscale(0);
}

/* 浮动动画效果 */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* 学校卡片样式增强 */
.school-card {
  @apply transform transition-all duration-300;
}

.school-card:hover {
  @apply shadow-lg -translate-y-1 border-[#4F46E5]/20;
}

/* 匹配度标签动画 */
.school-card .px-2\.5 {
  @apply transition-all duration-300;
}

.school-card:hover .px-2\.5 {
  @apply scale-105;
}

/* 优化按钮悬停效果 */
.el-button:hover {
  @apply transform scale-105 transition-transform duration-200;
}

/* === 现代化QS排名组件样式 === */
.ranking-badge {
  @apply relative flex items-center justify-center space-x-2 px-3 py-2 rounded-xl border shadow-sm backdrop-blur-sm;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%) !important;
  border-color: rgba(251, 191, 36, 0.4) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 5rem;
  height: 2.25rem;
  align-self: center; /* 确保垂直居中 */
}

.ranking-badge:hover {
  @apply shadow-md -translate-y-0.5 scale-105;
  background: linear-gradient(135deg, #fde68a 0%, #fcd34d 100%) !important;
  border-color: rgba(251, 191, 36, 0.6) !important;
}

.ranking-label {
  @apply text-xs font-semibold text-slate-500 tracking-wide uppercase leading-none;
  font-size: 0.6rem;
  letter-spacing: 0.05em;
}

.ranking-number {
  @apply text-sm font-bold text-slate-700 leading-none;
  font-feature-settings: 'tnum';
}



/* === 响应式设计优化 === */
@media (max-width: 640px) {
  .ranking-badge {
    @apply px-2 py-1.5 space-x-1.5 min-w-[3.5rem];
    height: 2rem;
  }
  
  .ranking-label {
    font-size: 0.55rem;
  }
  
  .ranking-number {
    @apply text-xs;
  }
  

}

/* 覆盖 Element Plus 下拉选择框的蓝色主题为紫色 */
:deep(.el-select .el-select__wrapper.is-focused) {
  @apply !border-[#4F46E5];
  box-shadow: 0 0 0 1px #4F46E5 inset !important;
}
:deep(.el-select-dropdown__item.is-selected.is-hovering) {
  @apply !bg-[#4F46E5]/20;
}

/* 多选标签 */
:deep(.el-select .el-tag.is-info) {
  @apply !bg-[#4F46E5]/10 !text-[#4F46E5] !border-[#4F46E5]/20;
}

/* 多选标签的关闭按钮 */
:deep(.el-select .el-tag.is-info .el-tag__close) {
  @apply !text-[#4F46E5] hover:!bg-[#4F46E5]/20;
}

/* 禁用覆盖Element Plus的默认主题色变量 */
:deep(.el-select) {
  --el-color-primary: #4F46E5 !important;
  --el-color-primary-light-3: #7C7AED !important;
  --el-color-primary-light-5: #9D9BF2 !important;
  --el-color-primary-light-7: #BEBDF7 !important;
  --el-color-primary-light-8: #CFCEF9 !important;
  --el-color-primary-light-9: #DFDFFB !important;
  --el-color-primary-dark-2: #3F37B7 !important;
}

/* 全局覆盖Element Plus主题色 */
:root {
  --el-color-primary: #4F46E5 !important;
}

/* === 响应式布局优化（MacBook等中等屏幕） === */
@media (min-width: 1024px) and (max-width: 1279px) {
  /* 在lg到xl之间的屏幕（如MacBook）上的优化 */
  .collapsed-form {
    font-size: 0.875rem;
  }
  
  .collapsed-form .animated-input-container {
    margin-bottom: 0.375rem;
  }
  
  .collapsed-form .px-4 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  
  .collapsed-form .py-3 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
  
  /* 在收起状态下减小标题字体 */
  .collapsed-form h4 {
    font-size: 0.8125rem;
  }
  
  /* 优化按钮间距 */
  .collapsed-form .pro-card-footer {
    padding: 0.75rem 1rem;
  }
  
  /* 优化网格布局间距 */
  .collapsed-form .grid.grid-cols-2 {
    gap: 0.75rem;
  }
}

/* 确保过渡动画流畅 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* 专业卡片特殊样式 */
.program-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.program-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.program-card .details-container {
  transition: height 0.4s cubic-bezier(0.4, 0, 0.2, 1), 
              opacity 0.3s ease;
  overflow: hidden !important;
  will-change: height, opacity;
}

/* 收藏按钮悬停效果 */
.program-card .el-button:hover {
  transform: translateY(-1px);
}

/* 学校分组卡片样式 */
.school-group-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.school-group-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.school-group-header {
  transition: background-color 0.2s ease;
}

.school-group-header:hover {
  background: linear-gradient(90deg, #f8fafc 0%, #ffffff 50%, #f8fafc 100%);
}

.school-programs-container {
  transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1), 
              opacity 0.3s ease;
}

/* 优化边框颜色 */
.border-gray-150 {
  border-color: #f1f3f4;
}

.bg-gray-25 {
  background-color: #fafbfc;
}

/* 确保过渡动画流畅 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* 客户档案下拉框样式 */
.client-dropdown {
  background: white !important;
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid #e5e7eb !important;
}

.client-selector {
  position: relative;
  z-index: 1;
}

/* 客户档案区域特殊处理 - 允许下拉框溢出 */
.client-section-content {
  overflow: visible !important;
}

/* 定校书悬浮框样式 */
.client-programs-float {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(229, 231, 235, 0.8);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 
              0 10px 10px -5px rgba(0, 0, 0, 0.04),
              0 0 0 1px rgba(79, 70, 229, 0.05);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: float-in 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.client-programs-float:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15), 
              0 10px 20px -5px rgba(0, 0, 0, 0.1),
              0 0 0 1px rgba(79, 70, 229, 0.1);
  transform: translateY(-3px) scale(1.02);
}

/* 悬浮框内容展开/收起动画 */
.float-content-expanded {
  max-height: 450px;
  opacity: 1;
  transform: scaleY(1);
  transform-origin: top;
}

.float-content-collapsed {
  max-height: 0px;
  opacity: 0;
  transform: scaleY(0.95);
  transform-origin: top;
  pointer-events: none;
}

/* 悬浮框入场动画 */
@keyframes float-in {
  0% {
    opacity: 0;
    transform: translateY(20px) translateX(20px) scale(0.9);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-5px) translateX(-2px) scale(1.05);
  }
  100% {
    opacity: 1;
    transform: translateY(0) translateX(0) scale(1);
  }
}

/* 项目列表项入场动画 */
@keyframes slide-in-item {
  0% {
    opacity: 0;
    transform: translateX(-15px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* 响应式调整悬浮框位置 */
@media (max-width: 768px) {
  .client-programs-float {
    position: fixed !important;
    bottom: 1rem !important;
    right: 1rem !important;
    left: 1rem !important;
    width: auto !important;
    max-width: none !important;
  }
}

@media (max-width: 480px) {
  .client-programs-float {
    bottom: 0.5rem !important;
    right: 0.5rem !important;
    left: 0.5rem !important;
  }
}

/* 确保悬浮框在所有元素之上 */
.client-programs-float {
  z-index: 9999 !important;
}
</style>

<style>
/* 全局覆盖Element Plus的主题色 - 不使用scoped */
.el-select-dropdown__item.is-hovering {
  background-color: rgba(79, 70, 229, 0.1) !important;
  color: #4F46E5 !important;
}

.el-select-dropdown__item.is-selected {
  color: #4F46E5 !important;
  font-weight: 500 !important;
}

.el-select-dropdown__item.is-selected::after {
  background: #4F46E5 !important;
}

.el-select-dropdown__item.is-selected.is-hovering {
  background-color: rgba(79, 70, 229, 0.2) !important;
  color: #4F46E5 !important;
}

/* 成绩制式默认百分制的淡色样式 */
.el-select .el-input__inner::placeholder {
  color: #9ca3af !important;
}

.el-select .el-input__inner {
  color: #6b7280 !important;
}

/* AI选校复选框自定义样式 */
.ai-selection-checkbox .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
}

.ai-selection-checkbox .el-checkbox__input.is-focus .el-checkbox__inner {
  border-color: #4F46E5 !important;
}

.ai-selection-checkbox .el-checkbox__inner:hover {
  border-color: #4F46E5 !important;
}
</style> 