<template>
  <div class="program-database-container">
    <!-- 1. 顶部导航区: 面包屑 + 页面标题 -->
    <div class="page-header">
      <div class="container">
        <div class="header-content">
          <div class="title-section">
            <h1 class="page-title">专业库</h1>
            <p class="page-subtitle">探索全球优质院校专业项目</p>
          </div>
          <div class="stats-section">
            <div class="stat-item">
              <div class="stat-number">{{ statistics.total_programs || 0 }}</div>
              <div class="stat-label">专业项目</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ statistics.total_schools || 0 }}</div>
              <div class="stat-label">合作院校</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ statistics.total_regions || 0 }}</div>
              <div class="stat-label">留学地区</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 2. 主要内容区: 左右布局 -->
    <div class="main-content">
      <div class="container">
        <div class="flex flex-col xl:flex-row gap-6">
          <!-- 左侧：搜索与筛选 -->
          <div 
            class="xl:w-96 xl:flex-shrink-0 transition-all duration-300 ease-in-out"
            :class="{
              'lg:w-full': !sidebarCollapsed || !isLargeScreen,
              'lg:w-80': sidebarCollapsed && isLargeScreen,
              'lg:flex-shrink-0': sidebarCollapsed && isLargeScreen
            }"
          >
            <div class="pro-card">
              <div class="pro-card-header flex justify-between items-center">
                <div class="pro-card-title">
                  <span class="material-icons-outlined icon">search</span>
                  搜索与筛选
                </div>
                <!-- 在大屏幕上显示折叠按钮 -->
                <button 
                  v-if="isLargeScreen && !isExtraLargeScreen"
                  @click="toggleSidebar"
                  class="lg:block xl:hidden text-gray-500 hover:text-gray-700 p-1 rounded-md hover:bg-gray-100 transition-colors duration-200"
                >
                  <span class="material-icons-outlined text-sm">
                    {{ sidebarCollapsed ? 'keyboard_arrow_right' : 'keyboard_arrow_left' }}
                  </span>
                </button>
              </div>
              <div class="pro-card-body">
                <!-- 搜索栏 -->
                <div class="search-section">
                  <div class="search-container">
                    <FloatingLabelInput
                      v-model="searchForm.keyword"
                      label="智能搜索学校、专业"
                      placeholder="例如：哈佛大学、金融、商科、CS..."
                      prefix-icon="search"
                      clearable
                      size="large"
                      class="search-input"
                      @input="handleSearch"
                      @clear="handleClearSearch"
                    />
                    <div class="search-tip">
                      <span class="material-icons-outlined">auto_awesome</span>
                      支持中英文、简称、专业领域关键词匹配
                    </div>
                  </div>
                </div>

                <!-- 筛选栏 -->
                <div class="filter-section">

                  <!-- 地区筛选 -->
                  <div class="filter-group">
                    <div class="filter-group-header">
                      <span class="material-icons-outlined">location_on</span>
                      留学地区
                    </div>
                    <div class="filter-tags">
                      <el-tag
                        :type="searchForm.regions.length === 0 ? 'primary' : undefined"
                        @click="handleRegionClick('')"
                        :class="[
                          'filter-tag',
                          { 'tag-selected': searchForm.regions.length === 0 }
                        ]"
                      >
                        不限
                      </el-tag>
                      <el-tag
                        v-for="region in regions"
                        :key="region.name"
                        :type="searchForm.regions.includes(region.name) ? 'primary' : undefined"
                        @click="handleRegionClick(region.name)"
                        :class="[
                          'filter-tag',
                          { 'tag-selected': searchForm.regions.includes(region.name) }
                        ]"
                      >
                        {{ region.name }}
                      </el-tag>
                    </div>
                  </div>

                  <!-- 专业大类筛选 -->
                  <div class="filter-group">
                    <div class="filter-group-header">
                      <span class="material-icons-outlined">category</span>
                      专业大类
                    </div>
                    <div class="filter-tags">
                      <el-tag
                        v-for="category in categories"
                        :key="category.name"
                        :type="selectedCategory === category.name ? 'primary' : undefined"
                        @click="handleCategoryClick(category.name)"
                        :class="[
                          'filter-tag', 
                          'category-tag',
                          { 'tag-selected': selectedCategory === category.name }
                        ]"
                      >
                        <span class="category-name">{{ category.name }}</span>
                        <span class="category-arrow" v-if="selectedCategory === category.name">
                          <span class="material-icons-outlined">keyboard_arrow_up</span>
                        </span>
                        <span class="category-arrow" v-else>
                          <span class="material-icons-outlined">keyboard_arrow_down</span>
                        </span>
                      </el-tag>
                    </div>
                  </div>

                  <!-- 专业方向标签 -->
                  <div v-if="selectedCategory" class="filter-group">
                    <div class="filter-group-header">
                      <span class="material-icons-outlined">label</span>
                      {{ selectedCategory }}专业方向
                    </div>
                    <div class="filter-tags">
                      <el-tag
                        v-for="direction in filteredDirections"
                        :key="direction.name"
                        :type="searchForm.program_directions.includes(direction.name) ? 'primary' : undefined"
                        @click="handleDirectionClick(direction.name)"
                        :class="[
                          'filter-tag',
                          { 'tag-selected': searchForm.program_directions.includes(direction.name) }
                        ]"
                      >
                        {{ direction.name }}
                      </el-tag>
                    </div>
                  </div>

                  <!-- 已选择的专业方向显示 -->
                  <div v-if="searchForm.program_directions.length > 0" class="filter-group">
                    <div class="filter-group-header">
                      <span class="material-icons-outlined">done_all</span>
                      已选择的专业方向 ({{ searchForm.program_directions.length }})
                    </div>
                    <div class="selected-tags">
                      <div class="selected-tag-wrapper">
                        <span class="category-prefix">{{ selectedCategory }}：</span>
                        <div class="selected-tags-container">
                          <el-tag
                            v-for="direction in searchForm.program_directions"
                            :key="direction"
                            type="primary"
                            closable
                            @close="handleDirectionRemove(direction)"
                            class="selected-tag"
                          >
                            {{ direction }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 重置按钮 -->
                  <div class="filter-actions">
                    <el-button @click="handleResetFilters" class="btn-secondary action-btn">
                      <span class="material-icons-outlined">refresh</span>
                      重置所有筛选
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：数据展示 -->
          <div class="flex-1 min-w-0">
            <div class="pro-card">
              <!-- 结果统计栏 -->
              <div class="pro-card-header">
                <div class="result-info">
                  <span class="material-icons-outlined">dataset</span>
                  共找到 <strong>{{ pagination.total }}</strong> 个专业项目
                </div>
              </div>

              <!-- 数据内容区域 -->
              <div class="pro-card-body">
                <!-- 固定高度容器避免抖动 -->
                <div class="content-container">
                  <!-- Loading状态 -->
                  <transition name="fade" mode="out-in">
                    <div v-if="loading" key="loading" class="loading-container">
                      <el-skeleton animated>
                        <template #template>
                          <div class="skeleton-grid">
                            <div 
                              v-for="n in 6" 
                              :key="n"
                              class="skeleton-card"
                            >
                              <el-skeleton-item variant="image" style="width: 100%; height: 200px;" />
                              <div class="skeleton-content">
                                <el-skeleton-item variant="h3" style="width: 80%;" />
                                <el-skeleton-item variant="text" style="width: 60%;" />
                                <el-skeleton-item variant="text" style="width: 90%;" />
                              </div>
                            </div>
                          </div>
                        </template>
                      </el-skeleton>
                    </div>

                    <!-- 卡片视图 -->
                    <div v-else-if="programs.length > 0" key="content" class="cards-container">
                      <transition-group name="fade-list" tag="div" class="cards-grid">
                        <div
                          v-for="program in programs"
                          :key="program.id"
                          class="program-card pro-card hover-scale transition-standard"
                          @click="handleViewDetails(program)"
                        >
                          <!-- 卡片头部：学校信息 -->
                          <div class="card-header">
                            <div class="school-info-section">
                              <div class="school-logo-container">
                                <img 
                                  :src="getSchoolLogo(program.school_name_cn)" 
                                  :alt="program.school_name_cn + ' Logo'"
                                  class="school-logo"
                                  @error="handleLogoError"
                                />
                              </div>
                              <div class="school-content">
                                <div class="school-name">{{ program.school_name_cn }}</div>
                                <div class="school-meta">
                                  <span class="region">{{ program.school_region }}</span>
                                  <span v-if="program.school_qs_rank" class="qs-rank">
                                    QS {{ program.school_qs_rank }}
                                  </span>
                                </div>
                              </div>
                            </div>
                            <div class="degree-badge">
                              {{ program.degree }}
                            </div>
                          </div>

                          <!-- 卡片内容：专业信息 -->
                          <div class="card-body">
                            <div class="program-title-section">
                              <h3 class="program-name">{{ program.program_name_cn }}</h3>
                              <p v-if="program.program_name_en" class="program-name-en">
                                {{ program.program_name_en }}
                              </p>
                            </div>
                            
                            <div class="program-meta-grid">
                              <div class="meta-item" v-if="program.program_direction">
                                <span class="meta-label">专业方向</span>
                                <span class="meta-value">{{ program.program_direction }}</span>
                              </div>
                              <div class="meta-item" v-if="program.faculty">
                                <span class="meta-label">所属学院</span>
                                <span class="meta-value">{{ program.faculty }}</span>
                              </div>
                              <div class="meta-item" v-if="program.program_duration">
                                <span class="meta-label">学制</span>
                                <span class="meta-value">{{ program.program_duration }}</span>
                              </div>
                            </div>

                            <!-- 重要信息标签 -->
                            <div class="info-tags" v-if="program.program_tuition">
                              <div v-if="program.program_tuition" class="info-tag tuition">
                                <span class="material-icons-outlined">payments</span>
                                <span>{{ program.program_tuition }}</span>
                              </div>
                            </div>
                          </div>

                          <!-- 卡片底部：操作按钮 -->
                          <div class="card-footer">
                            <el-button 
                              v-if="program.program_website" 
                              size="small" 
                              @click.stop="handleViewWebsite(program)" 
                              class="btn-website"
                            >
                              <span class="material-icons-outlined">language</span>
                              查看官网
                            </el-button>
                            <el-button 
                              size="small" 
                              @click.stop="handleCollect(program)" 
                              class="btn-collect"
                            >
                              <span class="material-icons-outlined">bookmark_border</span>
                              收藏
                            </el-button>
                          </div>
                        </div>
                      </transition-group>
                    </div>

                    <!-- 空状态 -->
                    <div v-else key="empty" class="empty-state">
                      <div class="empty-content">
                        <span class="material-icons-outlined empty-icon">search_off</span>
                        <h3>未找到相关专业</h3>
                        <p>请尝试调整搜索条件或筛选条件</p>
                        <el-button type="primary" @click="handleResetFilters" class="btn-primary">
                          重置筛选条件
                        </el-button>
                      </div>
                    </div>
                  </transition>
                </div>

                <!-- 分页组件 -->
                <transition name="fade">
                  <div v-if="!loading && programs.length > 0" class="pagination-container">
                    <div class="pagination-wrapper">
                      <div class="pagination-info">
                        <span class="info-text">
                          显示第 {{ (pagination.currentPage - 1) * pagination.pageSize + 1 }} - 
                          {{ Math.min(pagination.currentPage * pagination.pageSize, pagination.total) }} 条，
                          共 {{ pagination.total }} 条专业项目
                        </span>
                      </div>
                      <el-pagination
                        v-model:current-page="pagination.currentPage"
                        v-model:page-size="pagination.pageSize"
                        :page-sizes="[6, 12, 24, 48]"
                        :total="pagination.total"
                        layout="sizes, prev, pager, next, jumper"
                        @current-change="handlePageChange"
                        @size-change="handlePageSizeChange"
                        class="custom-pagination"
                      />
                    </div>
                  </div>
                </transition>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 专业详情弹窗 -->
    <program-detail-dialog
      v-model:visible="detailDialogVisible"
      :program-id="selectedProgramId"
      @collect="handleCollect"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  getProgramList, 
  getRegionList, 
  getProgramStatistics,
  getProgramCategories,
  getProgramDirections,
  getProgramCount
} from '@/api/programs'
import ProgramDetailDialog from '@/components/ProgramDetailDialog.vue'
import FloatingLabelInput from '@/components/common/FloatingLabelInput.vue'
import { getSchoolLogo as getSchoolLogoFallback } from '@/utils/schoolLogos'

// 响应式数据
const loading = ref(true) // 初始设为true，避免加载时闪烁空状态
const programs = ref([])
const regions = ref([])
const categories = ref([])
const directions = ref([])
const statistics = ref({})

// 学校Logo缓存
const schoolLogosCache = ref(new Map())

// 响应式布局状态
const sidebarCollapsed = ref(false)
const isLargeScreen = ref(false)
const isExtraLargeScreen = ref(false)

// 检查屏幕尺寸
const checkScreenSize = () => {
  const width = window.innerWidth
  const newIsLargeScreen = width >= 1024 // lg断点
  const newIsExtraLargeScreen = width >= 1280 // xl断点
  
  // 只有当屏幕尺寸状态真正改变时才更新，避免不必要的重新渲染
  if (newIsLargeScreen !== isLargeScreen.value) {
    isLargeScreen.value = newIsLargeScreen
  }
  
  if (newIsExtraLargeScreen !== isExtraLargeScreen.value) {
    isExtraLargeScreen.value = newIsExtraLargeScreen
  }
  
  // 在xl以下的大屏幕上默认收起侧边栏以节省空间
  if (newIsLargeScreen && !newIsExtraLargeScreen && !sidebarCollapsed.value) {
    sidebarCollapsed.value = true
  }
  // 在xl及以上或小屏幕上展开侧边栏
  else if (!newIsLargeScreen || newIsExtraLargeScreen) {
    if (sidebarCollapsed.value) {
      sidebarCollapsed.value = false
    }
  }
}

// 防抖处理屏幕尺寸检查
let resizeTimer = null
const debouncedCheckScreenSize = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  resizeTimer = setTimeout(checkScreenSize, 150) // 150ms防抖延迟
}

// 切换侧边栏状态
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 12, // 每页显示12个项目
  total: 0
})

// 筛选状态
const selectedCategory = ref('')

// 搜索表单
const searchForm = reactive({
  keyword: '',
  regions: [], // 改为数组支持多选
  program_category: '',
  program_directions: [] // 改为数组支持多选
})

// 显示名称到数据库值的映射
const regionDbMap = {
  "中国香港": "香港",
  "中国澳门": "澳门",
  "新加坡": "新加坡",
  "英国": "英国",
  "美国": "美国", 
  "澳大利亚": "澳大利亚",
  "马来西亚": "马来西亚"
}

// 计算属性：根据选中的专业大类筛选专业方向，并将"其他"选项排在最后
const filteredDirections = computed(() => {
  if (!selectedCategory.value) return []
  
  // 根据专业大类筛选相关的专业方向
  // 这里可以根据实际数据建立映射关系
  return directions.value.filter(direction => {
    // 根据实际专业方向数据建立精确的映射关系
    const categoryKeywords = {
      '商科': [
        '金工金数', '金融', '商业分析', '经济', '会计', '市场营销', '信息系统', 
        '管理', '人力资源管理', '供应链管理', '创业与创新', '房地产', 
        '旅游酒店管理', '工商管理', '其他商科'
      ],
      '工科': [
        '计算机', '电气电子', '机械工程', '材料', '化工', '生物工程', 
        '土木工程', '工程管理', '环境工程', '工业工程', '能源', 
        '航空工程', '地球科学', '交通运输', '海洋技术', '食品科学', '其他工科'
      ],
      '理科': [
        '物理', '化学', '数学', '生物', '数据科学', '其他理科'
      ],
      '社科': [
        '教育', '建筑', '法律', '社会学与社工', '国际关系', '哲学', '历史', 
        '公共政策与事务', '艺术', '公共卫生', '心理学', '体育', '药学', '医学', 
        '新闻', '影视', '文化', '媒体与传播', '新媒体', '媒介与社会', 
        '科学传播', '策略传播', '媒体产业', '语言', '其他社科'
      ]
    }
    
    const keywords = categoryKeywords[selectedCategory.value] || []
    return keywords.includes(direction.name)
  }).sort((a, b) => {
    // 将"其他"选项排在最后
    const aIsOther = a.name.startsWith('其他')
    const bIsOther = b.name.startsWith('其他')
    
    if (aIsOther && !bIsOther) return 1
    if (!aIsOther && bIsOther) return -1
    return 0
  })
})

// 详情弹窗
const detailDialogVisible = ref(false)
const selectedProgramId = ref(null)

// 防抖搜索定时器
let searchTimer = null

// --- 增强搜索逻辑 ---

// 学校关键词，用于解析和扩展搜索
const schoolKeywords = {
  '哈佛': ['哈佛大学', 'Harvard'],
  '斯坦福': ['斯坦福大学', 'Stanford'],
  '麻省理工': ['麻省理工学院', 'MIT'],
  '牛津': ['牛津大学', 'Oxford'],
  '剑桥': ['剑桥大学', 'Cambridge'],
  '清华': ['清华大学', 'Tsinghua'],
  '北大': ['北京大学', 'Peking University'],
  '港大': ['香港大学', 'University of Hong Kong', 'HKU'],
  '港科大': ['香港科技大学', 'HKUST'],
  '中大': ['香港中文大学', 'CUHK'],
  '新国立': ['新加坡国立大学', 'NUS'],
  '南洋理工': ['南洋理工大学', 'NTU'],
  '布里斯托': ['布里斯托大学', 'Bristol'],
  '南安普顿': ['南安普顿大学', 'Southampton'],
  'ucl': ['伦敦大学学院', 'University College London'],
  '帝国理工': ['帝国理工学院', 'Imperial College London'],
  '爱丁堡': ['爱丁堡大学', 'Edinburgh'],
  '曼大': ['曼彻斯特大学', 'Manchester'],
  'kcl': ["伦敦国王学院", "King's College London"],
  '华威': ['华威大学', 'Warwick'],
  '谢菲尔德': ['谢菲尔德大学', 'Sheffield'],
  '杜伦': ['杜伦大学', 'Durham'],
  '伯明翰': ['伯明翰大学', 'Birmingham'],
  '利兹': ['利兹大学', 'Leeds'],
  '诺丁汉': ['诺丁汉大学', 'Nottingham'],
  '格拉斯哥': ['格拉斯哥大学', 'Glasgow']
}

// 专业领域关键词
const subjectKeywords = {
  '商': ['商科', '商业', '管理', '工商管理'],
  '金融': ['金融', '财务', '经济', '银行', 'Finance'],
  '会计': ['会计', '财务', 'Accounting'],
  '管理': ['管理', '工商管理', 'Management', 'MBA'],
  '市场': ['市场营销', '营销', 'Marketing'],
  '经济': ['经济', '经济学', 'Economics'],
  '计算机': ['计算机', '软件', '编程', 'Computer Science', 'CS'],
  '软件': ['软件', '计算机', '编程', 'Software'],
  '电子': ['电子', '电气', '电子工程', 'Electronic'],
  '机械': ['机械', '机械工程', 'Mechanical'],
  '土木': ['土木', '建筑', 'Civil Engineering'],
  '数学': ['数学', '统计', 'Mathematics', 'Statistics'],
  '物理': ['物理', '物理学', 'Physics'],
  '化学': ['化学', '化学工程', 'Chemistry'],
  '生物': ['生物', '生命科学', 'Biology'],
  '心理': ['心理学', '心理', 'Psychology'],
  '教育': ['教育', '教育学', 'Education'],
  '法律': ['法律', '法学', 'Law'],
  '传媒': ['传媒', '新闻', '传播', 'Media', 'Communication'],
}

// 合并所有关键词映射
const allKeywords = { ...schoolKeywords, ...subjectKeywords }

/**
 * 智能解析搜索查询
 * @param {string} keyword - 用户输入的搜索字符串
 * @returns {string[]} 解析后的关键词数组
 */
const parseSearchQuery = (keyword) => {
  const trimmedKeyword = keyword.trim()
  const lowerKeyword = trimmedKeyword.toLowerCase()

  // 1. 按空格分割
  const parts = trimmedKeyword.split(/\s+/).filter(Boolean)
  if (parts.length > 1) {
    return parts
  }

  // 2. 如果没有空格，尝试根据学校名称分割
  const allSchoolNames = Object.entries(schoolKeywords)
    .flatMap(([key, values]) => [key, ...values])
    .map(v => v.toLowerCase())
  allSchoolNames.sort((a, b) => b.length - a.length) // 优先匹配长名称

  for (const schoolName of allSchoolNames) {
    if (lowerKeyword.startsWith(schoolName)) {
      const schoolPart = trimmedKeyword.substring(0, schoolName.length)
      const restPart = trimmedKeyword.substring(schoolName.length).trim()
      if (restPart) {
        return [schoolPart, restPart]
      }
    }
  }

  // 3. 无法分割，返回原始关键词
  return [trimmedKeyword]
}

// 智能关键词生成函数
const generateSmartKeywords = (keyword) => {
  const smartKeywords = new Set([keyword])
  const lowerKeyword = keyword.toLowerCase()
  
  // 使用重构后的关键词库
  for (const [key, synonyms] of Object.entries(allKeywords)) {
    if (lowerKeyword.includes(key.toLowerCase()) || 
        synonyms.some(syn => lowerKeyword.includes(syn.toLowerCase()))) {
      synonyms.forEach(syn => smartKeywords.add(syn))
    }
  }
  
  // 处理常见的简写
  const abbreviations = {
    'cs': '计算机',
    'ai': '人工智能',
    'ml': '机器学习',
    'mba': '工商管理',
    'phd': '博士',
    'msc': '硕士',
    'ba': '学士'
  }
  
  if (abbreviations[lowerKeyword]) {
    smartKeywords.add(abbreviations[lowerKeyword])
  }
  
  return Array.from(smartKeywords)
}

/**
 * 获取单个关键词的搜索结果
 * @param {string} term - 单个搜索关键词
 * @param {object} baseParams - 基础查询参数 (已包含筛选)
 * @param {boolean} directionsInForm - 表单中是否已选择了专业方向
 * @returns {Promise<Array>} 匹配该关键词的专业列表
 */
const getProgramsForTerm = async (term, baseParams, directionsInForm) => {
    const searchPromises = []
    
    // 多维度搜索：学校名称、专业名称、专业方向、专业大类
    searchPromises.push(
      getProgramList({ ...baseParams, school_name: term, limit: 50000 }),
      getProgramList({ ...baseParams, program_name: term, limit: 50000 })
    )
    
    // 如果没有通过筛选指定专业方向，才进行专业方向关键词搜索
    if (!directionsInForm) {
      searchPromises.push(
        getProgramList({ ...baseParams, program_direction: term, limit: 50000 })
      )
    }
    
    if (/[a-zA-Z]/.test(term)) {
      searchPromises.push(
        getProgramList({ ...baseParams, school_name_en: term, limit: 50000 }),
        getProgramList({ ...baseParams, program_name_en: term, limit: 50000 })
      )
    }
    
    const smartKeywords = generateSmartKeywords(term)
    for (const smartKeyword of smartKeywords) {
      if (smartKeyword !== term) {
        if (!directionsInForm) {
          searchPromises.push(
            getProgramList({ ...baseParams, program_direction: smartKeyword, limit: 50000 })
          )
        }
        searchPromises.push(
          getProgramList({ ...baseParams, program_category: smartKeyword, limit: 50000 })
        )
      }
    }
    
    const allSearchResults = await Promise.all(searchPromises)
    const allResults = allSearchResults.flat()
    return allResults.filter((program, index, self) => 
        index === self.findIndex(p => p.id === program.id))
}

// 相关性评分函数
const calculateRelevanceScore = (program, keyword) => {
  let score = 0
  const lowerKeyword = keyword.toLowerCase()
  
  // 精确匹配得分最高
  if (program.school_name_cn?.toLowerCase().includes(lowerKeyword)) score += 100
  if (program.program_name_cn?.toLowerCase().includes(lowerKeyword)) score += 90
  if (program.program_direction?.toLowerCase().includes(lowerKeyword)) score += 80
  if (program.program_category?.toLowerCase().includes(lowerKeyword)) score += 70
  
  // 英文匹配
  if (program.school_name_en?.toLowerCase().includes(lowerKeyword)) score += 95
  if (program.program_name_en?.toLowerCase().includes(lowerKeyword)) score += 85
  
  // 开头匹配加分
  if (program.school_name_cn?.toLowerCase().startsWith(lowerKeyword)) score += 50
  if (program.program_name_cn?.toLowerCase().startsWith(lowerKeyword)) score += 40
  
  // 完全匹配超高分
  if (program.school_name_cn?.toLowerCase() === lowerKeyword) score += 200
  if (program.program_name_cn?.toLowerCase() === lowerKeyword) score += 180
  
  // QS排名加分（排名越好加分越多）
  if (program.school_qs_rank && program.school_qs_rank !== 'null') {
    const rank = parseInt(program.school_qs_rank)
    if (rank <= 50) score += 30
    else if (rank <= 100) score += 20
    else if (rank <= 200) score += 10
  }
  
  return score
}

// 页面初始化
onMounted(async () => {
  // 初始化屏幕尺寸检查
  checkScreenSize()
  
  // 监听窗口大小变化
  window.addEventListener('resize', debouncedCheckScreenSize)
  
  // 先加载地区数据，确保按正确顺序显示
  await loadRegions()
  
  // 然后并行加载其他数据
  await Promise.all([
    loadPrograms(),
    loadCategories(),
    loadDirections(),
    loadStatistics()
  ])
  
  // 预加载一些常见学校的logo作为测试
  try {
    const commonSchools = [
      { school_name_cn: '香港大学' },
      { school_name_cn: '新加坡国立大学' },
      { school_name_cn: '帝国理工学院' },
      { school_name_cn: '伦敦大学学院' },
      { school_name_cn: '南洋理工大学' },
      { school_name_cn: '香港中文大学' },
      { school_name_cn: '香港科技大学' },
      { school_name_cn: '牛津大学' },
      { school_name_cn: '剑桥大学' },
      { school_name_cn: '哈佛大学' }
    ];
    await preloadSchoolLogos(commonSchools);
  } catch (error) {
    console.warn('预加载常见学校logo失败:', error);
  }
})

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', debouncedCheckScreenSize)
})

// 加载专业列表
const loadPrograms = async (resetPage = false) => {
  loading.value = true
  
  // 如果是新搜索，重置到第一页
  if (resetPage) {
    pagination.currentPage = 1
  }
  
  try {
    const params = {
      limit: pagination.pageSize,
      offset: (pagination.currentPage - 1) * pagination.pageSize,
      ...Object.fromEntries(
        Object.entries(searchForm).filter(([key, value]) => {
          if (key === 'keyword') {
            // 关键词搜索可以匹配学校名称和专业名称
            return value && value.trim()
          }
          if (key === 'program_directions') {
            // 专业方向数组，如果有选择则处理
            return Array.isArray(value) && value.length > 0
          }
          if (key === 'regions') {
            // 地区数组，如果有选择则处理
            return Array.isArray(value) && value.length > 0
          }
          return value && value.trim()
        })
      )
    }

    // --- 增强的关键词搜索逻辑 ---
    if (params.keyword) {
      const keyword = params.keyword.trim()
      
      // 准备基础查询参数 (包含地区和专业方向筛选)
      const baseParams = { ...params }
      delete baseParams.keyword
      delete baseParams.regions
      delete baseParams.program_directions
      delete baseParams.limit
      delete baseParams.offset
      
      const searchRegions = searchForm.regions.length > 0 
        ? searchForm.regions.map(region => regionDbMap[region] || region)
        : [undefined]
      
      const searchDirections = searchForm.program_directions.length > 0
        ? searchForm.program_directions
        : [undefined]

      // 解析关键词
      const searchTerms = parseSearchQuery(keyword)
      
      let finalResults = []

      if (searchTerms.length > 1) {
        // --- 多关键词组合搜索 (AND 逻辑) ---
        const resultsPerTerm = await Promise.all(searchTerms.map(async (term) => {
          const termPromises = []
          for (const dbRegion of searchRegions) {
            for (const direction of searchDirections) {
              const queryParams = { ...baseParams }
              if (dbRegion) queryParams.region = dbRegion
              if (direction) queryParams.program_direction = direction
              termPromises.push(getProgramsForTerm(term, queryParams, searchForm.program_directions.length > 0))
            }
          }
          const termResults = (await Promise.all(termPromises)).flat()
          return termResults.filter((p, i, self) => i === self.findIndex(item => item.id === p.id))
        }))

        // 取结果的交集
        if (resultsPerTerm.length > 0) {
          let intersection = resultsPerTerm[0]
          const intersectionIds = new Set(intersection.map(p => p.id))

          for (let i = 1; i < resultsPerTerm.length; i++) {
            const nextResultIds = new Set(resultsPerTerm[i].map(p => p.id))
            intersectionIds.forEach(id => {
              if (!nextResultIds.has(id)) {
                intersectionIds.delete(id)
              }
            })
          }
          
          finalResults = intersection.filter(p => intersectionIds.has(p.id))
        }
      } else {
        // --- 单关键词搜索 (OR 逻辑 - 原始行为) ---
        const termPromises = []
        for (const dbRegion of searchRegions) {
          for (const direction of searchDirections) {
            const queryParams = { ...baseParams }
            if (dbRegion) queryParams.region = dbRegion
            if (direction) queryParams.program_direction = direction
            termPromises.push(getProgramsForTerm(keyword, queryParams, searchForm.program_directions.length > 0))
          }
        }
        const keywordResults = (await Promise.all(termPromises)).flat()
        finalResults = keywordResults.filter((p, i, self) => i === self.findIndex(item => item.id === p.id))
      }
      
      // --- 统一处理排序和分页 ---
      const scoredResults = finalResults.map(program => ({
        ...program,
        relevanceScore: searchTerms.reduce((totalScore, term) => totalScore + calculateRelevanceScore(program, term), 0)
      }))
      
      const intelligentSortedResults = scoredResults.sort((a, b) => {
        const aHasRank = a.school_qs_rank && a.school_qs_rank !== 'null' && a.school_qs_rank !== ''
        const bHasRank = b.school_qs_rank && b.school_qs_rank !== 'null' && b.school_qs_rank !== ''
        if (aHasRank && !bHasRank) return -1
        if (!aHasRank && bHasRank) return 1
        if (aHasRank && bHasRank) {
          const rankA = parseInt(a.school_qs_rank)
          const rankB = parseInt(b.school_qs_rank)
          if (rankA !== rankB) return rankA - rankB
        }
        if (a.relevanceScore !== b.relevanceScore) {
          return b.relevanceScore - a.relevanceScore
        }
        return (a.school_name_cn || '').localeCompare(b.school_name_cn || '')
      })
      
      pagination.total = intelligentSortedResults.length
      const startIndex = (pagination.currentPage - 1) * pagination.pageSize
      const endIndex = startIndex + pagination.pageSize
      programs.value = intelligentSortedResults.slice(startIndex, endIndex)

    } else if (searchForm.regions.length > 0 || searchForm.program_directions.length > 0) {
      // 处理没有关键词但有地区和/或专业方向筛选的情况
      delete params.regions
      delete params.program_directions
      
      // 将显示名称转换为数据库值
      const dbRegions = searchForm.regions.length > 0 
        ? searchForm.regions.map(region => regionDbMap[region] || region)
        : [undefined]
      
      const searchDirections = searchForm.program_directions.length > 0
        ? searchForm.program_directions
        : [undefined]
      
      // 构建查询参数组合
      const queryParams = []
      
      for (const dbRegion of dbRegions) {
        for (const direction of searchDirections) {
          const queryParam = { ...params }
          if (dbRegion) queryParam.region = dbRegion
          if (direction) queryParam.program_direction = direction
          queryParams.push(queryParam)
        }
      }
      
      // 并行获取所有查询结果
      const allQueryResults = await Promise.all(
        queryParams.map(param => {
          const queryParam = { ...param }
          delete queryParam.offset // 获取所有数据用于合并
          queryParam.limit = 50000
          return getProgramList(queryParam)
        })
      )
      
      // 合并所有结果并去重
      const allResults = allQueryResults.flat()
      const uniqueResults = allResults.filter((program, index, self) => 
        index === self.findIndex(p => p.id === program.id)
      )
      
      // 按QS排名排序：先按是否有QS排名，再按排名数值，最后按学校名称
      const sortedResults = uniqueResults.sort((a, b) => {
        // 先让有QS排名的在前面（NULL排在后面）
        const aHasRank = a.school_qs_rank && a.school_qs_rank !== 'null' && a.school_qs_rank !== ''
        const bHasRank = b.school_qs_rank && b.school_qs_rank !== 'null' && b.school_qs_rank !== ''
        
        if (aHasRank && !bHasRank) return -1
        if (!aHasRank && bHasRank) return 1
        
        // 如果都有排名，按数字排序
        if (aHasRank && bHasRank) {
          const rankA = parseInt(a.school_qs_rank)
          const rankB = parseInt(b.school_qs_rank)
          if (rankA !== rankB) return rankA - rankB
        }
        
        // 最后按学校名称排序
        return (a.school_name_cn || '').localeCompare(b.school_name_cn || '')
      })
      
      // 设置总数
      pagination.total = sortedResults.length
      
      // 获取当前页的数据
      const startIndex = (pagination.currentPage - 1) * pagination.pageSize
      const endIndex = startIndex + pagination.pageSize
      programs.value = sortedResults.slice(startIndex, endIndex)
    } else {
      // 优化逻辑：直接用分页参数获取数据（没有任何筛选条件）
      const result = await getProgramList(params)
      programs.value = Array.isArray(result) ? result : (result.data || [])
      
      // 使用专门的count API获取总数
      if (pagination.currentPage === 1 || !pagination.total) {
        const countParams = { ...params }
        delete countParams.limit
        delete countParams.offset
        
        const countResult = await getProgramCount(countParams)
        pagination.total = countResult.total
      }
    }
  } catch (error) {
    console.error('加载专业列表失败:', error)
    ElMessage.error('加载专业列表失败')
    programs.value = []
    pagination.total = 0
  } finally {
    loading.value = false
    
    // 预加载当前页面学校的Logo
    if (programs.value.length > 0) {
      try {
        preloadSchoolLogos(programs.value);
      } catch (error) {
        console.warn('预加载页面学校logo失败:', error);
      }
    }
  }
}

// 加载地区列表
const loadRegions = async () => {
  try {
    const result = await getRegionList()
    
    // 确保result是数组且每个元素都有name属性
    if (!Array.isArray(result)) {
      console.error('地区数据格式错误:', result)
      regions.value = []
      return
    }
    
    // 指定的地区顺序
    const regionOrder = [
      "中国香港",
      "新加坡", 
      "英国",
      "美国",
      "澳大利亚",
      "中国澳门",
      "马来西亚"
    ]
    
    // 按指定顺序排序地区
    const orderedRegions = []
    
    // 先添加按指定顺序的地区
    regionOrder.forEach(regionName => {
      const region = result.find(r => r && r.name === regionName)
      if (region && typeof region.name === 'string') {
        orderedRegions.push(region)
      }
    })
    
    // 再添加不在指定列表中的其他地区
    result.forEach(region => {
      if (region && region.name && typeof region.name === 'string' && !regionOrder.includes(region.name)) {
        orderedRegions.push(region)
      }
    })
    
    regions.value = orderedRegions
  } catch (error) {
    console.error('加载地区列表失败:', error)
    regions.value = []
  }
}

// 加载专业大类列表
const loadCategories = async () => {
  try {
    const result = await getProgramCategories()
    
    // 确保result是数组且每个元素都有name属性
    if (!Array.isArray(result)) {
      console.error('专业大类数据格式错误:', result)
      categories.value = []
      return
    }
    
    // 过滤并验证数据
    categories.value = result.filter(category => 
      category && 
      category.name && 
      typeof category.name === 'string'
    )
  } catch (error) {
    console.error('加载专业大类失败:', error)
    categories.value = []
  }
}

// 加载专业方向列表
const loadDirections = async () => {
  try {
    const result = await getProgramDirections()
    
    // 确保result是数组且每个元素都有name属性
    if (!Array.isArray(result)) {
      console.error('专业方向数据格式错误:', result)
      directions.value = []
      return
    }
    
    // 过滤并验证数据
    directions.value = result.filter(direction => 
      direction && 
      direction.name && 
      typeof direction.name === 'string'
    )
  } catch (error) {
    console.error('加载专业方向失败:', error)
    directions.value = []
  }
}

// 加载统计信息
const loadStatistics = async () => {
  try {
    const result = await getProgramStatistics()
    statistics.value = result
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

// 处理智能搜索（防抖）
const handleSearch = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    loadPrograms(true) // 重置到第一页
  }, 300) // 减少防抖时间，提高响应速度
}

// 处理筛选变化
const handleFilterChange = () => {
  loadPrograms(true) // 重置到第一页
}

// 清除搜索
const handleClearSearch = () => {
  loadPrograms(true)
}

// 重置筛选条件
const handleResetFilters = () => {
  Object.keys(searchForm).forEach(key => {
    if (key === 'program_directions' || key === 'regions') {
      searchForm[key] = []
    } else {
      searchForm[key] = ''
    }
  })
  selectedCategory.value = ''
  loadPrograms(true)
}

// 处理地区点击
const handleRegionClick = (region) => {
  if (region === '') {
    // 点击"不限"，清空所有选择
    searchForm.regions = []
  } else {
    // 切换选择状态
    if (searchForm.regions.includes(region)) {
      // 如果已选中，则取消选择
      searchForm.regions = searchForm.regions.filter(r => r !== region)
    } else {
      // 如果未选中，则添加到选择列表
      searchForm.regions.push(region)
    }
  }
  handleFilterChange()
}

// 处理专业大类点击
const handleCategoryClick = (category) => {
  if (selectedCategory.value === category) {
    // 如果已选中，则取消选择
    selectedCategory.value = ''
    searchForm.program_category = ''
  } else {
    // 选择新的大类
    selectedCategory.value = category
    searchForm.program_category = category
    searchForm.program_directions = [] // 清空专业方向选择
  }
  handleFilterChange()
}

// 处理专业方向点击
const handleDirectionClick = (direction) => {
  if (searchForm.program_directions.includes(direction)) {
    // 如果已选中，则取消选择
    searchForm.program_directions = searchForm.program_directions.filter(d => d !== direction)
  } else {
    // 选择新的方向
    searchForm.program_directions.push(direction)
  }
  handleFilterChange()
}

// 处理删除已选择的专业方向
const handleDirectionRemove = (direction) => {
  searchForm.program_directions = searchForm.program_directions.filter(d => d !== direction)
  handleFilterChange()
}

// 处理分页变化
const handlePageChange = (page) => {
  pagination.currentPage = page
  loadPrograms(false) // 不重置页码
}

// 处理每页大小变化
const handlePageSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1 // 重置到第一页
  loadPrograms(false)
}

// 查看详情
const handleViewDetails = (program) => {
  selectedProgramId.value = program.id
  detailDialogVisible.value = true
}

// 收藏专业
const handleCollect = (program) => {
  // TODO: 实现收藏功能
  ElMessage.success(`已收藏 ${program.program_name_cn}`)
}

// 查看官网
const handleViewWebsite = (program) => {
  if (program.program_website) {
    // 确保URL有协议前缀
    let url = program.program_website
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url
    }
    window.open(url, '_blank')
  } else {
    ElMessage.warning('该专业暂无官网链接')
  }
}

/**
 * 从数据库获取学校Logo URL的方法
 */
const fetchSchoolLogoFromDB = async (schoolNameCn) => {
  try {
    // 检查缓存
    if (schoolLogosCache.value.has(schoolNameCn)) {
      return schoolLogosCache.value.get(schoolNameCn);
    }

    // 调用后端API获取学校logo信息
    const response = await fetch(`/api/ai-selection/data/abroad-schools?school_name=${encodeURIComponent(schoolNameCn)}&limit=1`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache',
      }
    });

    if (!response.ok) {
      return null;
    }

    const schools = await response.json();
    const school = schools.find(s => s.school_name_cn === schoolNameCn);
    
    if (school && school.school_logo_url) {
      // 缓存结果
      schoolLogosCache.value.set(schoolNameCn, school.school_logo_url);
      return school.school_logo_url;
    }

    return null;
  } catch (error) {
    console.warn(`获取学校logo失败: ${schoolNameCn}`, error);
    return null;
  }
};

/**
 * 批量预加载学校Logo
 */
const preloadSchoolLogos = async (schools) => {
  // 提取所有唯一的学校中文名
  const schoolNames = [...new Set(schools.map(school => school.school_name_cn).filter(Boolean))];
  
  // 并发获取logo URLs
  const logoPromises = schoolNames.map(async (schoolName) => {
    try {
      const logoUrl = await fetchSchoolLogoFromDB(schoolName);
      return { schoolName, logoUrl };
    } catch (error) {
      console.warn(`预加载logo失败: ${schoolName}`, error);
      return { schoolName, logoUrl: null };
    }
  });

  const results = await Promise.allSettled(logoPromises);
  
  // 更新缓存
  results.forEach((result) => {
    if (result.status === 'fulfilled' && result.value.logoUrl) {
      schoolLogosCache.value.set(result.value.schoolName, result.value.logoUrl);
    }
  });
};

/**
 * 获取学校Logo的方法（新版本：优先使用数据库logo_url）
 */
const getSchoolLogo = (schoolNameCn) => {
  // 方案1: 优先使用数据库中的logo URL（第一优先级）
  if (schoolNameCn && schoolLogosCache.value.has(schoolNameCn)) {
    const logoUrl = schoolLogosCache.value.get(schoolNameCn);
    if (logoUrl && logoUrl.trim() !== '') {
      return logoUrl;
    }
  }

  // 方案2: 使用原有的fallback逻辑（保留作为备用）
  return getSchoolLogoFallback(schoolNameCn);
};

// 处理logo加载错误
const handleLogoError = (event) => {
  // 当logo加载失败时，替换为默认logo
  event.target.src = getSchoolLogoFallback('');
}
</script>

<style scoped>
/* === 全局变量覆盖 === */
:root {
  --el-color-primary: #4F46E5 !important;
  --el-color-primary-light-3: #6366F1 !important;
  --el-color-primary-light-5: #818CF8 !important;
  --el-color-primary-light-7: #C7D2FE !important;
  --el-color-primary-light-9: #EEF2FF !important;
  --el-color-primary-dark-2: #4338CA !important;
}

/* === 布局容器 === */
.program-database-container {
  min-height: 100vh;
  background-color: #F9FAFB;
  margin: 0;
  padding: 0;
}

.container {
  max-width: 1600px; /* 进一步增加容器最大宽度，为右侧数据展示区域提供更多空间 */
  margin: 0 auto;
  padding: 0 1.5rem;
  width: 100%;
}

/* === 1. 页面头部 === */
.page-header {
  background: linear-gradient(135deg, #4F46E5 0%, #4338CA 100%);
  color: white;
  padding: 2rem 0;
  margin: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #FFFFFF;
}

.page-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0;
  color: #FFFFFF;
}

.stats-section {
  display: flex;
  gap: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #FFFFFF;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.8;
  color: #FFFFFF;
}

/* === 2. 主要内容区 === */
.main-content {
  padding: 2rem 0;
}

.search-section {
  margin-bottom: 1.5rem;
}

.search-container {
  position: relative;
}

.search-input {
  width: 100%;
}

:deep(.search-input .floating-input-wrapper) {
  border-radius: 0.5rem;
  border-width: 1px;
}

:deep(.search-input .floating-input-wrapper.focused) {
  border-color: #4F46E5;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-tip {
  display: flex;
  align-items: flex-start; /* 改为顶部对齐 */
  gap: 0.375rem; /* 增加间距 */
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #9CA3AF;
  line-height: 1.4; /* 增加行高 */
}

.search-tip .material-icons-outlined {
  font-size: 14px;
  flex-shrink: 0; /* 防止图标缩小 */
  margin-top: 0.125rem; /* 微调图标位置 */
}

.filter-section {
  border-top: 1px solid #E5E7EB;
  padding-top: 1rem;
}

/* === 筛选组样式 === */
.filter-group {
  margin-bottom: 1.25rem;
}

.filter-group-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
}

.filter-group-header .material-icons-outlined {
  color: #4F46E5;
  font-size: 1rem;
}

/* === 地区和专业方向标签 === */
.filter-tags {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
}

.filter-tag {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #E5E7EB;
  background: #FFFFFF;
  color: #6B7280;
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
  text-align: center;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.filter-tag:hover {
  border-color: #4F46E5;
  color: #4F46E5;
  background: rgba(79, 70, 229, 0.05);
}

.filter-tag.tag-selected {
  background: #4F46E5 !important;
  color: #FFFFFF !important;
  border-color: #4F46E5 !important;
  font-weight: 500;
}

/* === 专业大类样式 === */
.category-tag {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #E5E7EB;
  background: #FFFFFF;
  color: #6B7280;
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-align: center;
  width: 100%;
}

.category-tag:hover {
  border-color: #4F46E5;
  color: #4F46E5;
  background: rgba(79, 70, 229, 0.05);
}

.category-tag .category-name {
  font-weight: 500;
  font-size: 0.875rem;
}

.category-tag .category-arrow .material-icons-outlined {
  font-size: 1rem;
  transition: transform 0.2s ease;
}

.category-tag.tag-selected {
  background: #4F46E5 !important;
  color: #FFFFFF !important;
  border-color: #4F46E5 !important;
  font-weight: 500;
}

.category-tag.tag-selected .category-arrow .material-icons-outlined {
  color: #FFFFFF !important;
}

/* === 已选择专业方向样式 === */
.selected-tags {
  background: #F8FAFC;
  border: 1px solid #E2E8F0;
  border-radius: 0.5rem;
  padding: 1rem;
}

.selected-tag-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.category-prefix {
  font-weight: 600;
  color: #4F46E5;
  white-space: nowrap;
  font-size: 0.875rem;
  line-height: 2;
}

.selected-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  flex: 1;
}

.selected-tag {
  background: #4F46E5 !important;
  color: #FFFFFF !important;
  border: none !important;
  font-size: 0.875rem !important;
  padding: 0.5rem 0.75rem !important;
  border-radius: 1rem !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.selected-tag:hover {
  background: #4338CA !important;
  transform: scale(1.02);
}

.selected-tag .el-tag__close {
  color: #FFFFFF !important;
  margin-left: 0.5rem !important;
  font-size: 0.75rem !important;
}

.selected-tag .el-tag__close:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 50% !important;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 160px;
}

.filter-actions {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #F1F5F9;
}

.action-btn {
  min-width: 120px !important;
  height: 36px !important;
  font-size: 0.875rem !important;
  padding: 0 1.25rem !important;
}

/* === 左侧筛选区域 === */
.lg\:w-96 {
  width: 24rem; /* 384px，进一步增加左侧宽度以更好地显示筛选内容 */
}

.result-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6B7280;
  font-size: 0.875rem;
}

.view-controls {
  display: flex;
  gap: 0.5rem;
}

/* === 卡片系统 === */
.pro-card {
  background-color: #FFFFFF;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #E5E7EB;
  margin-bottom: 1rem;
}

.pro-card-header {
  height: 3.5rem;
  padding: 0 0.875rem;
  border-bottom: 1px solid #E5E7EB;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pro-card-title {
  color: #1F2937;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.pro-card-title .icon {
  margin-right: 0.5rem;
  color: #4F46E5;
  font-size: 1.125rem;
}

.pro-card-body {
  padding: 1rem;
}

/* === 卡片视图 === */
.cards-container {
  margin-bottom: 1rem;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  align-items: stretch;
}

.program-card {
  cursor: pointer;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  transition: all 0.2s ease;
  border: 1px solid #E5E7EB;
  border-radius: 0.75rem;
  background: #FFFFFF;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.program-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border-color: #4F46E5;
}

/* === 卡片头部 === */
.card-header {
  background: linear-gradient(135deg, #FAFBFF 0%, #F4F6FF 100%);
  padding: 1rem;
  border-bottom: 1px solid #E5E7EB;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem;
}

.school-info-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
}

.school-logo-container {
  flex-shrink: 0;
}

.school-logo {
  width: 36px;
  height: 36px;
  border-radius: 0.5rem;
  object-fit: contain;
  background-color: #FFFFFF;
  border: 1px solid #E5E7EB;
  padding: 0.25rem;
}

.school-content {
  flex: 1;
  min-width: 0;
}

.school-name {
  font-weight: 600;
  color: #1F2937;
  font-size: 0.875rem;
  line-height: 1.25;
  margin-bottom: 0.125rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.school-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
}

.region {
  color: #6B7280;
  font-weight: 500;
}

.qs-rank {
  background: linear-gradient(135deg, #FEF3C7, #FDE68A);
  color: #92400E;
  padding: 0.125rem 0.375rem;
  border-radius: 0.375rem;
  font-size: 0.6875rem;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.degree-badge {
  background: linear-gradient(135deg, #4F46E5, #6366F1);
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
}

/* === 卡片内容 === */
.card-body {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.program-title-section {
  margin-bottom: 0.25rem;
}

.program-name {
  font-size: 1.0625rem;
  font-weight: 650;
  color: #1F2937;
  margin: 0 0 0.375rem 0;
  line-height: 1.25;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.program-name-en {
  color: #6B7280;
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0;
  font-style: italic;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.program-meta-grid {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
}

.meta-item {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.meta-label {
  color: #6B7280;
  font-size: 0.78125rem;
  font-weight: 400;
  min-width: 56px;
  flex-shrink: 0;
  line-height: 1.4;
}

.meta-value {
  color: #374151;
  font-size: 0.78125rem;
  font-weight: 400;
  flex: 1;
  line-height: 1.4;
}

.info-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-top: auto;
}

.info-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  border: 1px solid;
}

.info-tag.tuition {
  color: #0EA5E9;
  background: linear-gradient(135deg, #F0F9FF, #E0F2FE);
  border-color: #BAE6FD;
}

.info-tag .material-icons-outlined {
  font-size: 0.875rem;
}

/* === 卡片底部 === */
.card-footer {
  padding: 0.75rem 1rem;
  border-top: 1px solid #F1F5F9;
  background: #FAFBFC;
  display: flex;
  gap: 0.5rem;
  margin-top: auto;
}

.card-footer .el-button {
  flex: 1;
  font-size: 0.8125rem !important;
  font-weight: 500 !important;
  padding: 0.5rem 0.75rem !important;
  border-radius: 0.5rem !important;
  transition: all 0.2s ease !important;
}

.btn-website {
  background: linear-gradient(135deg, #4F46E5, #6366F1) !important;
  color: #FFFFFF !important;
  border: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.375rem !important;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2) !important;
}

.btn-website:hover {
  background: linear-gradient(135deg, #4338CA, #5B21B6) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3) !important;
}

.btn-collect {
  background: #FFFFFF !important;
  color: #6B7280 !important;
  border: 1px solid #E5E7EB !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.375rem !important;
}

.btn-collect:hover {
  color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  background: #F8FAFC !important;
  transform: translateY(-1px) !important;
}

.btn-website .material-icons-outlined,
.btn-collect .material-icons-outlined {
  font-size: 1rem;
}



/* === 按钮系统 === */
.btn-primary {
  background-color: #4F46E5 !important;
  color: #FFFFFF !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem 1rem !important;
  font-weight: 500 !important;
  border: none !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.25rem !important;
}

.btn-primary:hover {
  background-color: #4338CA !important;
  transform: scale(1.02);
}

.btn-secondary {
  background-color: transparent !important;
  color: #6B7280 !important;
  border: 1px solid #E5E7EB !important;
  padding: 0.5rem 1rem !important;
  border-radius: 0.5rem !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.25rem !important;
}

.btn-secondary:hover {
  color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  background-color: rgba(79, 70, 229, 0.05) !important;
}



/* === 表单系统 === */
.form-label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-input {
  height: 40px;
  border-radius: 0.375rem;
}

:deep(.el-input.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.8) !important;
}

/* === 动画系统 === */
.transition-standard {
  transition: all 0.2s ease;
}

.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.02);
}

.button-click {
  transition: transform 0.1s ease;
}

.button-click:active {
  transform: scale(0.98);
}

/* === 淡入淡出动画 === */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.2s ease-in-out;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.fade-enter-to, .fade-leave-from {
  opacity: 1;
}

/* === 列表淡入淡出动画 === */
.fade-list-enter-active {
  transition: all 0.25s ease-out;
}

.fade-list-leave-active {
  transition: all 0.2s ease-in;
}

.fade-list-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.fade-list-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.fade-list-move {
  transition: transform 0.2s ease;
}

/* === 内容容器优化（防止抖动） === */
.content-container {
  min-height: 600px; /* 设置最小高度避免布局抖动 */
  position: relative;
}

/* === 响应式优化（防止过渡抖动） === */
.transition-all {
  transition-property: width, height, margin, padding;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* === 图标系统 === */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-size: 20px;
  line-height: 1;
  transition: color 0.2s ease;
}

.material-icons-outlined:hover {
  color: #4F46E5 !important;
}

/* === Loading和空状态 === */
.loading-container {
  margin-bottom: 1rem;
}

.skeleton-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.skeleton-card {
  border: 1px solid #E5E7EB;
  border-radius: 0.5rem;
  overflow: hidden;
}

.skeleton-content {
  padding: 1rem;
}

.empty-state {
  margin-bottom: 1rem;
}

.empty-content {
  text-align: center;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.empty-icon {
  font-size: 4rem;
  color: #D1D5DB;
}

.empty-content h3 {
  color: #6B7280;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.empty-content p {
  color: #9CA3AF;
  margin: 0;
  font-size: 0.875rem;
  max-width: 300px;
  line-height: 1.5;
}

/* === 分页系统 === */
.pagination-container {
  margin-top: 1rem;
}

.pagination-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.pagination-info {
  color: #6B7280;
  font-size: 0.875rem;
}

.info-text {
  font-weight: 500;
}

.custom-pagination {
  --el-pagination-font-size: 14px;
  --el-pagination-bg-color: #FFFFFF;
  --el-pagination-button-color: #606266;
  --el-pagination-hover-color: #4F46E5;
}

:deep(.custom-pagination .el-pagination__total) {
  color: #6B7280;
  font-weight: 500;
}

:deep(.custom-pagination .el-pagination__sizes) {
  margin-right: 1rem;
}

:deep(.custom-pagination .el-select .el-input .el-input__inner) {
  height: 32px;
  font-size: 14px;
}

:deep(.custom-pagination .el-pagination__jump) {
  margin-left: 1rem;
  color: #6B7280;
}

:deep(.custom-pagination .el-pagination__jump .el-input .el-input__inner) {
  width: 50px;
  height: 32px;
  font-size: 14px;
}

:deep(.custom-pagination .btn-prev),
:deep(.custom-pagination .btn-next),
:deep(.custom-pagination .el-pager li) {
  min-width: 32px;
  height: 32px;
  line-height: 30px;
  border: 1px solid #E5E7EB;
  border-radius: 0.375rem;
  color: #6B7280;
  font-size: 14px;
  margin: 0 2px;
}

:deep(.custom-pagination .btn-prev:hover),
:deep(.custom-pagination .btn-next:hover),
:deep(.custom-pagination .el-pager li:hover) {
  color: #4F46E5;
  border-color: #4F46E5;
  background-color: rgba(79, 70, 229, 0.05);
}

:deep(.custom-pagination .el-pager li.is-active) {
  color: #FFFFFF;
  background-color: #4F46E5;
  border-color: #4F46E5;
}



/* === 响应式设计 === */
@media (max-width: 1024px) {
  .lg\:w-96 {
    width: 100%;
  }
  
  .flex-col.lg\:flex-row {
    flex-direction: column;
  }
}

@media (max-width: 1200px) {
  .cards-grid {
    grid-template-columns: repeat(2, 1fr); /* 中等屏幕显示2列 */
    gap: 0.875rem; /* 优化中等屏幕间距 */
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .page-header {
    padding: 1.5rem 0;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .stats-section {
    gap: 1rem;
  }

  .filter-grid {
    grid-template-columns: 1fr;
  }

  .filter-tags {
    grid-template-columns: repeat(2, 1fr);
  }

  .filter-actions {
    justify-content: center;
  }

  .pro-card-header {
    flex-direction: column;
    height: auto;
    padding: 1rem;
    gap: 1rem;
    text-align: center;
  }

  .cards-grid {
    grid-template-columns: 1fr; /* 小屏幕显示1列 */
    gap: 0.75rem;
  }

  .card-footer {
    gap: 0.375rem;
  }

  .card-footer .el-button {
    font-size: 0.75rem !important;
    padding: 0.375rem 0.5rem !important;
  }

  .pagination-wrapper {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .filter-actions {
    justify-content: center;
  }

  .action-btn {
    width: 100% !important;
  }

  .main-content {
    padding: 1.5rem 0;
  }
}

/* === 响应式布局优化（MacBook等中等屏幕） === */
@media (min-width: 1024px) and (max-width: 1279px) {
  /* 在lg到xl之间的屏幕（如MacBook）上的优化 */
  .pro-card-body {
    padding: 0.75rem;
  }
  
  .search-section .search-container {
    margin-bottom: 0.75rem;
  }
  
  .filter-group {
    margin-bottom: 0.75rem;
  }
  
  .filter-group-header {
    font-size: 0.8125rem;
    margin-bottom: 0.375rem;
  }
  
  .filter-tags {
    gap: 0.375rem;
  }
  
  .filter-tag {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
  
  .category-tag {
    padding: 0.375rem 0.75rem;
  }
  
  /* 优化浮动标签输入框 */
  .search-input {
    font-size: 0.875rem;
  }
  
  .search-tip {
    font-size: 0.75rem;
    margin-top: 0.25rem;
  }
}
</style> 