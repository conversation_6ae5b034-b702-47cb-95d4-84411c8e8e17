<template>
  <div class="pro-card h-full flex flex-col">
    <!-- 编辑器工具栏 -->
    <div class="pro-card-header">
      <div class="flex items-center space-x-2">
        <el-button 
          @click="handleSave" 
          type="primary" 
          :loading="isSaving"
          class="primary-btn transition-standard hover-scale"
        >
          保存
        </el-button>
        <el-dropdown @command="handleExport" class="transition-standard">
          <el-button class="secondary-btn flex items-center">
            <span class="material-icons-outlined text-base mr-1.5">save_alt</span>
            <span>导出</span>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="pdf">导出为PDF</el-dropdown-item>
              <el-dropdown-item command="docx">导出为Word</el-dropdown-item>
              <el-dropdown-item command="txt">导出为文本</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button @click="handleCopyAll" class="secondary-btn transition-standard">
          复制全部
        </el-button>
      </div>
      <div class="flex items-center space-x-4 text-sm text-gray-500">
        <span class="text-gray-600">{{ wordCount }} 单词</span>
        <span class="text-gray-600">{{ characterCount }} 字符</span>
        <span v-if="lastSaved" class="text-gray-600">上次保存: <span class="font-medium text-gray-800">{{ lastSaved }}</span></span>
      </div>
    </div>

    <!-- 编辑器工具栏 -->
    <div v-if="editor" class="editor-toolbar">
      <div class="toolbar-group">
        <button
          @click="editor.chain().focus().undo().run()"
          :disabled="!editor.can().undo()"
          class="toolbar-btn"
          title="撤销"
        >
          <span class="material-icons-outlined">undo</span>
        </button>
        <button
          @click="editor.chain().focus().redo().run()"
          :disabled="!editor.can().redo()"
          class="toolbar-btn"
          title="重做"
        >
          <span class="material-icons-outlined">redo</span>
        </button>
      </div>
      
      <div class="toolbar-group">
        <button
          @click="editor.chain().focus().toggleBold().run()"
          :class="{ 'is-active': editor.isActive('bold') }"
          class="toolbar-btn"
          title="粗体"
        >
          <span class="material-icons-outlined">format_bold</span>
        </button>
        <button
          @click="editor.chain().focus().toggleItalic().run()"
          :class="{ 'is-active': editor.isActive('italic') }"
          class="toolbar-btn"
          title="斜体"
        >
          <span class="material-icons-outlined">format_italic</span>
        </button>
        <button
          @click="editor.chain().focus().toggleStrike().run()"
          :class="{ 'is-active': editor.isActive('strike') }"
          class="toolbar-btn"
          title="删除线"
        >
          <span class="material-icons-outlined">strikethrough_s</span>
        </button>
        <button
          @click="editor.chain().focus().toggleUnderline().run()"
          :class="{ 'is-active': editor.isActive('underline') }"
          class="toolbar-btn"
          title="下划线"
        >
          <span class="material-icons-outlined">format_underline</span>
        </button>
      </div>
      
      <div class="toolbar-group">
        <button
          @click="editor.chain().focus().setParagraph().run()"
          :class="{ 'is-active': editor.isActive('paragraph') && !editor.isActive('heading') }"
          class="toolbar-btn"
          title="正文"
        >
          A
        </button>
        <button
          @click="editor.chain().focus().toggleHeading({ level: 1 }).run()"
          :class="{ 'is-active': editor.isActive('heading', { level: 1 }) }"
          class="toolbar-btn"
          title="一级标题"
        >
          H1
        </button>
        <button
          @click="editor.chain().focus().toggleHeading({ level: 2 }).run()"
          :class="{ 'is-active': editor.isActive('heading', { level: 2 }) }"
          class="toolbar-btn"
          title="二级标题"
        >
          H2
        </button>
        <button
          @click="editor.chain().focus().toggleHeading({ level: 3 }).run()"
          :class="{ 'is-active': editor.isActive('heading', { level: 3 }) }"
          class="toolbar-btn"
          title="三级标题"
        >
          H3
        </button>
      </div>

      <div class="toolbar-group">
        <button
          @click="editor.chain().focus().toggleBulletList().run()"
          :class="{ 'is-active': editor.isActive('bulletList') }"
          class="toolbar-btn"
          title="无序列表"
        >
          <span class="material-icons-outlined">format_list_bulleted</span>
        </button>
        <button
          @click="editor.chain().focus().toggleOrderedList().run()"
          :class="{ 'is-active': editor.isActive('orderedList') }"
          class="toolbar-btn"
          title="有序列表"
        >
          <span class="material-icons-outlined">format_list_numbered</span>
        </button>
      </div>

      <div class="toolbar-group">
        <button
          @click="editor.chain().focus().setTextAlign('left').run()"
          :class="{ 'is-active': editor.isActive({ textAlign: 'left' }) }"
          class="toolbar-btn"
          title="左对齐"
        >
          <span class="material-icons-outlined">format_align_left</span>
        </button>
        <button
          @click="editor.chain().focus().setTextAlign('center').run()"
          :class="{ 'is-active': editor.isActive({ textAlign: 'center' }) }"
          class="toolbar-btn"
          title="居中对齐"
        >
          <span class="material-icons-outlined">format_align_center</span>
        </button>
        <button
          @click="editor.chain().focus().setTextAlign('right').run()"
          :class="{ 'is-active': editor.isActive({ textAlign: 'right' }) }"
          class="toolbar-btn"
          title="右对齐"
        >
          <span class="material-icons-outlined">format_align_right</span>
        </button>
      </div>

      <div class="toolbar-group">
        <button
          @click="editor.chain().focus().toggleBlockquote().run()"
          :class="{ 'is-active': editor.isActive('blockquote') }"
          class="toolbar-btn"
          title="引用"
        >
          <span class="material-icons-outlined">format_quote</span>
        </button>
        <button
          @click="editor.chain().focus().toggleCodeBlock().run()"
          :class="{ 'is-active': editor.isActive('codeBlock') }"
          class="toolbar-btn"
          title="代码块"
        >
          <span class="material-icons-outlined">code</span>
        </button>
        <button
          @click="editor.chain().focus().setHorizontalRule().run()"
          class="toolbar-btn"
          title="分割线"
        >
          <span class="material-icons-outlined">horizontal_rule</span>
        </button>
      </div>
    </div>

    <!-- 编辑器区域 -->
    <div class="pro-card-body flex-1 flex flex-col">
      <div class="editor-wrapper flex-1">
        <editor-content :editor="editor" class="editor-content" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Typography from '@tiptap/extension-typography'
import Placeholder from '@tiptap/extension-placeholder'
import TextAlign from '@tiptap/extension-text-align'
import Underline from '@tiptap/extension-underline'
import { marked } from 'marked'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  documentType: {
    type: String,
    required: true // 'cv', 'ps', 'recommendation'
  },
  placeholder: {
    type: String,
    default: '开始写作...'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'save', 'export'])

// 响应式数据
const editor = ref(null)
const isSaving = ref(false)
const lastSaved = ref('')

// 计算字数
const wordCount = computed(() => {
  if (!editor.value) return 0
  const text = editor.value.getText()
  return text.trim().split(/\s+/).filter(word => word.length > 0).length
})

// 计算字符数
const characterCount = computed(() => {
  if (!editor.value) return 0
  const text = editor.value.getText()
  return text.length
})

// Markdown 转 HTML 的配置
const markedOptions = {
  breaks: true,
  gfm: true
}

// 将 Markdown 转换为 HTML
const convertMarkdownToHtml = (markdown) => {
  if (!markdown) return ''
  try {
    return marked(markdown, markedOptions)
  } catch (error) {
    console.error('Markdown 转换失败:', error)
    return markdown
  }
}

// 初始化编辑器
const initEditor = () => {
  editor.value = new Editor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3, 4, 5, 6],
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Underline,
      Typography,
      Placeholder.configure({
        placeholder: props.placeholder,
      }),
    ],
    content: '',
    editable: true,
    autofocus: false,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      console.log('编辑器内容变化，HTML长度:', html.length)
      emit('update:modelValue', html)
    },
  })
  
  console.log('Tiptap编辑器初始化完成')
}

// 保存功能
const handleSave = async () => {
  if (!editor.value) return
  
  isSaving.value = true
  try {
    const content = editor.value.getHTML()
    emit('save', {
      content,
      type: props.documentType,
      wordCount: wordCount.value,
      characterCount: characterCount.value
    })
    
    // 模拟保存API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    lastSaved.value = new Date().toLocaleTimeString()
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
    console.error('Save error:', error)
  } finally {
    isSaving.value = false
  }
}

// 导出功能
const handleExport = async (format) => {
  if (!editor.value) return
  
  try {
    const htmlContent = editor.value.getHTML()
    const textContent = editor.value.getText()
    
    emit('export', {
      content: htmlContent,
      textContent: textContent,
      format,
      type: props.documentType,
      wordCount: wordCount.value,
      characterCount: characterCount.value
    })
    
    ElMessage.success(`正在生成${format.toUpperCase()}文件...`)
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('Export error:', error)
  }
}

// 复制全部内容
const handleCopyAll = async () => {
  if (!editor.value) return
  
  try {
    const content = editor.value.getText()
    
    if (!content.trim()) {
      ElMessage.warning('没有内容可复制')
      return
    }
    
    // 使用现代浏览器的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(content)
      ElMessage.success('内容已复制到剪贴板')
    } else {
      // 备用方案：使用传统方法
      const textArea = document.createElement('textarea')
      textArea.value = content
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      try {
        const successful = document.execCommand('copy')
        if (successful) {
          ElMessage.success('内容已复制到剪贴板')
        } else {
          ElMessage.error('复制失败，请手动选择文本复制')
        }
      } catch (err) {
        console.error('复制失败:', err)
        ElMessage.error('复制失败，请手动选择文本复制')
      } finally {
        document.body.removeChild(textArea)
      }
    }
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动选择文本复制')
  }
}

// 监听内容变化
watch(() => props.modelValue, (newValue) => {
  if (!editor.value || !newValue) return
  
  console.log('🔄 TiptapEditor watch触发')
  console.log('📏 新值长度:', newValue.length)
  console.log('📝 新值前100字符:', newValue.substring(0, 100))
  
  const currentHtml = editor.value.getHTML()
  
  // 检测是否是 Markdown 内容
  const isMarkdown = newValue.includes('##') || newValue.includes('**') || newValue.includes('- ') || newValue.includes('1. ')
  
  if (isMarkdown) {
    console.log('🔄 检测到Markdown内容，开始转换')
    const htmlContent = convertMarkdownToHtml(newValue)
    console.log('✅ Markdown转换完成，HTML长度:', htmlContent.length)
    
    // 避免循环更新
    if (htmlContent !== currentHtml) {
      editor.value.commands.setContent(htmlContent, false)
    }
  } else {
    // 如果是HTML内容，直接设置
    if (newValue !== currentHtml) {
      editor.value.commands.setContent(newValue, false)
    }
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  console.log('📱 TiptapEditor mounted')
  initEditor()
})

onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})
</script>

<style scoped>
/* 按钮样式 */
.primary-btn {
  background-color: #4F46E5 !important;
  color: #FFFFFF !important;
  border: none !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem 1rem !important;
  font-weight: 500 !important;
}

.primary-btn:hover {
  background-color: #4338CA !important;
}

.secondary-btn {
  background-color: transparent !important;
  color: #6B7280 !important;
  border: 1px solid #E5E7EB !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem 1rem !important;
}

.secondary-btn:hover {
  color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  background-color: rgba(79, 70, 229, 0.05) !important;
}

/* 卡片样式 */
.pro-card {
  background-color: #FFFFFF;
  border: none;
  border-radius: 0;
  box-shadow: none;
}

.pro-card-header {
  height: 3.5rem;
  padding: 0 1rem;
  border-bottom: 1px solid #E5E7EB;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.pro-card-body {
  padding: 0;
  flex: 1;
  min-height: 0;
}

/* 动画效果 */
.transition-standard {
  transition: all 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* 编辑器工具栏样式 */
.editor-toolbar {
  border-bottom: 1px solid #E5E7EB;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  flex-shrink: 0;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding-right: 0.5rem;
  border-right: 1px solid #E5E7EB;
}

.toolbar-group:last-child {
  border-right: none;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  background-color: transparent;
  color: #6B7280;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.toolbar-btn:hover {
  background-color: #F3F4F6;
  color: #374151;
}

.toolbar-btn.is-active {
  background-color: #4F46E5;
  color: #FFFFFF;
  border-color: #4F46E5;
}

.toolbar-btn:disabled {
  background-color: transparent;
  color: #D1D5DB;
  cursor: not-allowed;
  opacity: 0.5;
}

.toolbar-btn:disabled:hover {
  background-color: transparent;
  color: #D1D5DB;
}

.toolbar-btn .material-icons-outlined {
  font-size: 1rem;
}

/* 正文按钮样式 */
.toolbar-btn:has-text("A") {
  font-size: 0.875rem;
  font-weight: 600;
}

/* 编辑器容器样式 */
.editor-wrapper {
  position: relative;
  height: 100%;
  overflow: hidden;
}

.editor-content {
  height: 100%;
  overflow-y: auto;
  background-color: #f3f4f6; /* Add a canvas background */
  padding: 2rem 1rem; /* Add padding for the scrolling canvas */
}

/* Tiptap编辑器样式覆盖 */
:deep(.ProseMirror) {
  /* --- A4 Page Simulation --- */
  width: 210mm;
  margin: 0 auto;
  padding: 20mm;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
  border-radius: 3px;
  /* --- End A4 Simulation --- */

  outline: none;
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* 确保文本可选择 */
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  cursor: text;
}

/* 增强：确保列表项正确显示项目符号 */
:deep(.ProseMirror ul) {
  list-style-type: disc;
  padding-left: 1.75rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

:deep(.ProseMirror ol) {
  list-style-type: decimal;
  padding-left: 1.75rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

:deep(.ProseMirror li) {
  display: list-item;
  margin-top: 0.25rem;
}

:deep(.ProseMirror p.is-editor-empty:first-child::before) {
  content: attr(data-placeholder);
  float: left;
  color: #9CA3AF;
  pointer-events: none;
  height: 0;
}

/* 标题样式 */
:deep(.ProseMirror h1) {
  font-size: 1.875rem;
  font-weight: 700;
  margin: 2rem 0 1rem 0;
  line-height: 1.2;
  color: #1F2937;
  border-bottom: 2px solid #E5E7EB;
  padding-bottom: 0.5rem;
}

:deep(.ProseMirror h2) {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.5rem 0 0.75rem 0;
  line-height: 1.3;
  color: #374151;
}

:deep(.ProseMirror h3) {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1.25rem 0 0.5rem 0;
  line-height: 1.4;
  color: #4B5563;
}

/* 段落样式 */
:deep(.ProseMirror p) {
  margin: 0.875rem 0;
  line-height: 1.7;
  color: #374151;
  text-align: justify;
}

/* 引用样式 */
:deep(.ProseMirror blockquote) {
  border-left: 4px solid #4F46E5;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6B7280;
}

/* 代码样式 */
:deep(.ProseMirror code) {
  background-color: #F3F4F6;
  color: #EF4444;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

:deep(.ProseMirror pre) {
  background-color: #1F2937;
  color: #F9FAFB;
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 1rem 0;
  overflow-x: auto;
}

:deep(.ProseMirror pre code) {
  background-color: transparent;
  color: inherit;
  padding: 0;
}

/* 选择高亮 */
:deep(.ProseMirror)::selection {
  background-color: rgba(79, 70, 229, 0.2);
}

:deep(.ProseMirror) *::selection {
  background-color: rgba(79, 70, 229, 0.2);
}

/* Element Plus覆盖 */
:deep(.el-input.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.8) !important;
}

/* Material Icons */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  line-height: 1;
}

/* 自定义导出下拉框悬浮样式 */
:deep(.el-dropdown-menu__item:not(.is-disabled):hover) {
  background-color: #4F46E5 !important;
  color: #FFFFFF !important;
}
</style> 