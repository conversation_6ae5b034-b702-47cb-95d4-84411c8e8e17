import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useSchoolLogosStore = defineStore('schoolLogos', () => {
  // 全局的学校logo缓存
  const logoCache = ref(new Map())

  // 获取学校logo
  const getLogoFromCache = (schoolNameCn) => {
    return logoCache.value.get(schoolNameCn)
  }

  // 缓存学校logo
  const setLogoCache = (schoolNameCn, logoUrl) => {
    logoCache.value.set(schoolNameCn, logoUrl)
  }

  // 检查缓存中是否存在logo
  const hasLogoInCache = (schoolNameCn) => {
    return logoCache.value.has(schoolNameCn)
  }

  // 清除缓存
  const clearCache = () => {
    logoCache.value.clear()
  }

  // 从数据库获取学校Logo URL的方法
  const fetchSchoolLogoFromDB = async (schoolNameCn) => {
    try {
      // 检查缓存
      if (hasLogoInCache(schoolNameCn)) {
        return getLogoFromCache(schoolNameCn)
      }

      // 调用后端API获取学校logo信息，启用缓存
      const response = await fetch(`/api/ai-selection/data/abroad-schools?school_name=${encodeURIComponent(schoolNameCn)}&limit=1`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Cache-Control': 'max-age=3600',  // 缓存1小时
        }
      })

      if (!response.ok) {
        return null
      }

      const schools = await response.json()
      const school = schools.find(s => s.school_name_cn === schoolNameCn)
      
      if (school && school.school_logo_url) {
        // 缓存结果
        setLogoCache(schoolNameCn, school.school_logo_url)
        return school.school_logo_url
      }

      return null
    } catch (error) {
      console.warn(`获取学校logo失败: ${schoolNameCn}`, error)
      return null
    }
  }

  // 批量预加载学校Logo
  const preloadSchoolLogos = async (schools) => {
    // 提取所有唯一的学校中文名
    const schoolNames = [...new Set(schools.map(school => school.school_name_cn || school.学校中文名).filter(Boolean))]
    
    // 并发获取logo URLs
    const logoPromises = schoolNames.map(async (schoolName) => {
      try {
        const logoUrl = await fetchSchoolLogoFromDB(schoolName)
        return { schoolName, logoUrl }
      } catch (error) {
        console.warn(`预加载logo失败: ${schoolName}`, error)
        return { schoolName, logoUrl: null }
      }
    })

    const results = await Promise.allSettled(logoPromises)
    
    // 更新缓存
    results.forEach((result) => {
      if (result.status === 'fulfilled' && result.value.logoUrl) {
        setLogoCache(result.value.schoolName, result.value.logoUrl)
      }
    })
  }

  // Fallback logo获取逻辑
  const getSchoolLogoFallback = (schoolNameCn) => {
    // 学校域名映射
    const domainMapping = {
      '麻省理工学院': 'mit.edu',
      '新加坡国立大学': 'nus.edu.sg',
      '香港大学': 'hku.hk',
      '帝国理工学院': 'imperial.ac.uk',
      '伦敦大学学院': 'ucl.ac.uk',
      '哈佛大学': 'harvard.edu',
      '斯坦福大学': 'stanford.edu',
      '剑桥大学': 'cam.ac.uk',
      '牛津大学': 'ox.ac.uk',
      '清华大学': 'tsinghua.edu.cn',
      '北京大学': 'pku.edu.cn',
      '复旦大学': 'fudan.edu.cn',
      '上海交通大学': 'sjtu.edu.cn',
      '浙江大学': 'zju.edu.cn',
      '南京大学': 'nju.edu.cn',
      '中国人民大学': 'ruc.edu.cn',
      '北京航空航天大学': 'buaa.edu.cn',
      '同济大学': 'tongji.edu.cn',
      '天津大学': 'tju.edu.cn',
      '华中科技大学': 'hust.edu.cn',
      '西安交通大学': 'xjtu.edu.cn',
      '中山大学': 'sysu.edu.cn',
      '哈尔滨工业大学': 'hit.edu.cn',
      '武汉大学': 'whu.edu.cn',
      '东南大学': 'seu.edu.cn',
      '中南大学': 'csu.edu.cn',
      '大连理工大学': 'dlut.edu.cn',
      '北京理工大学': 'bit.edu.cn',
      '厦门大学': 'xmu.edu.cn'
    }

    // 首先检查是否有预定义的域名映射
    if (domainMapping[schoolNameCn]) {
      const domain = domainMapping[schoolNameCn]
      // 使用Google的favicon服务，更可靠
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=128`
    }

    // 生成美观的文字Logo（最终fallback）
    const colors = [
      { bg: '4F46E5', color: 'FFFFFF' }, // 紫色
      { bg: '059669', color: 'FFFFFF' }, // 绿色
      { bg: 'DC2626', color: 'FFFFFF' }, // 红色
      { bg: '2563EB', color: 'FFFFFF' }, // 蓝色
      { bg: 'F59E0B', color: 'FFFFFF' }, // 橙色
      { bg: '7C2D12', color: 'FFFFFF' }, // 棕色
      { bg: '581C87', color: 'FFFFFF' }, // 深紫色
      { bg: '0F766E', color: 'FFFFFF' }  // 青色
    ]
    
    // 使用学校名称的第一个字符来选择颜色
    const firstChar = (schoolNameCn || 'U').charAt(0)
    const colorIndex = firstChar.charCodeAt(0) % colors.length
    const { bg, color } = colors[colorIndex]
    
    // 使用学校名称的前两个字符（如果是中文）或第一个字符（如果是英文）
    let initials = schoolNameCn || 'U'
    if (/[\u4e00-\u9fa5]/.test(initials)) {
      // 中文：取前两个字符
      initials = initials.substring(0, 2)
    } else {
      // 英文：取第一个字符
      initials = initials.charAt(0).toUpperCase()
    }
    
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=${bg}&color=${color}&size=128&font-size=0.6&bold=true&format=svg`
  }

  // 获取学校Logo的统一方法
  const getSchoolLogo = (schoolNameCn) => {
    // 方案1: 优先使用缓存中的logo URL
    if (schoolNameCn && hasLogoInCache(schoolNameCn)) {
      const logoUrl = getLogoFromCache(schoolNameCn)
      if (logoUrl && logoUrl.trim() !== '') {
        return logoUrl
      }
    }

    // 方案2: 异步获取logo并缓存
    if (schoolNameCn) {
      fetchSchoolLogoFromDB(schoolNameCn).then(logoUrl => {
        if (logoUrl) {
          setLogoCache(schoolNameCn, logoUrl)
        }
      }).catch(err => {
        console.warn('异步获取logo失败:', err)
      })
    }

    // 方案3: 立即返回fallback逻辑
    return getSchoolLogoFallback(schoolNameCn)
  }

  return {
    logoCache,
    getLogoFromCache,
    setLogoCache,
    hasLogoInCache,
    clearCache,
    fetchSchoolLogoFromDB,
    preloadSchoolLogos,
    getSchoolLogoFallback,
    getSchoolLogo
  }
}) 