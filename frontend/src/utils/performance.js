/**
 * 性能监控工具
 * 用于监控API请求和页面加载性能
 */

// 性能监控类
class PerformanceMonitor {
  constructor() {
    this.timers = new Map()
    this.metrics = []
  }

  // 开始计时
  start(label) {
    this.timers.set(label, performance.now())
  }

  // 结束计时并记录
  end(label) {
    const startTime = this.timers.get(label)
    if (startTime) {
      const duration = performance.now() - startTime
      this.timers.delete(label)
      
      const metric = {
        label,
        duration: Math.round(duration),
        timestamp: new Date().toISOString()
      }
      
      this.metrics.push(metric)
      
      // 如果耗时超过阈值，输出警告
      if (duration > 2000) {
        console.warn(`⚠️ 性能警告: ${label} 耗时 ${Math.round(duration)}ms`)
      } else if (duration > 1000) {
        console.info(`ℹ️ 性能提示: ${label} 耗时 ${Math.round(duration)}ms`)
      }
      
      return metric
    }
    return null
  }

  // 获取性能报告
  getReport() {
    return {
      totalMetrics: this.metrics.length,
      averageDuration: this.metrics.length > 0 
        ? Math.round(this.metrics.reduce((sum, m) => sum + m.duration, 0) / this.metrics.length)
        : 0,
      slowestOperations: this.metrics
        .sort((a, b) => b.duration - a.duration)
        .slice(0, 5),
      recentMetrics: this.metrics.slice(-10)
    }
  }

  // 清除历史记录
  clear() {
    this.metrics = []
    this.timers.clear()
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor()

// 装饰器函数，用于监控异步函数性能
export function withPerformanceMonitoring(label) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function(...args) {
      performanceMonitor.start(label)
      try {
        const result = await originalMethod.apply(this, args)
        performanceMonitor.end(label)
        return result
      } catch (error) {
        performanceMonitor.end(label)
        throw error
      }
    }
    
    return descriptor
  }
}

// 监控API请求的函数
export function monitorApiCall(apiName, apiCall) {
  return async (...args) => {
    performanceMonitor.start(`API: ${apiName}`)
    try {
      const result = await apiCall(...args)
      performanceMonitor.end(`API: ${apiName}`)
      return result
    } catch (error) {
      performanceMonitor.end(`API: ${apiName}`)
      throw error
    }
  }
}

// 监控页面加载的函数
export function monitorPageLoad(pageName, loadFunction) {
  return async (...args) => {
    performanceMonitor.start(`页面加载: ${pageName}`)
    try {
      const result = await loadFunction(...args)
      performanceMonitor.end(`页面加载: ${pageName}`)
      return result
    } catch (error) {
      performanceMonitor.end(`页面加载: ${pageName}`)
      throw error
    }
  }
}

// 防抖函数
export function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 节流函数
export function throttle(func, limit) {
  let inThrottle
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 缓存装饰器
export function withCache(ttl = 300000) { // 默认5分钟缓存
  const cache = new Map()
  
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function(...args) {
      const key = JSON.stringify(args)
      const cached = cache.get(key)
      
      if (cached && Date.now() - cached.timestamp < ttl) {
        console.log(`🎯 缓存命中: ${propertyKey}`)
        return cached.data
      }
      
      const result = originalMethod.apply(this, args)
      
      // 如果是Promise，缓存resolved的值
      if (result instanceof Promise) {
        return result.then(data => {
          cache.set(key, { data, timestamp: Date.now() })
          return data
        })
      } else {
        cache.set(key, { data: result, timestamp: Date.now() })
        return result
      }
    }
    
    return descriptor
  }
}

// 导出性能监控实例
export { performanceMonitor }

// 在开发环境下，将性能监控器挂载到window对象上，方便调试
if (import.meta.env.DEV) {
  window.performanceMonitor = performanceMonitor
}
