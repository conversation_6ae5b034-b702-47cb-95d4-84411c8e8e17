#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 打印带颜色的消息函数
print_message() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')] $1${NC}"
}

print_success() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

print_error() {
    echo -e "${RED}[$(date +'%H:%M:%S')] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[$(date +'%H:%M:%S')] $1${NC}"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_error "$1 未安装，请先安装 $1"
        exit 1
    fi
}

# 检查并启动 PostgreSQL (已注释 - 使用Supabase云端数据库)
# check_postgres() {
#     print_message "检查 PostgreSQL 服务..."
#     if ! pg_isready > /dev/null 2>&1; then
#         print_warning "PostgreSQL 服务未启动"
#         print_message "正在启动 PostgreSQL 服务..."
#         if command -v brew &> /dev/null; then
#             brew services start postgresql
#         else
#             print_error "未找到 brew 命令，请手动启动 PostgreSQL 服务"
#             exit 1
#         fi
#         sleep 3
#     fi
#     print_success "PostgreSQL 服务已启动"
# }

# 检查并创建数据库 (已注释 - 使用Supabase云端数据库)
# setup_database() {
#     print_message "检查数据库..."
#     export PGPASSWORD=admin123
#     
#     # 检查 postgres 用户是否存在
#     if ! psql -U postgres -c "\du" | grep -q "postgres"; then
#         print_message "创建 postgres 用户..."
#         createuser -s postgres
#         print_success "postgres 用户创建成功"
#     fi
#     
#     # 检查数据库是否存在
#     if ! psql -U postgres -c "SELECT 1 FROM pg_database WHERE datname='tunshuedu_db'" | grep -q 1; then
#         print_message "创建数据库..."
#         psql -U postgres -c "CREATE DATABASE tunshuedu_db;"
#         print_success "数据库创建成功"
#     else
#         print_success "数据库已存在"
#     fi
#     
#     # 确保 plpgsql 扩展已安装
#     print_message "检查 plpgsql 扩展..."
#     psql -U postgres -d tunshuedu_db -c "CREATE EXTENSION IF NOT EXISTS plpgsql;"
#     
#     # 删除已存在的表（如果存在）
#     print_message "清理已存在的表..."
#     psql -U postgres -d tunshuedu_db -c "DROP TABLE IF EXISTS users CASCADE;"
#     
#     # 创建数据表
#     print_message "创建数据表..."
#     psql -U postgres -d tunshuedu_db -f backend/create_tables.sql
#     print_success "数据表创建成功"
# }

# 检查并关闭占用端口的进程
check_ports() {
    print_message "检查端口占用..."
    for port in 8000 3000; do
        if lsof -ti:$port &> /dev/null; then
            print_warning "端口 $port 被占用，正在关闭相关进程..."
            lsof -ti:$port | xargs kill -9 2>/dev/null || true
            sleep 1
        fi
    done
}

# 设置后端环境
setup_backend() {
    print_message "设置后端环境..."
    cd backend

    # 检查虚拟环境
    if [ ! -d ".venv" ]; then
        print_message "创建虚拟环境..."
        python3.10 -m venv .venv
        print_success "虚拟环境创建成功"
    else
        print_success "虚拟环境已存在"
    fi

    # 激活虚拟环境
    source .venv/bin/activate

    # 安装依赖
    print_message "安装后端依赖..."
    pip install -r requirements.txt
    print_success "后端依赖安装完成"
    
    # 初始化数据库 (已注释 - 使用Supabase云端数据库)
    # print_message "初始化数据库数据..."
    # python init_db.py
    # print_success "数据库初始化完成"

    cd ..
}

# 设置前端环境
setup_frontend() {
    print_message "设置前端环境..."
    cd frontend

    # 检查依赖是否需要更新
    if [ ! -d "node_modules" ]; then
        print_message "安装前端依赖..."
        npm install
        print_success "前端依赖安装完成"
    else
        # 计算package.json的哈希值
        package_hash=$(md5sum package.json | awk '{print $1}')
        
        # 检查是否存在之前保存的哈希值
        if [ -f "node_modules/.package-hash" ]; then
            old_hash=$(cat node_modules/.package-hash)
            
            # 如果哈希值不同，说明package.json有变化
            if [ "$package_hash" != "$old_hash" ]; then
                print_message "检测到package.json变更，更新依赖..."
                npm install
                print_success "依赖更新完成"
            else
                print_success "依赖无变化，无需更新"
            fi
        else
            print_message "首次检查依赖状态，更新依赖..."
            npm install
            print_success "依赖更新完成"
        fi
        
        # 保存当前package.json的哈希值
        echo "$package_hash" > node_modules/.package-hash
    fi

    cd ..
}

# 启动服务
start_services() {
    print_message "启动服务..."

    # 启动后端
    print_message "启动后端服务..."
    cd backend
    source .venv/bin/activate
    python main.py &
    BACKEND_PID=$!
    cd ..

    # 等待后端启动
    print_message "等待后端服务启动..."
    sleep 5

    # 启动前端
    print_message "启动前端服务..."
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..

    # 显示访问信息
    print_success "系统启动成功！"
    print_success "前端地址: http://localhost:3000"
    print_success "后端地址: http://localhost:8000"
    print_success "管理员账号: admin"
    print_success "管理员密码: admin123"
    print_message "按 Ctrl+C 停止所有服务"

    # 设置退出处理
    trap "kill $BACKEND_PID $FRONTEND_PID; exit" INT TERM

    # 等待用户中断
    wait
}

# 主函数
main() {
    print_message "开始启动 TunShuEdu 系统..."

    # 检查必要命令
    check_command python3.10
    check_command npm
    # check_command psql  # 已注释 - 无需本地PostgreSQL

    # 执行各个步骤 (已注释本地数据库相关函数)
    # check_postgres
    # setup_database
    check_ports
    setup_backend
    setup_frontend
    start_services
}

# 执行主函数
main 