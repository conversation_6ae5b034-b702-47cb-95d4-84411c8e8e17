{"cells": [{"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import json\n", "import pandas as pd\n", "from pathlib import Path\n", "\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import wordninja\n", "from thefuzz import fuzz, process\n", "\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from requests.adapters import HTTPAdapter\n", "import ssl"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["case_df = pd.read_csv('案例数据库.csv')\n", "program_df = pd.read_csv('专业数据库.csv')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 境内院校数据库基本制备"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 院校中英文名称提取"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# unique_schools = program_df.drop_duplicates(subset=['学校中文名', '学校英文名'])\n", "# en_school_dict = dict(zip(unique_schools['学校中文名'], unique_schools['学校英文名']))\n", "\n", "# cn_school_dict = {name: '' for name in cn_school_df['学校名称']}\n", "# merged_school_dict = {**cn_school_dict,**en_school_dict}\n", "# with open('院校数据/schools.json', 'w', encoding='utf-8') as f:\n", "#     json.dump(merged_school_dict, f, ensure_ascii=False, indent=4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 软科榜单处理"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# 读取Excel文件\n", "file_path = '院校数据/2025年软科中国大学排名.xlsx'\n", "xls = pd.ExcelFile(file_path)\n", "\n", "# 初始化一个空的DataFrame用于存储最终结果\n", "ruanke_df = pd.DataFrame()\n", "\n", "# 定义需要处理的子表名列表\n", "sheet_names = ['主榜', '医药类', '财经类', '语言类', '政法类', '民族类', '体育类', '合作办学大学']\n", "\n", "for sheet_name in sheet_names:\n", "    # 检查工作表是否存在\n", "    if sheet_name not in xls.sheet_names:\n", "        print(f\"Sheet '{sheet_name}' 不存在于Excel文件中\")\n", "        continue\n", "    \n", "    # 读取每个子表的数据\n", "    df = pd.read_excel(xls, sheet_name)\n", "    \n", "    if sheet_name == '主榜':\n", "        # 提取主榜中的特定列\n", "        columns_to_keep = ['2025排名', '学校名称', '学校类型']\n", "    else:\n", "        # 提取其他子表中的特定列\n", "        columns_to_keep = ['主榜参考排名', '学校名称', '学校类型']\n", "    \n", "    # 检查是否存在所需的列\n", "    existing_columns = set(df.columns).intersection(columns_to_keep)\n", "    if not existing_columns:\n", "        print(f\"Sheet '{sheet_name}' 缺少所需列: {columns_to_keep}\")\n", "        continue\n", "    \n", "    # 提取并重命名列\n", "    sub_df = df[list(existing_columns)].copy()\n", "    sub_df.rename(columns={'2025排名': '排名', '主榜参考排名': '排名'}, inplace=True)\n", "    \n", "    # 将子表数据追加到最终的DataFrame中\n", "    ruanke_df = pd.concat([ruanke_df, sub_df], ignore_index=True)\n", "\n", "# 数据清洗处理\n", "ruanke_df.dropna(how='all', inplace=True)\n", "\n", "# 将'排名'列中所有值为'500+'的变为500\n", "ruanke_df['排名'] = ruanke_df['排名'].apply(lambda x: 500 if x == '500+' else x)\n", "\n", "# 将'排名'列转换为int类型\n", "ruanke_df['排名'] = ruanke_df['排名'].astype(int)\n", "\n", "# 按排名排序\n", "ruanke_df.sort_values(by='排名', inplace=True)\n", "\n", "# 重置index\n", "ruanke_df.reset_index(drop=True, inplace=True)\n", "\n", "# ruanke_df.to_csv(\"境内院校数据库.csv\", index=False, encoding='utf-8-sig')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 软科表与教育部官方院校名单表合并"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校类型</th>\n", "      <th>排名</th>\n", "      <th>学校名称</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>530</th>\n", "      <td>医药</td>\n", "      <td>376</td>\n", "      <td>内蒙古科技大学包头医学院</td>\n", "    </tr>\n", "    <tr>\n", "      <th>634</th>\n", "      <td>综合</td>\n", "      <td>460</td>\n", "      <td>内蒙古科技大学包头师范学院</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    学校类型   排名           学校名称\n", "530   医药  376   内蒙古科技大学包头医学院\n", "634   综合  460  内蒙古科技大学包头师范学院"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["cn_school_df = pd.read_excel('./院校数据/境内院校.xlsx')\n", "ruanke_df[~ruanke_df['学校名称'].isin(cn_school_df['学校名称'])]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# 处理 cn_school_df\n", "# 将 '学校标识码' 列转换为 str 类型\n", "cn_school_df['学校标识码'] = cn_school_df['学校标识码'].astype(str)\n", "\n", "# 去掉 '办学层次' 列\n", "if '办学层次' in cn_school_df.columns:\n", "    cn_school_df.drop(columns=['办学层次'], inplace=True)\n", "\n", "# 将 '排名' 列重命名为 '软科排名'\n", "if '排名' in ruanke_df.columns:\n", "    ruanke_df.rename(columns={'排名': '软科排名'}, inplace=True)\n", "\n", "# 合并两个 DataFrame，以 outer 方式连接\n", "merged_cn_school_df = pd.merge(ruanke_df, cn_school_df, on='学校名称', how='outer')\n", "\n", "# 按 '软科排名' 排序，NaN 值排在最后\n", "merged_cn_school_df.sort_values(by='软科排名', ascending=True, na_position='last', inplace=True)\n", "merged_cn_school_df['软科排名'] = merged_cn_school_df['软科排名'].astype('Int64')\n", "\n", "# 重置 index\n", "merged_cn_school_df.reset_index(drop=True, inplace=True)\n", "\n", "# 重新排列列的顺序\n", "merged_cn_school_df = merged_cn_school_df[['学校名称', '学校类型', '学校标识码', '所在地', '主管部门', '软科排名', '备注']]"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校名称</th>\n", "      <th>学校类型</th>\n", "      <th>学校标识码</th>\n", "      <th>所在地</th>\n", "      <th>主管部门</th>\n", "      <th>软科排名</th>\n", "      <th>备注</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>清华大学</td>\n", "      <td>综合</td>\n", "      <td>4111010003</td>\n", "      <td>北京市</td>\n", "      <td>教育部</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>北京大学</td>\n", "      <td>综合</td>\n", "      <td>4111010001</td>\n", "      <td>北京市</td>\n", "      <td>教育部</td>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>浙江大学</td>\n", "      <td>综合</td>\n", "      <td>4133010335</td>\n", "      <td>杭州市</td>\n", "      <td>教育部</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>上海交通大学</td>\n", "      <td>综合</td>\n", "      <td>4131010248</td>\n", "      <td>上海市</td>\n", "      <td>教育部</td>\n", "      <td>4</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>复旦大学</td>\n", "      <td>综合</td>\n", "      <td>4131010246</td>\n", "      <td>上海市</td>\n", "      <td>教育部</td>\n", "      <td>5</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1305</th>\n", "      <td>黑龙江工程学院昆仑旅游学院</td>\n", "      <td>NaN</td>\n", "      <td>4123013304</td>\n", "      <td>哈尔滨市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1306</th>\n", "      <td>黑龙江财经学院</td>\n", "      <td>NaN</td>\n", "      <td>4123013298</td>\n", "      <td>哈尔滨市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1307</th>\n", "      <td>齐鲁医药学院</td>\n", "      <td>NaN</td>\n", "      <td>4137010825</td>\n", "      <td>淄博市</td>\n", "      <td>山东省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1308</th>\n", "      <td>齐鲁理工学院</td>\n", "      <td>NaN</td>\n", "      <td>4137013998</td>\n", "      <td>济南市</td>\n", "      <td>山东省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1309</th>\n", "      <td>齐齐哈尔工程学院</td>\n", "      <td>NaN</td>\n", "      <td>4123012729</td>\n", "      <td>齐齐哈尔市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1310 rows × 7 columns</p>\n", "</div>"], "text/plain": ["               学校名称 学校类型       学校标识码    所在地     主管部门  软科排名   备注\n", "0              清华大学   综合  4111010003    北京市      教育部     1  NaN\n", "1              北京大学   综合  4111010001    北京市      教育部     2  NaN\n", "2              浙江大学   综合  4133010335    杭州市      教育部     3  NaN\n", "3            上海交通大学   综合  4131010248    上海市      教育部     4  NaN\n", "4              复旦大学   综合  4131010246    上海市      教育部     5  NaN\n", "...             ...  ...         ...    ...      ...   ...  ...\n", "1305  黑龙江工程学院昆仑旅游学院  NaN  4123013304   哈尔滨市  黑龙江省教育厅  <NA>   民办\n", "1306        黑龙江财经学院  NaN  4123013298   哈尔滨市  黑龙江省教育厅  <NA>   民办\n", "1307         齐鲁医药学院  NaN  4137010825    淄博市   山东省教育厅  <NA>   民办\n", "1308         齐鲁理工学院  NaN  4137013998    济南市   山东省教育厅  <NA>   民办\n", "1309       齐齐哈尔工程学院  NaN  4123012729  齐齐哈尔市  黑龙江省教育厅  <NA>   民办\n", "\n", "[1310 rows x 7 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_cn_school_df"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# merged_cn_school_df.to_csv(\"境内院校数据库.csv\", index=False, encoding='utf-8-sig')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 境外院校数据库"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 基本数据爬取"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# 直接发送HTTP请求获取网页内容，会经典报错SSLError: HTTPSConnectionPool(host='www.compassedu.hk', port=443):\n", "#  Max retries exceeded with url: /qs (Caused by SSLError(SSLError(1, '[SSL: DH_KEY_TOO_SMALL] dh key too small (_ssl.c:1010)')))\n", "# url = \"https://www.compassedu.hk/qs\"\n", "# response = requests.get(url)\n", "\n", "# 还是要通过创建一个自定义的HTTP适配器解决\n", "class SSLContextAdapter(HTTPAdapter):\n", "    def __init__(self, ssl_context=None, **kwargs):\n", "        self.ssl_context = ssl_context\n", "        super().__init__(**kwargs)\n", "\n", "    def init_poolmanager(self, *args, **kwargs):\n", "        kwargs['ssl_context'] = self.ssl_context\n", "        return super(SSLConte<PERSON>t<PERSON><PERSON><PERSON><PERSON>, self).init_poolmanager(*args, **kwargs)\n", "        \n", "# 创建一个自定义的SSL上下文\n", "context = ssl.create_default_context()\n", "context.set_ciphers('DEFAULT:@SECLEVEL=1')  # 设置较低的安全级别要求\n", "\n", "# 创建一个session对象并挂载我们的自定义适配器\n", "session = requests.Session()\n", "adapter = SSLContextAdapter(ssl_context=context)\n", "session.mount('https://', adapter)\n", "\n", "url = \"https://www.compassedu.hk/qs\"\n", "headers = {\n", "        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.93 Safari/537.36'\n", "}\n", "response = session.get(url, headers=headers)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["html_content = response.text\n", "\n", "# 使用BeautifulSoup解析HTML内容\n", "soup = BeautifulSoup(html_content, 'html.parser')\n", "\n", "# 初始化一个空列表来存储学校信息\n", "school_data = []\n", "\n", "# 查找包含所有院校排名的<div class=\"result-list\">元素\n", "result_list = soup.find('div', class_='result-list')\n", "\n", "# 遍历每一个<a>标签，其中包含了学校的详细信息\n", "for a_tag in result_list.find_all('a'):\n", "    # 构造完整的学校网站链接\n", "    school_website = 'https://www.compassedu.hk' + a_tag['href']\n", "    \n", "    # 获取每个学校详情所在的<div class=\"rank-item\">\n", "    rank_item = a_tag.find('div', class_='rank-item')\n", "    \n", "    # 提取学校排名\n", "    rank = rank_item.find('div', class_='rank-tr1').text.strip()\n", "    \n", "    # 提取学校logo链接\n", "    logo_img = rank_item.find('img')\n", "    logo_link = logo_img['src'] if logo_img else None\n", "    \n", "    # 提取学校名称（中文和英文）\n", "    univ_name_div = rank_item.find('div', class_='univ-name')\n", "    chinese_name = univ_name_div.find('div', class_='cname line-one').text.strip()\n", "    english_name = univ_name_div.find('div', class_='ename line-one').text.strip()\n", "    \n", "    # 将提取的信息添加到列表中\n", "    school_data.append({\n", "        '学校中文名': chinese_name,\n", "        '学校英文名': english_name,\n", "        '学校QS排名': rank,\n", "        '学校网站链接': school_website,\n", "        '学校logo链接': logo_link\n", "    })\n", "\n", "# 将学校数据转换为DataFrame\n", "aboard_school_df = pd.DataFrame(school_data)\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校中文名</th>\n", "      <th>学校英文名</th>\n", "      <th>学校QS排名</th>\n", "      <th>学校网站链接</th>\n", "      <th>学校logo链接</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>1</td>\n", "      <td>https://www.compassedu.hk/univ_85</td>\n", "      <td>http://logo.compassedu.hk/85.png?imageMogr2/au...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>帝国理工学院</td>\n", "      <td>Imperial College London</td>\n", "      <td>2</td>\n", "      <td>https://www.compassedu.hk/univ_3</td>\n", "      <td>http://logo.compassedu.hk/3.png?imageMogr2/aut...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>斯坦福大学</td>\n", "      <td>Stanford University</td>\n", "      <td>3</td>\n", "      <td>https://www.compassedu.hk/univ_86</td>\n", "      <td>http://logo.compassedu.hk/86.png?imageMogr2/au...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>牛津大学</td>\n", "      <td>University of Oxford</td>\n", "      <td>4</td>\n", "      <td>https://www.compassedu.hk/univ_8</td>\n", "      <td>http://logo.compassedu.hk/8.png?imageMogr2/aut...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>哈佛大学</td>\n", "      <td>Harvard University</td>\n", "      <td>5</td>\n", "      <td>https://www.compassedu.hk/univ_80</td>\n", "      <td>http://logo.compassedu.hk/80.png?imageMogr2/au...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>497</th>\n", "      <td>于韦斯屈莱大学</td>\n", "      <td>University of Jyväskylä</td>\n", "      <td>498</td>\n", "      <td>https://www.compassedu.hk/univ_1313</td>\n", "      <td>http://logo.compassedu.hk/1313.png?imageMogr2/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>498</th>\n", "      <td>托木斯克国立大学</td>\n", "      <td>Tomsk State University</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/univ_1391</td>\n", "      <td>http://logo.compassedu.hk/1391.png?imageMogr2/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>499</th>\n", "      <td>哥斯达黎加大学</td>\n", "      <td>Universidad de Costa Rica</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/univ_1788</td>\n", "      <td>http://logo.compassedu.hk/1788.png?imageMogr2/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>500</th>\n", "      <td>西北工业大学</td>\n", "      <td>Northwestern Polytechnical University</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/college_2462</td>\n", "      <td>http://info.compassedu.hk/collegee/2021/2462.p...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>501</th>\n", "      <td>中央昆士兰大学</td>\n", "      <td>Central Queensland University</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/univ_1527</td>\n", "      <td>http://logo.compassedu.hk/1527.png?imageMogr2/...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>502 rows × 5 columns</p>\n", "</div>"], "text/plain": ["        学校中文名                                  学校英文名 学校QS排名  \\\n", "0      麻省理工学院  Massachusetts Institute of Technology      1   \n", "1      帝国理工学院                Imperial College London      2   \n", "2       斯坦福大学                    Stanford University      3   \n", "3        牛津大学                   University of Oxford      4   \n", "4        哈佛大学                     Harvard University      5   \n", "..        ...                                    ...    ...   \n", "497   于韦斯屈莱大学                University of Jyväskylä    498   \n", "498  托木斯克国立大学                 Tomsk State University    499   \n", "499   哥斯达黎加大学              Universidad de Costa Rica    499   \n", "500    西北工业大学  Northwestern Polytechnical University    499   \n", "501   中央昆士兰大学          Central Queensland University    499   \n", "\n", "                                     学校网站链接  \\\n", "0         https://www.compassedu.hk/univ_85   \n", "1          https://www.compassedu.hk/univ_3   \n", "2         https://www.compassedu.hk/univ_86   \n", "3          https://www.compassedu.hk/univ_8   \n", "4         https://www.compassedu.hk/univ_80   \n", "..                                      ...   \n", "497     https://www.compassedu.hk/univ_1313   \n", "498     https://www.compassedu.hk/univ_1391   \n", "499     https://www.compassedu.hk/univ_1788   \n", "500  https://www.compassedu.hk/college_2462   \n", "501     https://www.compassedu.hk/univ_1527   \n", "\n", "                                              学校logo链接  \n", "0    http://logo.compassedu.hk/85.png?imageMogr2/au...  \n", "1    http://logo.compassedu.hk/3.png?imageMogr2/aut...  \n", "2    http://logo.compassedu.hk/86.png?imageMogr2/au...  \n", "3    http://logo.compassedu.hk/8.png?imageMogr2/aut...  \n", "4    http://logo.compassedu.hk/80.png?imageMogr2/au...  \n", "..                                                 ...  \n", "497  http://logo.compassedu.hk/1313.png?imageMogr2/...  \n", "498  http://logo.compassedu.hk/1391.png?imageMogr2/...  \n", "499  http://logo.compassedu.hk/1788.png?imageMogr2/...  \n", "500  http://info.compassedu.hk/collegee/2021/2462.p...  \n", "501  http://logo.compassedu.hk/1527.png?imageMogr2/...  \n", "\n", "[502 rows x 5 columns]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["aboard_school_df"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["170"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["program_df['学校中文名'].unique()"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'伍斯特理工学院',\n", " '伦敦商学院',\n", " '伦敦大学亚非学院',\n", " '伦敦大学城市学院',\n", " '伦敦大学金史密斯学院',\n", " '伦斯勒理工学院',\n", " '佐治亚大学',\n", " '佐治亚州立大学',\n", " '佩珀代因大学',\n", " '克兰菲尔德大学',\n", " '克莱姆森大学',\n", " '北卡罗来纳州立大学罗利分校',\n", " '南卫理公会大学',\n", " '叶史瓦大学',\n", " '圣塔克拉拉大学',\n", " '威廉玛丽学院',\n", " '威斯敏斯特大学',\n", " '布兰迪斯大学',\n", " '康涅狄格大学',\n", " '德州大学达拉斯分校',\n", " '斯蒂文斯理工学院',\n", " '新加坡科技设计大学',\n", " '新加坡管理大学',\n", " '旧金山大学',\n", " '明尼苏达大学双城分校',\n", " '本特利大学',\n", " '杜兰大学',\n", " '杨百翰大学',\n", " '波士顿学院',\n", " '澳门城市大学',\n", " '澳门理工大学',\n", " '白金汉大学',\n", " '福特汉姆大学',\n", " '科罗拉多大学波德分校',\n", " '纽约市立大学巴鲁克学院',\n", " '维克森林大学',\n", " '罗格斯大学',\n", " '罗格斯大学纽瓦克分校',\n", " '美利坚大学',\n", " '考文垂大学',\n", " '贝勒大学',\n", " '里海大学',\n", " '雪城大学',\n", " '香港岭南大学',\n", " '香港恒生大学',\n", " '香港教育大学',\n", " '香港树仁大学',\n", " '香港都会大学'}"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["set(program_df['学校中文名'].unique()) - set(aboard_school_df['学校中文名'].unique())"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['学校中文名', '学校英文名', '学校QS排名', '学校网站链接', '学校logo链接'], dtype='object')"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["aboard_school_df.columns"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["# 如果需要，可以将DataFrame保存到CSV文件中\n", "aboard_school_df.to_csv('院校数据/aboard_schools.csv', index=False, encoding='utf-8-sig')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 如果需要，可以将DataFrame保存到CSV文件中\n", "aboard_school_df.to_csv('境外院校数据库.csv', index=False, encoding='utf-8-sig')"]}], "metadata": {"kernelspec": {"display_name": "tunshu_data", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}