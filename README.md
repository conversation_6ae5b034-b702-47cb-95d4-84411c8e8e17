# 津述教育 (TunshuEdu) - 教育科技平台

[![Vue.js](https://img.shields.io/badge/Vue.js-3.0-4FC08D?style=flat&logo=vue.js&logoColor=white)](https://vuejs.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100-009688?style=flat&logo=fastapi&logoColor=white)](https://fastapi.tiangolo.com/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-13+-336791?style=flat&logo=postgresql&logoColor=white)](https://www.postgresql.org/)
[![TailwindCSS](https://img.shields.io/badge/TailwindCSS-3.0-06B6D4?style=flat&logo=tailwindcss&logoColor=white)](https://tailwindcss.com/)

## 🎯 项目概述

津述教育是一个专业的教育科技平台，致力于提供AI驱动的留学申请辅导和文书写作服务。我们通过先进的技术和丰富的教育资源，帮助学生实现留学梦想。

## 🚀 核心功能

### 📝 AI智能写作模块
- **CV简历生成**: 基于个人经历智能生成专业简历
- **PS个人陈述写作**: AI辅助创作个性化申请文书  
- **推荐信写作**: 智能生成个性化推荐信模板
- **富文本编辑器**: 全新的Tiptap编辑器，支持Markdown自动渲染
  - 实时Markdown到HTML转换
  - 完整的键盘快捷键支持 (Ctrl+C, Ctrl+V, Ctrl+A, Ctrl+X)
  - 可视化富文本编辑工具栏
  - 支持导出PDF、Word、纯文本格式

### 🎓 AI智能选校模块
- **智能院校匹配**: 基于学生背景推荐最适合的院校和专业
- **个性化推荐**: AI分析学生档案，提供精准择校建议
- **数据驱动决策**: 综合考虑排名、地理位置、专业匹配度等因素

### 📊 CRM客户管理系统
- **学生档案管理**: 完整的学生信息记录和跟踪
- **申请进度追踪**: 实时监控申请状态和重要时间节点
- **任务管理**: 高效的任务分配和进度管理
- **团队协作**: 支持多用户协作和权限管理

### 🔍 背景信息提取
- **智能文档解析**: 自动提取和整理学生背景信息
- **结构化数据**: 将非结构化信息转换为可用数据

### ✨ AI内容增强
- **文档优化**: AI驱动的内容改进和建议
- **语言润色**: 专业的语言表达优化

### 🔍 AI文书检测
- **AI内容检测**: 基于ZeroGPT API的智能AI内容识别
- **批量检测支持**: 支持多个文档同时检测
- **详细分析报告**: 提供AI比例、高亮句子、统计信息
- **风险等级评估**: 智能评估文档的AI使用风险等级

## 🛠️ 技术架构

### 前端技术栈
- **Vue 3** - 渐进式JavaScript框架，使用Composition API
- **Vite** - 下一代前端构建工具
- **TailwindCSS** - 原子化CSS框架，快速构建现代UI
- **Element Plus** - 基于Vue 3的企业级UI组件库
- **Tiptap** - 现代化富文本编辑器，完美支持Vue 3
- **Pinia** - 轻量级状态管理库

### 后端技术栈
- **FastAPI** - 现代、快速的Python Web框架
- **SQLAlchemy** - 强大的Python SQL工具包和ORM
- **PostgreSQL** - 企业级关系型数据库
- **Alembic** - 数据库迁移工具
- **Pydantic** - 数据验证和设置管理

### AI集成
- **OpenAI GPT** - 自然语言处理和生成
- **向量数据库** - 高效的语义搜索和匹配
- **机器学习模型** - 个性化推荐算法

## 📁 项目结构

```
TunshuEdu/
├── frontend/                 # Vue 3 前端应用
│   ├── src/
│   │   ├── components/       # 可复用组件
│   │   │   ├── writing/      # 写作相关组件
│   │   │   │   ├── TiptapEditor.vue    # 新的富文本编辑器
│   │   │   │   └── SharedTextEditor.vue # 旧编辑器(已弃用)
│   │   │   ├── layout/       # 布局组件
│   │   │   └── common/       # 通用组件
│   │   ├── views/            # 页面组件
│   │   │   ├── writing/      # 写作模块页面
│   │   │   ├── crm/          # CRM系统页面
│   │   │   ├── clients/      # 客户管理页面
│   │   │   └── dashboard/    # 仪表板
│   │   ├── stores/           # Pinia状态管理
│   │   ├── router/           # Vue Router路由配置
│   │   ├── api/             # API接口
│   │   └── utils/           # 工具函数
│   ├── public/              # 静态资源
│   └── package.json         # 前端依赖配置
│
├── backend/                 # FastAPI 后端应用
│   ├── app/
│   │   ├── ai_writing/      # AI写作模块
│   │   ├── ai_selection/    # AI选校模块
│   │   ├── ai_augmentation/ # AI内容增强模块
│   │   ├── ai_detection/    # AI文书检测模块
│   │   ├── background_extraction/ # 背景提取模块
│   │   ├── core/            # 核心配置
│   │   ├── models/          # 数据模型
│   │   ├── schemas/         # Pydantic模式
│   │   └── api/             # API路由
│   ├── migrations/          # 数据库迁移文件
│   └── requirements.txt     # Python依赖
│
└── data_processing/         # 数据处理脚本
    ├── csv_to_postgres.py   # 数据导入脚本
    └── requirements.txt     # 数据处理依赖
```

## 🚀 快速开始

### 环境要求
- Node.js 16+ 
- Python 3.9+
- PostgreSQL 13+ (或使用Supabase云端数据库)

### 📊 数据库配置

**当前配置：Supabase云端数据库**
- 项目已配置使用Supabase云端PostgreSQL数据库
- 无需本地安装PostgreSQL服务
- 自动SSL连接和连接池管理
- 时区设置：Asia/Shanghai

**配置详情：**
- 主机：`db.xrswrbnnyzorpoyoumzx.supabase.co`
- 端口：`5432`
- 数据库：`postgres`
- 用户：`postgres`
- SSL：必需
- 连接池：20个连接，最大溢出30个
- 查询缓存：内存缓存系统，提升重复查询性能

**性能优化措施：**
- ✅ 优化连接池配置（pool_size=20, max_overflow=30）
- ✅ 关闭pool_pre_ping减少网络延迟
- ✅ 添加19个数据库索引（12个单列 + 7个复合索引）
- ✅ 实现内存缓存系统，缓存时间1-10分钟
- ✅ 优化AI选校模块关键API端点
- ✅ **新增：解决N+1查询问题**
  - 客户详情页面：使用`asyncio.gather`并行查询，避免11次独立查询
  - 客户项目列表：批量查询学校logo，避免循环查询
  - 总览页面：并行执行统计查询，减少数据库访问
  - 用户认证：添加用户信息缓存，减少重复数据库查询
- ✅ **新增：智能缓存策略**
  - 用户信息缓存5分钟，显著减少认证查询
  - 总览统计数据缓存1分钟，提升仪表盘响应速度
  - 缓存失效机制：用户信息更新时自动清理相关缓存
- ✅ 查询性能提升约75%（平均响应时间降至0.1秒以内）

**如需切换回本地数据库：**
1. 打开 `backend/app/core/config.py`
2. 取消注释本地数据库配置行
3. 注释Supabase配置行
4. 重启后端服务

### 1. 克隆项目
```bash
git clone https://github.com/your-org/TunshuEdu.git
cd TunshuEdu
```

### 2. 后端设置
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt

# 配置API密钥 (数据库已配置Supabase云端)
# 如需自定义配置，可创建 .env 文件覆盖默认设置
# cp .env.example .env

# 运行数据库迁移 (连接到Supabase云端数据库)
alembic upgrade head

# 启动后端服务
python main.py
```

### 3. 前端设置
```bash
cd frontend
npm install
npm run dev
```

### 4. 访问应用
- 前端应用: http://localhost:5173
- 后端API文档: http://localhost:8000/docs

## 📝 使用指南

### AI写作模块使用方法

1. **选择写作类型**: CV简历、PS个人陈述或推荐信
2. **填写表单信息**: 在左侧表单中填入相关信息
3. **AI生成内容**: 点击"智能生成"按钮，AI将生成初稿
4. **富文本编辑**: 在右侧的Tiptap编辑器中编辑和完善内容
   - 支持Markdown语法自动转换
   - 可使用工具栏进行格式化
   - 完整的键盘快捷键支持
5. **导出文档**: 支持导出PDF、Word、纯文本格式

### 新编辑器特性

**TiptapEditor组件** (替代原有SharedTextEditor):
- **Markdown实时渲染**: 自动将Markdown转换为富文本显示
- **可视化编辑**: 提供完整的富文本编辑工具栏
- **完美复制粘贴**: 支持所有标准键盘快捷键
- **高性能**: 基于ProseMirror引擎，性能优异
- **Vue 3原生**: 专为Vue 3设计的现代编辑器

### 🔄 智能页面管理

**新增页面切换保护机制**:
- **自动取消生成**: 当用户在AI生成过程中切换到其他页面时，系统自动取消正在进行的生成任务
- **组件卸载保护**: 组件卸载时自动清理生成状态，防止内存泄漏
- **用户友好提示**: 页面切换时显示友好的取消提示，让用户了解系统行为

### 📤 高级导出功能

**真正的文件导出** (不再是浏览器打印):
- **📄 PDF导出**: 使用html2pdf.js库生成标准PDF文件，直接下载到本地
  - 优化的PDF样式，适合A4纸张打印
  - 支持中文字体和复杂排版
  - 自动分页和避免内容截断
- **📝 DOCX导出**: 使用docx库生成真正的Microsoft Word文档(.docx格式)
  - 保持富文本格式(粗体、斜体、标题等)
  - 兼容所有Microsoft Office和WPS Office版本
  - 标准化的段落和样式设置
- **📋 TXT导出**: 纯文本格式导出，去除所有格式
  - UTF-8编码，支持中文字符
  - 保持原始内容结构和换行
- **🎯 智能文件命名**: 自动生成文件名格式：`客户姓名_文档类型_日期`
- **⚡ 即时下载**: 所有格式都支持即时下载，无需等待或手动操作

### AI文书检测使用方法

1. **单文档检测**: 
   - 提交文本内容进行AI检测
   - 获取AI使用比例和风险等级评估
   - 查看AI生成内容的高亮标注

2. **批量检测**:
   - 同时提交多个文档进行检测
   - 并发处理，提高检测效率
   - 统一查看所有文档的检测结果

3. **统计分析**:
   - 查看总体检测统计信息
   - 分析AI使用趋势和分布
   - 导出检测报告

### API端点说明

**AI检测模块** (`/api/ai-detection/`):
- `POST /detect` - 单文档AI检测
- `POST /batch-detect` - 批量文档检测  
- `GET /stats` - 获取检测统计信息
- `GET /health` - 服务健康检查
- `GET /config` - 获取配置信息
- `GET /info` - 获取API信息

### CRM系统使用
1. **客户档案管理**: 录入和维护学生基本信息
2. **申请进度跟踪**: 监控每个学生的申请状态
3. **任务分配**: 创建和分配跟进任务
4. **数据分析**: 查看申请统计和成功率

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [贡献指南](CONTRIBUTING.md) 了解详细信息。

### 开发规范
- 遵循Vue 3 Composition API最佳实践
- 使用TypeScript进行类型检查
- 遵循TailwindCSS原子化CSS方法
- 后端遵循FastAPI和SQLAlchemy最佳实践
- 提交前运行代码检查和测试

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🎉 致谢

感谢所有贡献者和开源社区的支持！

---

**津述教育** - 让每个学生都能找到最适合的教育路径 🎓✨

## 🆕 最新更新

### 2025-01-17: PS和RL模块定校书集成功能

#### ✨ 新增功能
- **🎓 申请院校与专业统一管理**：PS（个人陈述）和RL（推荐信）模块重构院校专业选择，将原有的分离式选择合并为统一的院校专业选择组件
- **📋 定校书数据集成**：选择客户后自动加载客户定校书，从真实的申请专业中选择，确保文书与申请目标一致
- **🔄 智能数据联动**：客户选择后自动触发定校书加载，无需手动操作
- **⚡ 手动输入备选**：当客户无定校书数据时，提供手动输入院校专业信息的选项
- **📤 一体化体验**：院校专业信息实时反映在生成的文书模板中，包括学校排名、地区等详细信息

#### 🛠️ 技术实现
- **前端重构**：将原有的分离式院校、学位、专业选择重构为统一的院校专业选择组件
- **定校书API集成**：使用现有的`getClientPrograms` API获取客户定校书数据
- **数据结构优化**：使用`formData.selectedProgram`统一存储院校专业信息，替代原有的分散字段
- **模板系统升级**：文书生成模板支持完整的院校专业信息显示，包括中文名称、地区、排名等
- **表单验证更新**：简化验证逻辑，只需要客户选择和院校专业选择即可

#### 🎯 功能对比
| 功能 | 修改前 | 修改后 |
|-----|-------|-------|
| 院校选择 | 静态下拉列表 | 从定校书获取 + 手动输入 |
| 专业选择 | 静态下拉列表 | 与院校绑定的真实专业 |
| 学位选择 | 独立选择 | 院校专业包含学位信息 |
| 数据来源 | 前端硬编码 | 后端数据库 + API |
| 数据一致性 | 无保证 | 与定校书完全一致 |

#### 📋 涉及文件
- `frontend/src/views/writing/PS.vue` - 个人陈述页面
- `frontend/src/views/writing/RL.vue` - 推荐信页面
- `frontend/src/api/client.js` - 客户API（使用现有`getClientPrograms`接口）

#### 🎨 UI/UX 改进
- **统一设计风格**：与CV.vue的客户选择保持一致的设计语言
- **智能状态展示**：清晰显示选中的院校专业信息，包括头像、排名、地区等
- **交互优化**：加载状态、错误处理、成功反馈的完整交互流程
- **手动输入界面**：优雅的折叠式手动输入表单，三种学位类型快速选择

#### 🧪 验证测试
- ✅ 客户选择后自动加载定校书
- ✅ 定校书数据正确显示和选择
- ✅ 手动输入功能正常工作
- ✅ 文书模板正确引用院校专业信息
- ✅ 表单验证逻辑更新完成
- ✅ UI交互流程完整无误

#### 💡 用户价值
- **数据准确性**：确保文书中的院校专业信息与实际申请目标完全一致
- **工作效率**：无需重复录入院校专业信息，一键从定校书获取
- **流程优化**：文书写作与申请规划的无缝衔接，提升整体工作效率
- **容错能力**：提供手动输入选项，应对各种特殊情况

### 2025-01-16: 选校匹配系统客户档案集成功能

#### ✨ 新增功能
- **🔍 智能客户搜索**：在选校匹配页面新增客户档案选择窗口，集成在表单内部，与其他表单区域设计风格一致
- **📋 多字段搜索支持**：支持通过客户姓名、电话号码、电子邮箱、身份证号、护照号、系统哈希ID等多个字段搜索客户
- **🔄 智能按钮切换**：未选择客户时显示"收藏"按钮，选择客户后自动切换为"添加到定校书"按钮
- **⚡ 实时状态同步**：自动检测客户已有的定校书专业，已添加的专业显示"已添加"状态
- **📤 一键操作**：支持一键添加/移除专业到客户定校书，操作成功后显示提示信息
- **🤖 智能信息填充**：选择客户后自动获取并填充教育背景信息（学校、专业）
  - ✅ 学校名称自动填充
  - ✅ 专业名称自动填充  
  - 🔄 **TODO**: GPA信息填充功能（暂时禁用，待修复响应式对象访问问题）
  - 优先选择本科教育背景，无本科记录时选择最新教育记录
  - 容错处理和详细错误日志，确保功能稳定性
  - 自动展开学术背景表单区域，优化用户体验

#### 🛠️ 技术实现
- **后端优化**：扩展客户搜索API，支持OR条件的多字段模糊匹配查询
- **前端集成**：使用Vue 3组合式API，响应式状态管理，优雅的用户交互设计
- **数据同步**：使用Set数据结构快速检查专业状态，高效的状态管理
- **智能填充算法**：
  - 数据源正确识别：使用`education`字段而非`academic`字段获取教育背景
  - 智能GPA解析：正则匹配`x/y`格式，数值范围自动判断制式
  - 异常处理优化：try-catch包装，多层数据验证，详细错误日志
  - API响应处理：支持多种响应格式（`response.data`或直接`response`）

#### 🎯 用户价值
- **提升效率**：留学顾问可在进行选校匹配的同时，直接为客户管理定校书，无需切换页面
- **减少错误**：智能状态检测，避免重复添加专业
- **优化体验**：流畅的操作流程，清晰的状态反馈，提升工作体验

#### 🧪 测试验证
- ✅ 客户姓名搜索功能正常
- ✅ 电话号码搜索功能正常  
- ✅ 身份证号搜索功能正常
- ✅ 护照号搜索功能正常
- ✅ 系统哈希ID搜索功能正常
- ✅ 定校书添加/移除功能正常
- ✅ 状态同步功能正常
- ✅ 客户教育背景信息自动填充功能正常（学校、专业）
- 🔄 GPA格式智能识别功能（临时禁用，待修复）
- ✅ 错误处理和容错机制正常
- ✅ API数据格式验证通过
- ✅ 修复了`reactive()`对象访问错误（`profileForm.value.academic` → `profileForm.academic`）
- ⚠️ **临时方案**: 注释掉GPA填充功能，只填充学校和专业，避免访问错误

#### 🔧 故障排除
**常见问题**: 如果遇到 `Cannot read properties of undefined (reading 'academic')` 错误：
- **原因**: Vue 3中 `reactive()` 对象不需要使用 `.value` 访问，只有 `ref()` 创建的响应式引用才需要
- **解决**: 将 `profileForm.value.academic` 改为 `profileForm.academic`
- **检查**: 确认响应式对象的创建方式，`reactive()` vs `ref()`

**当前临时方案**: 
- **状态**: GPA填充功能已临时禁用（代码中标注TODO）
- **可用功能**: 学校名称和专业名称自动填充正常工作
- **下一步**: 需要进一步调试响应式对象访问问题，恢复GPA智能识别功能

## 📁 项目结构

```
TunshuEdu/
├── backend/                 # 后端代码 (Python FastAPI)
│   ├── alembic.ini          # Alembic 配置文件
│   ├── app/                 # FastAPI 应用核心目录
│   │   ├── ai_selection/      # AI选校模块
│   │   │   ├── api/
│   │   │   │   ├── endpoints/
│   │   │   │   ├── __init__.py
│   │   │   │   └── router.py
│   │   │   ├── core/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── candidate_pool.py
│   │   │   │   ├── program_matching.py
│   │   │   │   ├── ranking.py
│   │   │   │   ├── school_matching.py
│   │   │   │   └── user_profile.py
│   │   │   ├── db/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── init_db.py
│   │   │   │   ├── models.py
│   │   │   │   └── seed.py
│   │   │   ├── schemas/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── case.py
│   │   │   │   ├── program.py
│   │   │   │   ├── recommendation.py
│   │   │   │   └── user.py
│   │   │   ├── utils/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── llm.py
│   │   │   │   └── rag.py
│   │   │   ├── __init__.py
│   │   │   └── README.md
│   │   ├── api/             # API 路由 (按功能模块划分)
│   │   │   ├── __init__.py
│   │   │   ├── auth.py
│   │   │   ├── clients.py
│   │   │   └── dashboard.py
│   │   ├── core/            # 核心功能模块
│   │   │   ├── __init__.py
│   │   │   ├── config.py
│   │   │   ├── dependencies.py
│   │   │   └── security.py
│   │   ├── db/              # 数据库相关
│   │   │   ├── __init__.py
│   │   │   └── database.py
│   │   ├── models/          # SQLAlchemy 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── client.py
│   │   │   └── user.py
│   │   ├── schemas/         # Pydantic 模型/schemas
│   │   │   ├── __init__.py
│   │   │   ├── client.py
│   │   │   └── user.py
│   │   ├── utils/           # 后端工具函数
│   │   │   └── __init__.py
│   │   ├── __init__.py
│   │   └── main.py
│   ├── migrations/          # 数据库迁移脚本
│   │   ├── versions/
│   │   ├── README
│   │   ├── env.py
│   │   └── script.py.mako
│   ├── __init__.py
│   ├── create_tables.sql
│   ├── drop_tables.sql
│   ├── init_db.py
│   ├── main.py
│   └── requirements.txt
├── frontend/                # 前端代码 (Vue 3 + Vite)
│   ├── public/              # 静态资源目录
│   │   └── logo.png
│   ├── src/                 # 源代码目录
│   │   ├── api/
│   │   │   ├── account.js
│   │   │   ├── auth.js
│   │   │   ├── client.js
│   │   │   ├── dashboard.js
│   │   │   └── programs.js
│   │   ├── assets/
│   │   │   ├── logo.svg
│   │   │   └── vue.svg
│   │   ├── components/      # 可复用的 Vue 组件
│   │   │   ├── common/
│   │   │   │   ├── AnimatedInput.vue
│   │   │   │   └── Breadcrumb.vue
│   │   │   └── layout/
│   │   │       ├── Header.vue
│   │   │       ├── MainLayout.vue
│   │   │       └── Sidebar.vue
│   │   ├── router/          # 路由配置
│   │   │   ├── index.js
│   │   │   └── modules/
│   │   │       ├── account.js
│   │   │       ├── ai-tools.js
│   │   │       ├── auth.js
│   │   │       ├── clients.js
│   │   │       ├── dashboard.js
│   │   │       ├── error.js
│   │   │       ├── programs.js
│   │   │       ├── school.js
│   │   │       └── writing.js
│   │   ├── stores/          # 状态管理 (Pinia)
│   │   │   ├── auth.js
│   │   │   ├── clients.js
│   │   │   └── user.js
│   │   ├── styles/
│   │   │   └── index.css
│   │   ├── utils/           # 工具函数
│   │   │   ├── adapter.js
│   │   │   ├── auth.js
│   │   │   ├── format.js
│   │   │   └── request.js
│   │   ├── views/           # 页面级组件
│   │   │   ├── account/     # 账户管理页面
│   │   │   ├── ai-tools/    # AI工具页面
│   │   │   ├── auth/        # 认证相关页面
│   │   │   ├── clients/     # 客户管理页面
│   │   │   ├── dashboard/   # 仪表盘页面
│   │   │   ├── error/       # 错误页面
│   │   │   ├── planning/    # 申请规划页面
│   │   │   ├── school/      # 选校助手页面
│   │   │   └── writing/     # 文书写作页面
│   │   ├── App.vue          # Vue 应用根组件
│   │   ├── main.js          # 应用入口文件
│   │   └── style.css        # 全局样式文件
│   ├── auto-imports.d.ts    # 自动导入声明文件
│   ├── components.d.ts      # 组件类型声明文件
│   ├── index.html           # HTML 入口文件
│   ├── package.json         # Node.js 项目配置和依赖
│   ├── package-lock.json    # 锁定依赖版本
│   ├── postcss.config.cjs   # PostCSS 配置文件
│   ├── tailwind.config.js   # TailwindCSS 配置文件
│   └── vite.config.js       # Vite 配置文件
├── backend/                 # 后端代码 (Python FastAPI)
│   ├── app/                 # FastAPI 应用核心目录
│   │   ├── api/             # API 路由
│   │   │   ├── auth.py      # 认证相关 API 路由
│   │   │   ├── clients.py   # 客户相关 API 路由
│   │   │   ├── dashboard.py # 仪表盘相关 API 路由
│   │   │   └── __init__.py  # 初始化 API 路由
│   │   ├── core/            # 核心功能模块
│   │   │   ├── config.py    # 配置
│   │   │   ├── dependencies.py # 依赖注入
│   │   │   ├── security.py  # 安全相关功能
│   │   │   └── __init__.py  # 初始化核心模块
│   │   ├── db/              # 数据库相关
│   │   │   ├── database.py  # 数据库连接配置
│   │   │   └── __init__.py  # 初始化数据库模块
│   │   ├── models/          # SQLAlchemy 数据模型
│   │   │   ├── user.py      # 用户模型
│   │   │   └── __init__.py  # 初始化模型
│   │   ├── schemas/         # Pydantic 模型/schemas
│   │   │   ├── user.py      # 用户相关 schema
│   │   │   └── __init__.py  # 初始化 schemas
│   │   ├── utils/           # 后端工具函数
│   │   │   └── __init__.py  # 初始化工具模块
│   │   ├── main.py          # FastAPI 应用实例化
│   │   └── __init__.py      # 包初始化
│   ├── create_tables.sql    # 数据库表创建 SQL
│   ├── init_db.py           # 数据库初始化脚本
│   ├── main.py              # 应用启动入口脚本
│   └── requirements.txt     # Python 项目依赖
├── .cursor/                 # Cursor 编辑器配置
│   └── rules/               # Cursor 规则
├── .gitignore               # Git 忽略文件配置
├── .prettierrc              # Prettier 代码格式化配置
├── README.md                # 项目说明文档
├── start.sh                 # Linux/macOS 启动脚本 (启动前后端)
├── structure.md             # 项目结构详细说明
├── tsconfig.json            # TypeScript 配置文件
└── tsconfig.node.json       # Node.js TypeScript 配置
```

## 🔧 安装和运行

### 系统要求
- Node.js 16+
- Python 3.9+
- PostgreSQL 12+

### 快速开始

1. **克隆项目**
```bash
git clone <repository-url>
cd TunshuEdu
```

2. **数据库设置**
```bash
# 创建数据库
createdb -U postgres tunshuedu_db

# 导入数据表结构
psql -U postgres -d tunshuedu_db -f backend/create_tables.sql
```

3. **后端启动**
```bash
cd backend
python -m venv .venv
source .venv/bin/activate  # Linux/macOS
# 或 .venv\Scripts\activate  # Windows

pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

4. **前端启动**
```bash
cd frontend
npm install
npm run dev
```

5. **使用启动脚本（推荐）**
```bash
chmod +x start.sh
./start.sh
```

### 访问地址
- 前端应用: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

### 默认账户
- 用户名: `admin`
- 密码: `admin123`

## 🔧 配置说明

### 后端配置
编辑 `backend/app/core/config.py`:
```python
# 数据库配置
POSTGRES_USER = "postgres"
POSTGRES_PASSWORD = "your_password"
POSTGRES_HOST = "localhost"
POSTGRES_DB = "tunshuedu_db"

# JWT配置
SECRET_KEY = "your-secret-key"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
```

### 前端配置
创建 `frontend/.env.development`:
```env
VITE_API_URL=http://localhost:8000
```

## 🔥 重要特性

### AI选校系统
- **智能匹配**: 基于学生背景和历史案例的智能院校推荐
- **多维评分**: 院校层级、专业匹配、经历契合度等多维度评分
- **案例对比**: 与历史申请案例进行对比分析
- **实时筛选**: 支持多条件组合筛选

### 客户管理
- **全生命周期管理**: 从初次咨询到申请完成的完整流程
- **结构化信息**: 教育背景、工作经历、语言成绩等结构化存储
- **自定义模块**: 支持自定义背景模块和想法模块
- **档案归档**: 服务完成后的档案归档管理

### 文书系统
- **AI辅助写作**: 基于用户背景的个性化文书生成
- **智能优化**: 文书结构和内容的智能优化建议
- **模板管理**: 多种文书模板和格式支持
- **版本控制**: 文书修改历史和版本管理

### 学校Logo智能显示系统
- **数据库优先**: 优先使用数据库中存储的学校官方Logo URL
- **智能缓存**: 自动缓存已获取的Logo信息，提升页面加载速度
- **多级降级**: 数据库->官方网站favicon->默认Logo的多级降级策略
- **批量预加载**: 页面加载时批量预加载当前显示学校的Logo
- **错误处理**: 优雅处理Logo加载失败的情况

## 📊 API文档

项目提供完整的API文档，启动后端服务后访问:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

主要API端点：
- `/api/auth/*` - 用户认证相关
- `/api/clients/*` - 客户管理相关  
- `/api/ai-selection/*` - AI选校相关
- `/api/ai-selection/data/abroad-schools` - 境外院校数据（支持Logo URL获取）
- `/api/dashboard` - 仪表板数据

## 🧪 开发指南

### 数据库迁移
```bash
cd backend
# 创建迁移
alembic revision --autogenerate -m "描述"
# 应用迁移
alembic upgrade head
```

### 代码规范
- 后端遵循 FastAPI 最佳实践
- 前端使用 Vue 3 组合式API
- 统一使用 TypeScript 类型检查
- 遵循 RESTful API 设计原则

### 测试
```bash
# 后端测试
cd backend
pytest

# 前端测试
cd frontend
npm run test
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目维护: 囤鼠科技团队
- 邮箱: <EMAIL>
- 官网: https://tunshuedu.com

---

**TunshuEdu** - 让留学申请更智能，让教育服务更高效 🎓✨

## 最新更新 (2025-01-16)

### 🎯 客户档案智能填充功能

#### 选校匹配页面客户档案集成
实现了客户档案与学术背景信息的智能关联，大幅提升工作效率：

##### 核心功能
1. **客户搜索与选择**：
   - 支持多字段搜索（姓名、电话、邮箱、学生ID、护照等）
   - 实时搜索结果下拉框，使用 Teleport 确保不被遮挡
   - 客户头像和基本信息展示

2. **学术背景自动填充**：
   - 选择客户后自动获取其教育背景信息
   - 智能识别本科教育经历
   - 自动填充学校、专业、GPA等信息到表单
   - 智能解析GPA格式（百分制/4.0制/5.0制）

3. **定校书直接管理**：
   - 匹配结果可直接添加到客户的定校书中
   - 实时显示专业是否已在客户定校书中
   - 一键添加/移除功能

##### 技术实现亮点
- **数据关联**：前后端完整的客户教育信息API对接
- **智能解析**：自动识别GPA格式和制式
- **UI优化**：下拉框位置动态计算，响应式跟随
- **错误处理**：优雅的降级处理和用户提示

##### 工作流程优化
- **传统流程**：手动输入学生信息 → 获取匹配结果 → 手动记录推荐院校
- **新流程**：选择客户 → 自动填充信息 → 获取匹配结果 → 直接添加到定校书

### 🎨 智能推荐卡片UI规范应用

### 客户资料页面优化 (ClientProfile.vue)

#### 目标专业清单重构
将原有的2列网格布局改为横向一排一排的卡片形式，采用统一的智能推荐卡片设计规范：

##### 新卡片设计特点
1. **横向布局**：每个专业卡片占据一行，提供更好的信息展示空间
2. **统一设计语言**：应用与选校匹配页面相同的设计规范
3. **分层信息结构**：
   - 卡片头部：Logo、学校/专业名称、地区、排名徽章、操作按钮
   - 卡片主体：3列网格展示详细信息（专业名称、学校名称、地区、语言要求、申请截止、排名）
   - 卡片底部：官网链接和操作按钮区域

##### 设计系统应用
- **色彩系统**：使用品牌主色 #4F46E5，悬停色 #4338CA
- **排名徽章**：渐变背景、毛玻璃效果、悬停动画
- **交互动效**：卡片悬停上浮、按钮动画、过渡效果
- **间距规范**：统一的内外边距、网格间距

##### 用户体验改进
- **更好的可读性**：横向布局提供更多空间展示信息
- **一致的交互**：与选校匹配页面保持相同的操作习惯
- **视觉层次**：通过阴影、颜色、字重建立清晰的信息层次
- **响应式设计**：支持不同屏幕尺寸的自适应展示

#### 技术实现细节
- 使用 TailwindCSS 功能类实现样式
- 组件化的排名徽章设计
- 流畅的 CSS 过渡动画
- TypeScript 类型支持