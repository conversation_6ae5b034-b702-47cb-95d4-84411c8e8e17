"""Text Processor - 文本处理工具"""
import re
from typing import List, Dict, Any, Tuple
from app.ai_detection.schemas.detection import HighlightedSentence, RiskLevel
from app.ai_detection.config import AI_PERCENTAGE_THRESHOLD
import html

def clean_text(text: str) -> str:
    """
    清理和预处理文本。
    此版本经过优化，会保留换行符以维持文本的原始格式。
    
    Args:
        text: 原始文本
        
    Returns:
        清理后的文本
    """
    if not text:
        return ""
    
    text = text.strip()
    
    # 1. 将水平方向的多个空白符（如空格、制表符）替换为单个空格
    text = re.sub(r'[ \t\r\f\v]+', ' ', text)
    
    # 2. 将三个或更多的连续换行符压缩为两个，以保持段落间距的美观
    text = re.sub(r'\n{3,}', '\n\n', text)
    
    # 移除特殊的控制字符（保留换行符 \n）
    text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)
    
    # 标准化引号
    text = text.replace('"', '"').replace('"', '"')
    text = text.replace(''', "'").replace(''', "'")
    
    return text

def extract_highlighted_sentences(
    original_text: str, 
    highlighted_raw: List[str]
) -> List[HighlightedSentence]:
    """
    从原始高亮数据中提取句子信息，并计算在原文中的精确位置。
    这个版本修复了坐标不准和截断单词的问题。
    
    Args:
        original_text: 原始文本
        highlighted_raw: API返回的高亮句子列表
        
    Returns:
        处理后的高亮句子列表
    """
    if not highlighted_raw or not original_text:
        return []
    
    highlighted_sentences = []
    # 使用一个与原文等长的数组来标记已匹配的部分，避免重复匹配
    matched_mask = [False] * len(original_text)
    
    # 对API返回的高亮句子去重并按长度降序排序
    # 这样可以优先匹配更长的句子，避免长句中的短句被错误匹配
    unique_highlights = sorted(list(set(s.strip() for s in highlighted_raw if s and s.strip())), key=len, reverse=True)

    for sentence in unique_highlights:
        try:
            # 使用正则表达式查找所有可能的匹配项
            # `\s+`可以匹配不同的空白符组合
            pattern = re.escape(sentence).replace(r'\\ ', r'\\s+')
            for match in re.finditer(pattern, original_text):
                start, end = match.start(), match.end()
                
                # 检查这部分文本是否已被匹配
                if any(matched_mask[start:end]):
                    continue # 如果区域内任何字符已被匹配，则跳过

                highlighted_sentences.append(HighlightedSentence(
                    sentence=match.group(0),
                    start_index=start,
                    end_index=end
                ))
                
                # 标记该区域为已匹配
                for i in range(start, end):
                    matched_mask[i] = True
                
        except re.error as e:
            # logger.warning(f"为句子创建正则表达式时出错 '{sentence}': {e}") # Original code had this line commented out
            continue

    # 按句子的起始位置排序，保持其在原文中的顺序
    highlighted_sentences.sort(key=lambda s: s.start_index)
    
    return highlighted_sentences

def calculate_risk_level(ai_percentage: float) -> RiskLevel:
    """
    根据AI百分比计算风险等级
    
    Args:
        ai_percentage: AI生成内容的百分比
        
    Returns:
        风险等级
    """
    if ai_percentage < AI_PERCENTAGE_THRESHOLD["low"]:
        return RiskLevel.LOW
    elif ai_percentage < AI_PERCENTAGE_THRESHOLD["medium"]:
        return RiskLevel.MEDIUM
    elif ai_percentage < AI_PERCENTAGE_THRESHOLD["high"]:
        return RiskLevel.HIGH
    else:
        return RiskLevel.CRITICAL

def get_risk_level_description(risk_level: RiskLevel) -> str:
    """
    获取风险等级的中文描述
    
    Args:
        risk_level: 风险等级
        
    Returns:
        风险等级描述
    """
    descriptions = {
        RiskLevel.LOW: "低风险 - 文本大部分为人工创作",
        RiskLevel.MEDIUM: "中等风险 - 文本可能包含部分AI生成内容",
        RiskLevel.HIGH: "高风险 - 文本很可能大部分由AI生成",
        RiskLevel.CRITICAL: "极高风险 - 文本极可能完全由AI生成"
    }
    return descriptions.get(risk_level, "未知风险等级")

def validate_text_input(text: str) -> Tuple[bool, str]:
    """
    验证输入文本的有效性
    
    Args:
        text: 输入文本
        
    Returns:
        (是否有效, 错误信息)
    """
    if not text:
        return False, "文本不能为空"
    
    text = text.strip()
    if not text:
        return False, "文本不能仅包含空白字符"
    
    if len(text) < 10:
        return False, "文本长度过短，至少需要10个字符"
    
    # 检查是否主要由数字或特殊字符组成
    word_count = len(re.findall(r'\w+', text))
    if word_count < 3:
        return False, "文本应包含至少3个有意义的词汇"
    
    return True, ""

def split_text_into_sentences(text: str) -> List[str]:
    """
    将文本分割成句子
    
    Args:
        text: 输入文本
        
    Returns:
        句子列表
    """
    # 使用正则表达式分割句子
    sentence_pattern = r'[.!?]+\s+'
    sentences = re.split(sentence_pattern, text)
    
    # 清理和过滤句子
    cleaned_sentences = []
    for sentence in sentences:
        sentence = sentence.strip()
        if sentence and len(sentence) > 5:  # 过滤太短的句子
            cleaned_sentences.append(sentence)
    
    return cleaned_sentences

def calculate_text_statistics(text: str) -> Dict[str, Any]:
    """
    计算文本统计信息
    
    Args:
        text: 输入文本
        
    Returns:
        文本统计信息字典
    """
    if not text:
        return {
            "character_count": 0,
            "word_count": 0,
            "sentence_count": 0,
            "paragraph_count": 0,
            "average_words_per_sentence": 0.0
        }
    
    # 字符数（不含空格）
    character_count = len(re.sub(r'\s', '', text))
    
    # 词数
    words = re.findall(r'\w+', text)
    word_count = len(words)
    
    # 句子数
    sentences = split_text_into_sentences(text)
    sentence_count = len(sentences)
    
    # 段落数
    paragraphs = [p.strip() for p in text.split('\n') if p.strip()]
    paragraph_count = len(paragraphs)
    
    # 平均每句词数
    average_words_per_sentence = word_count / sentence_count if sentence_count > 0 else 0.0
    
    return {
        "character_count": character_count,
        "word_count": word_count,
        "sentence_count": sentence_count,
        "paragraph_count": paragraph_count,
        "average_words_per_sentence": round(average_words_per_sentence, 2)
    }

def create_text_preview(text: str, max_length: int = 100) -> str:
    """
    创建文本预览（截取前面的部分内容）
    
    Args:
        text: 原始文本
        max_length: 最大预览长度
        
    Returns:
        文本预览
    """
    if not text:
        return ""
    
    text = text.strip()
    if len(text) <= max_length:
        return text
    
    # 尝试在单词边界处截断
    truncated = text[:max_length]
    last_space = truncated.rfind(' ')
    
    if last_space > max_length * 0.7:  # 如果最后一个空格不会截断太多内容
        truncated = truncated[:last_space]
    
    return truncated + "..." 

def create_highlighted_html(original_text: str, highlighted_sentences: List[HighlightedSentence]) -> str:
    """
    根据高亮句子的坐标，在原文中插入<mark>标签.
    
    Args:
        original_text: 原始文本.
        highlighted_sentences: 包含精确坐标的高亮句子列表.
        
    Returns:
        带有高亮标签的HTML字符串.
    """
    if not highlighted_sentences:
        return html.escape(original_text).replace('\n', '<br>')

    parts = []
    last_index = 0
    
    # 确保按起始位置排序
    highlighted_sentences.sort(key=lambda s: s.start_index)
    
    for hs in highlighted_sentences:
        start, end = hs.start_index, hs.end_index
        # 添加高亮区域前的普通文本 (已进行HTML转义)
        if start > last_index:
            parts.append(html.escape(original_text[last_index:start]))
        
        # 添加高亮文本
        # 从原始文本中截取，确保内容准确，然后进行HTML转义
        original_highlight = original_text[start:end]
        parts.append(f'<mark class="ai-highlight">{html.escape(original_highlight)}</mark>')
        last_index = end
        
    # 添加最后一个高亮区域之后的剩余文本
    if last_index < len(original_text):
        parts.append(html.escape(original_text[last_index:]))
        
    return "".join(parts).replace('\n', '<br>') 