from fastapi import APIRouter, Depends, status, HTTPException
from fastapi.responses import StreamingResponse
from typing import List, Dict, Any, AsyncGenerator

from app.core.dependencies import DBSession
from app.ai_writing.cv.core.generator import (
    generate_cv_stream, 
    get_client_profiles_summary, 
    get_client_module_data,
    save_cv_content
)
from app.ai_writing.schemas.cv import (
    CVGenerationRequest, 
    ClientProfileSummary,
    ClientModuleData,
    CVSaveRequest,
    CVSaveResponse
)

router = APIRouter()

@router.post(
    "/generate",
    response_class=StreamingResponse,
    status_code=status.HTTP_200_OK,
    summary="流式生成CV/简历",
    description="根据客户档案信息和用户选择的模块流式生成专业的CV/简历"
)
async def generate_cv_stream_endpoint(
    request: CVGenerationRequest,
    db: DBSession
) -> StreamingResponse:
    """
    流式生成CV/简历
    
    以流式响应返回生成的CV内容。
    """
    cv_generator = generate_cv_stream(db, request)
    return StreamingResponse(cv_generator, media_type="text/markdown")

@router.post(
    "/save",
    response_model=CVSaveResponse,
    status_code=status.HTTP_200_OK,
    summary="保存CV内容",
    description="将CV的Markdown内容保存到数据库"
)
async def save_cv_endpoint(
    request: CVSaveRequest,
    db: DBSession
) -> CVSaveResponse:
    """
    保存CV内容
    
    将用户编辑后的CV Markdown内容保存到数据库中。
    """
    result = await save_cv_content(db, request)
    return result

@router.get(
    "/clients",
    response_model=List[ClientProfileSummary],
    status_code=status.HTTP_200_OK,
    summary="获取客户档案列表",
    description="获取所有客户的档案摘要信息，用于前端选择"
)
async def get_client_profiles(db: DBSession) -> List[ClientProfileSummary]:
    """
    获取客户档案列表
    
    返回所有未归档客户的基本信息和各模块数据数量，
    用于前端显示客户选择列表。
    """
    profiles = await get_client_profiles_summary(db)
    return [ClientProfileSummary(**profile) for profile in profiles]

@router.get(
    "/clients/{client_id}/modules",
    response_model=ClientModuleData,
    status_code=status.HTTP_200_OK,
    summary="获取客户详细模块数据",
    description="获取指定客户的所有模块详细数据，用于前端显示和选择"
)
async def get_client_modules(
    client_id: str,
    db: DBSession
) -> ClientModuleData:
    """
    获取客户详细模块数据
    
    根据客户ID获取该客户的所有教育经历、工作经历、学术经历等详细信息，
    用于前端显示和用户选择特定的经历条目。
    """
    module_data = await get_client_module_data(db, client_id)
    return ClientModuleData(**module_data) 