"""
LLM (Large Language Model) 功能实现
专门用于AI文书生成
"""

from typing import Dict, Any, AsyncGenerator
import httpx
import asyncio
import logging
import re
import json

import json_repair

from app.ai_writing.config import ALIBABACLOUD_API_KEY_ai_writing
from app.ai_writing.schemas.rl import RecommenderProfile

logger = logging.getLogger(__name__)

# 创建并发限制信号量，避免API过载
API_SEMAPHORE = asyncio.Semaphore(5)  # 限制为5个并发请求
HTTP_CLIENT = httpx.AsyncClient(timeout=httpx.Timeout(5.0, read=180.0))

async def _call_llm_stream_async(
    prompt: str,
    system_prompt: str,
    model: str = "qwen-turbo-latest",
    temperature: float = 0.3
) -> AsyncGenerator[str, None]:
    """
    使用指定的提示词异步流式调用LLM API。
    这是一个内部通用函数，封装了API请求、错误处理和流式响应解析的逻辑。
    输出Markdown格式的内容。
    """
    async with API_SEMAPHORE:
        url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        payload = {
            "model": model,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            "stream": True,  # 启用流式输出
            "max_tokens": 6000,
            "temperature": temperature,
            "top_p": 0.6,
            "stop": None,
            "top_k": 30,
            "enable_thinking": False,
            "n": 1,
        }
        headers = {
            "Authorization": f"Bearer {ALIBABACLOUD_API_KEY_ai_writing}",
            "Content-Type": "application/json"
        }

        try:
            async with HTTP_CLIENT.stream("POST", url, json=payload, headers=headers) as response:
                response.raise_for_status()
                
                finish_reason = None
                async for line in response.aiter_lines():
                    if not line.startswith('data:'):
                        continue
                    
                    line_data = line[len('data: '):].strip()
                    if line_data == '[DONE]':
                        break
                        
                    try:
                        chunk = json.loads(line_data)
                        delta = chunk.get("choices", [{}])[0].get("delta", {})
                        content = delta.get("content")
                        if content:
                            yield content
                        
                        if chunk.get("choices", [{}])[0].get("finish_reason"):
                            finish_reason = chunk["choices"][0]["finish_reason"]

                    except json.JSONDecodeError:
                        logger.warning(f"无法解析流式响应中的JSON: {line_data}")
                        continue
                
                if finish_reason == "length":
                    logger.warning("内容生成被截断，因为达到了模型的最大token限制。")
                    warning_message = """
<div style='border: 2px solid red; color: red; background-color: #ffebee; padding: 15px; margin: 20px; text-align: center; font-family: sans-serif;'>
    <strong>警告：内容可能不完整</strong>
    <p>AI生成的内容因为达到最大长度而被截断。请尝试减少选择的经历数量或简化要求。</p>
</div>
"""
                    yield warning_message

        except httpx.TimeoutException:
            logger.error("API调用超时")
            yield "生成超时，请稍后重试。"
        except httpx.RequestError as e:
            logger.error(f"API请求失败: {e.request.url}, 错误: {e}")
            yield "生成失败，请稍后重试。"
        except Exception as e:
            logger.error(f"API调用时发生未知异常: {e}", exc_info=True)
            yield "生成失败，请稍后重试。"

async def generate_cv_content_stream_async(
    client_data: Dict[str, Any], 
    language: str = "english", 
    additional_info: str = ""
) -> AsyncGenerator[str, None]:
    """
    异步调用大模型API流式生成CV内容
    """
    prompt = _build_cv_prompt(client_data, language, additional_info)
    system_prompt = _build_cv_system_prompt(language)
    
    async for chunk in _call_llm_stream_async(
        prompt=prompt,
        system_prompt=system_prompt,
        temperature=0.2
    ):
        yield chunk

async def generate_ps_content_stream_async(
    client_data: Dict[str, Any], 
    request: Any
) -> AsyncGenerator[str, None]:
    """
    异步调用大模型API流式生成PS内容
    """
    prompt = _build_ps_prompt(client_data, request)
    system_prompt = "你是一位专业的留学文书写作专家，擅长根据学生背景和申请目标撰写高质量的个人陈述(Personal Statement)。请以Markdown格式输出文书内容。"

    async for chunk in _call_llm_stream_async(
        prompt=prompt,
        system_prompt=system_prompt,
        temperature=0.4
    ):
        yield chunk

async def parse_text_to_rl_profile_async(text: str) -> RecommenderProfile:
    """
    使用LLM将纯文本解析为结构化的RecommenderProfile对象。

    Args:
        text: 从文档中提取的纯文本内容。

    Returns:
        一个填充了数据的RecommenderProfile Pydantic模型实例。
    """
    from app.ai_writing.schemas.rl import RecommenderProfile

    system_prompt = """你是一个精通信息抽取的AI助手。你的任务是从用户提供的文本中，根据指定的JSON格式，准确地抽取出推荐人相关的信息。如果某些字段在文本中找不到对应信息，请将其保留为null，不要编造。"""
    
    # 构造一个带有pydantic模型json结构的few-shot prompt
    pydantic_json_structure = RecommenderProfile.model_json_schema()
    
    prompt = f"""
请从以下文本中抽取出推荐人的详细信息，并严格按照指定的JSON格式返回结果。

**待解析文本:**
---
{text}
---

**请将抽取的信息填充到以下JSON结构中:**
```json
{pydantic_json_structure}
```

请只返回符合上述JSON结构的纯文本，不要包含任何代码块标记(```json)或额外的解释。
"""
    
    # 使用非流式调用，因为我们需要完整的JSON对象
    async with API_SEMAPHORE:
        url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        payload = {
            "model": "qwen-turbo-latest",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            "stream": False,
            "max_tokens": 2000,
            "temperature": 0.2, # 对于信息抽取，温度设为0以确保确定性
            "top_p": 0.8,
            "stop": None,
            "top_k": 30,
            "n": 1,
            "enable_thinking": False
        }
        headers = {
            "Authorization": f"Bearer {ALIBABACLOUD_API_KEY_ai_writing}",
            "Content-Type": "application/json"
        }

        try:
            response = await HTTP_CLIENT.post(url, json=payload, headers=headers)
            response.raise_for_status()
            
            response_data = response.json()
            json_content_str = response_data["choices"][0]["message"]["content"]

            # 解析JSON字符串为Pydantic模型
            parsed_data = json_repair.loads(json_content_str)
            return RecommenderProfile(**parsed_data)

        except httpx.RequestError as e:
            logger.error(f"解析RL文本时API请求失败: {e}", exc_info=True)
            raise
        except (json.JSONDecodeError, TypeError) as e:
            logger.error(f"解析LLM返回的RL JSON时失败: {e}", exc_info=True)
            raise
        except Exception as e:
            logger.error(f"解析RL文本时发生未知异常: {e}", exc_info=True)
            raise

async def generate_rl_content_stream_async(
    client_data: Dict[str, Any], 
    recommender_profile: Dict[str, Any],
    word_limit: int,
    additional_info: str
) -> AsyncGenerator[str, None]:
    """
    异步调用大模型API流式生成RL内容
    """
    prompt = _build_rl_prompt(client_data, recommender_profile, word_limit, additional_info)
    system_prompt = _build_rl_system_prompt(recommender_profile)
    
    async for chunk in _call_llm_stream_async(
        prompt=prompt,
        system_prompt=system_prompt,
        temperature=0.4
    ):
        yield chunk

def _build_cv_system_prompt(language: str) -> str:
    """
    构建CV生成的系统提示词
    """
    if language == "english":
        return """你是一位精通国际标准写作规范的英文简历撰写专家，具备跨学科理解能力，能够根据结构化字段准确生成风格专业、表达规范、格式统一的英文简历内容。请以Markdown格式输出简历内容，只需要简历内容，不需要其他的回答内容。"""
    else:
        return """你是一位专业的中文简历写作专家，擅长根据客户信息生成高质量的中文简历。请以Markdown格式输出简历内容，严格按照提供的格式要求生成专业的简历内容。"""

def _build_cv_prompt(
    client_data: Dict[str, Any], 
    language: str, 
    additional_info: str
) -> str:
    """
    构建CV生成的提示词
    """
    if language == "english":
        return _build_english_cv_prompt(client_data, additional_info)
    else:
        return _build_chinese_cv_prompt(client_data, additional_info)

def _build_english_cv_prompt(client_data: Dict[str, Any], additional_info: str) -> str:
    """构建英文CV的提示词"""
    
    # 基本信息部分
    name = client_data.get('name', '[Name]')
    prompt = f"""请将{name}的中文姓名转换为英文简历中常用的拼音格式，要求如下：
1. 姓氏在前，并全部大写
2. 名字在后，并保留首字母大写，其余小写
示例输入：张二三
示例输出：ZHANG Ersan

请按照以下格式生成个人信息：

# **{name}**

**Phone: {client_data.get('phone', '[Phone]')} | Email: {client_data.get('email', '[Email]')}**

"""

    # 教育背景部分
    prompt += "### **Education**\n---\n"
    
    if client_data.get('education'):
        for edu in client_data['education']:
            prompt += f"""请将以下教育信息转换为英文简历格式：
学校: {edu.get('school', '[School]')}
专业: {edu.get('major', '[Major]')}
学位: {edu.get('degree', '[Degree]')}
GPA: {edu.get('gpa', '[GPA]')}
起止时间: {edu.get('start_date', '[Start]')} - {edu.get('end_date', '[End]')}

请按照以下格式输出：
**[英文学校名] | [起止时间]**
Major: [英文专业名] (GPA: [GPA])

时间格式示例：Sep 2021 - Jun 2026

"""
    else:
        prompt += "[教育信息待补充]\n\n"

    prompt += "\n\n"

    # 学术项目部分
    if client_data.get('academic'):
        prompt += "### **Academic Projects**\n---\n"
        
        for academic in client_data['academic']:
            prompt += f"""请将以下学术项目转换为英文简历格式：
项目标题: {academic.get('title', '[Title]')}
项目类型: {academic.get('type', '[Type]')}
项目描述: {academic.get('description', '[Description]')}
项目时间: {academic.get('date', '[Date]')}

请按照以下格式输出：
**[英文项目标题]** 
*[角色/类型]*
* **Introduction:** [项目背景和目标的简洁描述]
* **[方法/技术1]:** [具体实现描述，使用专业术语]
* **[方法/技术2]:** [具体实现描述，使用专业术语]
* **[方法/技术3]:** [具体实现描述，使用专业术语]
* **Outcome:** [项目成果或状态]

注意：
1. 每个技术要点都要加粗标题
2. 使用学术性动词如 deployed, implemented, optimized 等
3. 包含具体的技术栈、工具、库等
4. 贡献程度用斜体标注，如 *Main Contribution*, *Participation* 等
5. 根据项目类型灵活调整格式：
   - 科研项目: 突出研究方法和学术贡献
   - 课程项目: 突出学习成果和技术应用
   - 比赛项目: 突出竞赛成果和团队协作

"""
        prompt += "\n\n"

    # 工作经历部分
    if client_data.get('work'):
        prompt += "### **Internships**\n---\n"
        
        for work in client_data['work']:
            prompt += f"""请将以下工作经历转换为英文简历格式：
职位: {work.get('position', '[Position]')}
公司: {work.get('company', '[Company]')}
工作类型: {work.get('employment_type', '[Employment Type]')}
起止时间: {work.get('start_date', '[Start Date]')} - {work.get('end_date', '[End Date]')}
工作描述: {work.get('description', '[Description]')}

请按照以下格式输出：
**[英文职位] ([职位类型]) | [起止时间]**
**[英文公司名]**
* [工作职责1的简洁描述，突出技术和成果，使用动词开头]
* [工作职责2的简洁描述，突出技术和成果，使用动词开头]
* [工作职责3的简洁描述，突出技术和成果，使用动词开头]

时间格式示例：Jul 4th, 2024 - Aug 15th, 2024
职位类型翻译：实习生 → Intern，全职 → Full-time等

"""
        prompt += "\n\n"

    # 课外活动部分
    if client_data.get('activities'):
        prompt += "### **Activities**\n---\n"
        for activity in client_data['activities']:
            prompt += f"""请将以下活动经历转换为英文简历格式：
活动名称: {activity.get('name', '')}
担任角色: {activity.get('role', '')}
起止时间: {activity.get('start_date', '')} - {activity.get('end_date', '')}
活动描述: {activity.get('description', '')}

请按照以下格式输出：
* [英文时间]: [英文角色] | **[英文活动名称]**

"""
        prompt += "\n\n"

    # 荣誉奖项部分
    if client_data.get('awards'):
        prompt += "### **Honors**\n---\n"
        for award in client_data['awards']:
            prompt += f"""请将以下奖项转换为英文简历格式：
奖项名称: {award.get('name', '')}
奖项级别: {award.get('level', '')}
获奖时间: {award.get('date', '')}
奖项描述: {award.get('description', '')}

请按照以下格式输出：
* **[英文奖项名称] | [获奖时间]**

"""
        prompt += "\n\n"

    # 技能部分
    if client_data.get('skills') or client_data.get('language_scores'):
        prompt += "### **Skills**\n---\n"
        
        if client_data.get('language_scores'):
            prompt += "* **Language Proficiency:** "
            lang_scores = []
            for lang in client_data['language_scores']:
                lang_type = lang.get('type', '').upper()
                lang_score = lang.get('score', '')
                if lang_type and lang_score:
                    if 'TBD' in lang_score.upper() or 'TBC' in lang_score.upper():
                        lang_scores.append(f"{lang_type} (TBD)")
                    else:
                        lang_scores.append(f"{lang_type}: {lang_score}")
            prompt += ", ".join(lang_scores) + "\n"
        
        if client_data.get('skills'):
            for skill in client_data['skills']:
                skill_type = skill.get('type', '')
                skill_desc = skill.get('description', '')
                if skill_type and skill_desc:
                    prompt += f"* **{skill_type}:** {skill_desc}\n"

    if additional_info:
        prompt += f"\n### **Additional Information**\n---\n{additional_info}\n"

    prompt += """

请生成专业的英文简历，严格按照上述Markdown格式要求：
1. 使用# **姓名**作为主标题
2. 使用### **章节名**作为各部分标题
3. 使用---作为分隔线
4. 重要内容使用**粗体**标记
5. 角色/类型信息使用*斜体*标记
6. 所有bullet points使用*开头
7. 保持清晰的层次结构和专业的视觉效果

重要提示：请只返回Markdown格式的简历内容，不要包含任何代码块标记（```）、额外的解释文字或其他格式。直接输出可以被Markdown解析器渲染的纯文本内容。
"""
    
    return prompt

def _build_chinese_cv_prompt(client_data: Dict[str, Any], additional_info: str) -> str:
    """构建中文CV的提示词"""
    
    # 基本信息部分
    name = client_data.get('name', '[姓名]')
    prompt = f"""请按照以下格式生成个人信息：

# **{name}**

**电话：{client_data.get('phone', '[电话]')} 邮箱：{client_data.get('email', '[邮箱]')}**

---

"""

    # 教育背景部分
    prompt += "### **教育背景**\n\n"
    
    if client_data.get('education'):
        for edu in client_data['education']:
            prompt += f"""请将以下教育信息转换为中文简历格式：
学校: {edu.get('school', '[学校]')}
专业: {edu.get('major', '[专业]')}
学位: {edu.get('degree', '[学位]')}
GPA: {edu.get('gpa', '[GPA]')}
起止时间: {edu.get('start_date', '[开始时间]')} - {edu.get('end_date', '[结束时间]')}

请按照以下格式输出：
{edu.get('school', '[学校]')} | {edu.get('start_date', '[开始时间]')} - {edu.get('end_date', '[结束时间]')}
专业：{edu.get('major', '[专业]')} | (GPA: {edu.get('gpa', '[GPA]')})

"""
    else:
        prompt += "[教育信息待补充]\n\n"

    prompt += "---\n\n"

    # 学术项目部分
    if client_data.get('academic'):
        prompt += "### **学术项目**\n\n"
        
        for academic in client_data['academic']:
            prompt += f"""请将以下学术项目转换为中文简历格式：
项目标题: {academic.get('title', '[项目标题]')}
项目类型: {academic.get('type', '[项目类型]')}
项目描述: {academic.get('description', '[项目描述]')}
项目时间: {academic.get('date', '[项目时间]')}

请按照以下格式输出：
**{academic.get('title', '[项目标题]')}** | *{academic.get('type', '[项目类型]')}*

* **项目背景：** [根据描述提取项目背景和目标的简洁描述]
* **[技术方法1]：** [具体实现描述，使用专业术语] | *[贡献程度]*
* **[技术方法2]：** [具体实现描述，使用专业术语] | *[贡献程度]*
* **[技术方法3]：** [具体实现描述，使用专业术语] | *[贡献程度]*
* **项目成果：** [项目成果或当前状态]

注意：
1. 每个技术要点都要加粗标题
2. 使用专业术语描述技术实现
3. 包含具体的技术栈、工具、库等
4. 贡献程度用斜体标注，如 *主要贡献*, *参与* 等
5. 根据项目类型灵活调整格式：
   - 科研项目: 突出研究方法和学术贡献
   - 课程项目: 突出学习成果和技术应用
   - 比赛项目: 突出竞赛成果和团队协作

"""
        prompt += "---\n\n"

    # 工作经历部分
    if client_data.get('work'):
        prompt += "### **工作经历**\n\n"
        
        for work in client_data['work']:
            prompt += f"""请将以下工作经历转换为中文简历格式：
职位: {work.get('position', '[职位]')}
公司: {work.get('company', '[公司]')}
工作类型: {work.get('employment_type', '[工作类型]')}
起止时间: {work.get('start_date', '[开始时间]')} - {work.get('end_date', '[结束时间]')}
工作描述: {work.get('description', '[工作描述]')}

请按照以下格式输出：
{work.get('position', '[职位]')} ({work.get('employment_type', '[工作类型]')}) | {work.get('start_date', '[开始时间]')} - {work.get('end_date', '[结束时间]')}
{work.get('company', '[公司]')}
* **[工作职责1的简洁描述，突出技术和成果]**
* **[工作职责2的简洁描述，突出技术和成果]**
* **[工作职责3的简洁描述，突出技术和成果]**

每个职责要点都要加粗并突出具体贡献

"""
        prompt += "---\n\n"

    # 课外活动部分
    if client_data.get('activities'):
        prompt += "### **课外活动**\n\n"
        for activity in client_data['activities']:
            prompt += f"""请将以下活动经历转换为中文简历格式：
活动名称: {activity.get('name', '')}
担任角色: {activity.get('role', '')}
起止时间: {activity.get('start_date', '')} - {activity.get('end_date', '')}
活动描述: {activity.get('description', '')}

请按照以下格式输出：
* {activity.get('start_date', '')} - {activity.get('end_date', '')}：{activity.get('role', '')} | **{activity.get('name', '')}**

"""
        prompt += "---\n\n"

    # 荣誉奖项部分
    if client_data.get('awards'):
        prompt += "### **荣誉奖项**\n\n"
        for award in client_data['awards']:
            prompt += f"""请将以下奖项转换为中文简历格式：
奖项名称: {award.get('name', '')}
奖项级别: {award.get('level', '')}
获奖时间: {award.get('date', '')}
奖项描述: {award.get('description', '')}

请按照以下格式输出：
* **{award.get('name', '')}**

"""
        prompt += "---\n\n"

    # 技能部分
    if client_data.get('skills') or client_data.get('language_scores'):
        prompt += "### **技能专长**\n\n"
        
        if client_data.get('language_scores'):
            prompt += "* **语言能力：** "
            lang_scores = []
            for lang in client_data['language_scores']:
                lang_type = lang.get('type', '').upper()
                lang_score = lang.get('score', '')
                if lang_type and lang_score:
                    if 'TBD' in lang_score.upper() or 'TBC' in lang_score.upper() or '待定' in lang_score:
                        lang_scores.append(f"{lang_type} (待定)")
                    else:
                        lang_scores.append(f"{lang_type}: {lang_score}")
            prompt += "，".join(lang_scores) + "\n"
        
        if client_data.get('skills'):
            for skill in client_data['skills']:
                skill_type = skill.get('type', '')
                skill_desc = skill.get('description', '')
                if skill_type and skill_desc:
                    prompt += f"* **{skill_type}：** {skill_desc}\n"

    if additional_info:
        prompt += f"\n### **补充信息**\n{additional_info}\n"

    prompt += """

请生成专业的中文简历，严格按照上述Markdown格式要求：
1. 使用# **姓名**作为主标题
2. 使用### **章节名**作为各部分标题
3. 使用---作为分隔线
4. 重要内容使用**粗体**标记
5. 角色/类型信息使用*斜体*标记
6. 右对齐时间请使用合适的空格调整
7. 所有bullet points使用*开头
8. 保持清晰的层次结构和专业的视觉效果
9. 使用中文标点符号和格式习惯

重要提示：请只返回Markdown格式的简历内容，不要包含任何代码块标记（```）、额外的解释文字或其他格式。直接输出可以被Markdown解析器渲染的纯文本内容。
"""
    
    return prompt 

def _build_rl_system_prompt(recommender_profile: Dict[str, Any]) -> str:
    """构建RL生成的系统提示词"""
    recommender_name = recommender_profile.get('first_name_en', '') + " " + recommender_profile.get('last_name_en', '')
    recommender_title = recommender_profile.get('title', 'a professional')
    
    return f"""你是一位经验丰富的推荐信撰写专家，现在将扮演 {recommender_name} ({recommender_title}) 的角色。你的任务是基于提供的学生信息和推荐人信息，撰写一封语气真实、内容具体、有说服力的英文推荐信。**重要提示:** 请只返回Markdown格式的推荐信内容，不要包含任何代码块标记（```）、额外的解释文字或其他格式。直接输出可以被Markdown解析器渲染的纯文本内容。确保内容流畅、专业。"""

def _build_rl_prompt(client_data: Dict[str, Any], recommender_profile: Dict[str, Any], word_limit: int, additional_info: str) -> str:
    """构建RL生成的提示词"""
    
    # 学生信息
    student_name = client_data.get('name', '[Student Name]')
    student_gender = client_data.get('gender', '[Student Gender]') 
    prompt = f"### Student to Recommend\n- Name: {student_name}\n- Gender: {student_gender}\n\n"

    # 推荐人信息
    recommender_name = recommender_profile.get('first_name_en', '') + " " + recommender_profile.get('last_name_en', '')
    prompt += f"### Recommender Information\n- Name: {recommender_name}\n"
    prompt += f"- Title: {recommender_profile.get('title', '')}\n"
    prompt += f"- Affiliation: {recommender_profile.get('affiliation', '')}\n"
    prompt += f"- Background: {recommender_profile.get('background_summary', 'N/A')}\n"

    # 关系与具体事例
    prompt += "\n**Relationship with Student & Specific Examples for the Letter**:\n"
    if recommender_profile.get('course_info'):
        course = recommender_profile['course_info']
        prompt += f"- Taught the student in the course '{course.get('course_name_en', 'N/A')}' during {course.get('term', 'N/A')}.\n"
        prompt += f"- The student's performance: Final grade was {course.get('final_grade', 'N/A')}. Note on outstanding performance: {course.get('outstanding_performance', 'N/A')}\n"
    
    if recommender_profile.get('work_info'):
        work = recommender_profile['work_info']
        prompt += f"- Supervised the student during '{work.get('collaboration_content', 'N/A')}' from {work.get('collaboration_date', 'N/A')}.\n"
        prompt += f"- The student's duties and performance: {work.get('student_duties', 'N/A')}\n"
        prompt += f"- Notable strengths observed: {work.get('praised_ability', 'N/A')}\n"
    
    if additional_info:
        prompt += f"\n### Additional Information from User\n{additional_info}\n"
        
    prompt += "\n---\n"
    
    # 生成指令
    word_limit_map = {0: "No limit", 1: "around 500 words", 2: "around 1000 words", 3: "around 1500 words", 4: "around 2000 words"}
    word_limit_text = word_limit_map.get(word_limit, "No limit")

    prompt += f"""
### Task
Based on all the information provided above, please write a compelling and professional recommendation letter in English.

**Instructions:**
1.  **Introduction:** Start by introducing yourself (the recommender) and your relationship with the student.
2.  **Body Paragraphs:**
    -   Dedicate paragraphs to specific qualities of the student (e.g., academic ability, research potential, teamwork, leadership).
    -   Use the **concrete examples** from the "Relationship with Student & Specific Examples" section to support your claims. For instance, if the student did well in a course, mention their outstanding performance. If they excelled in a project, describe their contribution and praised abilities.
    -   Quantify achievements where possible (e.g., "ranked in the top 5% of the class based on my observation").
3.  **Conclusion:** Summarize the student's key strengths and give your unequivocal recommendation for the student.
4.  **Tone:** Maintain a professional, positive, and authentic tone. The language should reflect your role (e.g., a professor's letter would be more formal and academic). **Use appropriate pronouns (he/she) based on the student's gender.**
5.  **Format:** Output as a clean Markdown file.
6.  **Word Count:** Please adhere to a word count of **{word_limit_text}**.

**IMPORTANT:** Do not just list the facts. Weave them into a coherent narrative that paints a vivid picture of the student's capabilities and potential. Directly output the Markdown content of the letter without any extra explanations or code blocks.
"""
    return prompt

def _build_ps_prompt(client_data: Dict[str, Any], request: Any) -> str:
    """
    构建PS生成的提示词
    """
    # 获取配置参数
    word_limit_map = {0: "不限制", 1: "500词", 2: "1000词", 3: "1500词", 4: "2000词"}
    paragraph_map = {0: "不限制", 1: "智能分段", 2: "4段", 3: "5段", 4: "6段", 5: "7段", 6: "8段"}
    
    word_limit_text = word_limit_map.get(request.word_limit, "不限制")
    paragraph_text = paragraph_map.get(request.paragraph_setting, "不限制")
    
    # 构建基本信息
    prompt = f"""
请为以下学生撰写一份专业的个人陈述(Personal Statement)，内容应为HTML格式。

## 学生基本信息
姓名: {client_data.get('name', '[姓名]')}
邮箱: {client_data.get('email', '[邮箱]')}
电话: {client_data.get('phone', '[电话]')}
所在地: {client_data.get('location', '[所在地]')}

## 申请目标
申请院校: {request.target_school}
申请学位: {request.degree}
申请专业: {request.major}

## 学生自身经历描述
{request.personal_experience}
"""

    # 添加院校要求
    if request.school_requirements:
        prompt += f"\n## 院校要求及其他说明\n{request.school_requirements}\n"

    # 添加教育背景
    if client_data.get('education'):
        prompt += "\n## 教育背景\n"
        for edu in client_data['education']:
            prompt += f"""
- {edu.get('school', '')} | {edu.get('major', '')} | {edu.get('degree', '')}
  GPA: {edu.get('gpa', '未提供')} | {edu.get('start_date', '')} - {edu.get('end_date', '')}
  {edu.get('description', '')}
"""

    # 添加学术经历
    if client_data.get('academic'):
        prompt += "\n## 学术经历\n"
        for academic in client_data['academic']:
            prompt += f"""
- {academic.get('title', '')} ({academic.get('type', '')})
  时间: {academic.get('date', '')}
  {academic.get('description', '')}
"""

    # 添加工作经历
    if client_data.get('work'):
        prompt += "\n## 工作/实习经历\n"
        for work in client_data['work']:
            prompt += f"""
- {work.get('company', '')} | {work.get('position', '')}
  {work.get('start_date', '')} - {work.get('end_date', '')}
  {work.get('description', '')}
"""

    # 添加课外活动
    if client_data.get('activities'):
        prompt += "\n## 课外活动\n"
        for activity in client_data['activities']:
            prompt += f"""
- {activity.get('name', '')} | {activity.get('role', '')}
  {activity.get('start_date', '')} - {activity.get('end_date', '')}
  {activity.get('description', '')}
"""

    # 添加奖项荣誉
    if client_data.get('awards'):
        prompt += "\n## 荣誉奖项\n"
        for award in client_data['awards']:
            prompt += f"""
- {award.get('name', '')} ({award.get('level', '')})
  时间: {award.get('date', '')}
  {award.get('description', '')}
"""

    # 添加个人想法
    if client_data.get('thoughts'):
        prompt += "\n## 个人想法与规划\n"
        for thought in client_data['thoughts']:
            if thought.get('target_major'):
                prompt += f"专业申请动机: {thought.get('target_major')}\n"
            if thought.get('personal_understanding'):
                prompt += f"专业个人解读: {thought.get('personal_understanding')}\n"
            if thought.get('future_plan'):
                prompt += f"未来规划: {thought.get('future_plan')}\n"

    # 添加生成要求
    prompt += f"""

## 生成要求
目标字数: {word_limit_text}
段落设置: {paragraph_text}

请根据以上信息生成一份专业的个人陈述，要求：
1. 内容要真实、具体、有说服力
2. 逻辑清晰，层次分明
3. 突出申请者的优势和特色
4. 体现对目标专业的理解和热情
5. 展现明确的职业规划
6. 使用标准Markdown格式
7. 清晰的段落结构和标题层次
8. 适当使用**粗体**和*斜体*强调重点
9. 合理的段落分隔

重要提示：请只返回Markdown格式的个人陈述内容，不要包含任何代码块标记（```）、额外的解释文字或其他格式。直接输出可以被Markdown解析器渲染的纯文本内容。
"""
    
    return prompt