from fastapi import APIRouter, Depends, status, UploadFile, File
from fastapi.responses import StreamingResponse

from app.core.dependencies import DBSession
from app.ai_writing.rl.core.generator import (
    parse_uploaded_rl_template,
    generate_rl_stream,
    save_rl_content
)
from app.ai_writing.schemas.rl import (
    RLParseResponse,
    RLGenerationRequest,
    RLSaveRequest,
    RLSaveResponse
)

router = APIRouter()

@router.post(
    "/parse",
    response_model=RLParseResponse,
    status_code=status.HTTP_200_OK,
    summary="解析推荐信模板文件",
    description="上传包含推荐人信息的.docx文件，解析并返回结构化的JSON数据。"
)
async def parse_rl_template_endpoint(
    file: UploadFile = File(..., description="要解析的 .docx 格式的推荐人信息收集表格")
):
    """
    解析上传的推荐信模板文件。
    """
    parsed_data = await parse_uploaded_rl_template(file)
    return RLParseResponse(
        file_name=file.filename,
        parsed_data=parsed_data
    )

@router.post(
    "/generate",
    response_class=StreamingResponse,
    status_code=status.HTTP_200_OK,
    summary="流式生成RL/推荐信",
    description="根据客户档案和解析后的推荐人信息，流式生成专业的推荐信。"
)
async def generate_rl_stream_endpoint(
    request: RLGenerationRequest,
    db: DBSession
) -> StreamingResponse:
    """
    流式生成RL/推荐信。
    """
    rl_generator = generate_rl_stream(db, request)
    return StreamingResponse(rl_generator, media_type="text/markdown") 

@router.post(
    "/save",
    response_model=RLSaveResponse,
    status_code=status.HTTP_200_OK,
    summary="保存RL内容",
    description="将RL的Markdown内容保存到数据库"
)
async def save_rl_endpoint(
    request: RLSaveRequest,
    db: DBSession
) -> RLSaveResponse:
    """
    保存RL内容
    
    将用户编辑后的RL Markdown内容保存到数据库中。
    """
    result = await save_rl_content(db, request)
    return result 