from fastapi import APIRouter, Depends, status, HTTPException
from fastapi.responses import StreamingResponse
from typing import List, Dict, Any, AsyncGenerator

from app.core.dependencies import DBSession
from app.ai_writing.ps.core.generator import (
    generate_ps_stream, 
    get_client_profiles_summary, 
    get_client_programs_data
)
from app.ai_writing.schemas.ps import (
    PSGenerationRequest, 
    ClientProfileSummary,
    ClientProgramsData
)

router = APIRouter()

@router.post(
    "/generate",
    response_class=StreamingResponse,
    status_code=status.HTTP_200_OK,
    summary="流式生成PS/个人陈述",
    description="根据客户档案信息和申请目标流式生成专业的PS/个人陈述"
)
async def generate_ps_stream_endpoint(
    request: PSGenerationRequest,
    db: DBSession
) -> StreamingResponse:
    """
    流式生成PS/个人陈述
    
    根据客户信息和申请目标流式生成专业的个人陈述。
    """
    ps_generator = generate_ps_stream(db, request)
    return StreamingResponse(ps_generator, media_type="text/markdown")

@router.get(
    "/clients",
    response_model=List[ClientProfileSummary],
    status_code=status.HTTP_200_OK,
    summary="获取客户档案列表",
    description="获取所有客户的档案摘要信息，用于前端选择"
)
async def get_client_profiles(db: DBSession) -> List[ClientProfileSummary]:
    """
    获取客户档案列表
    
    返回所有未归档客户的基本信息和各模块数据数量，
    用于前端显示客户选择列表。
    """
    profiles = await get_client_profiles_summary(db)
    return [ClientProfileSummary(**profile) for profile in profiles]

@router.get(
    "/clients/{client_id}/programs",
    response_model=ClientProgramsData,
    status_code=status.HTTP_200_OK,
    summary="获取客户定校书数据",
    description="获取指定客户的定校书数据，用于前端显示申请目标选项"
)
async def get_client_programs(
    client_id: str,
    db: DBSession
) -> ClientProgramsData:
    """
    获取客户定校书数据
    
    根据客户ID获取该客户的定校书数据，包括申请院校、学位、专业选项，
    用于前端显示和用户选择申请目标。
    """
    programs_data = await get_client_programs_data(db, client_id)
    return ClientProgramsData(**programs_data) 