import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
from fastapi import HTT<PERSON>Exception, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload, joinedload

from app.models.client import Client, ClientProgram
from app.ai_selection.db.models import AISelectionProgram
from app.ai_writing.utils.llm import generate_ps_content_stream_async
from app.ai_writing.utils.client_data import (
    get_client_with_full_relations,
    filter_client_experiences,
    get_all_clients_with_summaries
)
from app.ai_writing.schemas.ps import (
    PSGenerationRequest, 
    PSGenerationResponse,
    ClientProfileSummary,
    ClientProgramsData,
    ClientBasicInfo,
    ProgramOption
)

logger = logging.getLogger(__name__)

async def generate_ps_stream(
    db: AsyncSession,
    request: PSGenerationRequest
) -> AsyncGenerator[str, None]:
    """
    根据客户信息和申请目标流式生成PS
    
    Args:
        db: 数据库会话
        request: PS生成请求
        
    Returns:
        生成的PS响应流
    """
    # 1. 验证客户是否存在并获取完整信息
    client = await get_client_with_full_relations(db, request.client_id)
    if not client:
        yield "Error: Client not found"
        return

    # 2. 根据用户选择筛选数据
    filtered_data = await filter_client_experiences(client, request)
    
    # 3. 构建客户数据字典
    client_data = build_client_data_dict(client, request, filtered_data)
    
    # 4. 调用LLM流式生成PS内容
    logger.info(f"开始为客户 {client.name} 流式生成PS")
    async for chunk in generate_ps_content_stream_async(
        client_data=client_data,
        request=request
    ):
        yield chunk
    
    logger.info(f"成功为客户 {client.name} 流式生成PS")

def build_client_data_dict(
    client: Client, 
    request: PSGenerationRequest,
    filtered_data: Dict[str, List[Any]]
) -> Dict[str, Any]:
    """
    将客户信息和申请目标转换为字典格式
    """
    return {
        # 客户基本信息
        'name': client.name,
        'email': client.email,
        'phone': client.phone,
        'location': client.location,
        
        # 申请目标
        'target_school': request.target_school,
        'degree': request.degree,
        'major': request.major,
        'personal_experience': request.personal_experience,
        'school_requirements': request.school_requirements,
        'word_limit': request.word_limit,
        'paragraph_setting': request.paragraph_setting,
        
        # 客户经历数据（使用筛选后的数据）
        'education': [edu.to_dict() for edu in filtered_data['education']],
        'academic': [academic.to_dict() for academic in filtered_data['academic']],
        'work': [work.to_dict() for work in filtered_data['work']],
        'activities': [activity.to_dict() for activity in filtered_data['activities']],
        'awards': [award.to_dict() for award in filtered_data['awards']],
        'skills': [skill.to_dict() for skill in filtered_data['skills']],
        'language_scores': [lang.to_dict() for lang in filtered_data['language_scores']],
        'thoughts': [thought.to_dict() for thought in client.thoughts]  # thoughts不需要筛选，全部包含
    }

async def get_client_profiles_summary(db: AsyncSession) -> List[Dict[str, Any]]:
    """
    获取所有客户的档案摘要，用于前端选择
    """
    clients = await get_all_clients_with_summaries(db)
    
    profiles = []
    for client in clients:
        profiles.append({
            'id_hashed': client.id_hashed,
            'name': client.name,
            'education_count': len(client.education),
            'academic_count': len(client.academic),
            'work_count': len(client.work),
            'activity_count': len(client.activities),
            'programs_count': len(client.client_programs)
        })
    
    return profiles

async def get_client_programs_data(
    db: AsyncSession, 
    client_id: str
) -> Dict[str, Any]:
    """
    获取客户的定校书数据，用于前端显示申请目标选项
    """
    # 1. 获取客户基本信息
    client = await get_client_with_full_relations(db, client_id)
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )
    
    # 2. 获取客户的定校书数据 (已通过get_client_with_full_relations预加载)
    client_programs = client.client_programs
    
    # 3. 整理定校书数据
    school_options = set()
    degree_options = set()
    major_options = set()
    program_combinations = []
    
    for cp in client_programs:
        program = cp.ai_selection_program
        if program:
            school_name = program.school_name_cn or program.school_name_en or "未知学校"
            degree = program.degree or "未知学位"
            program_name = program.program_name_cn or program.program_name_en or "未知专业"
            
            school_options.add(school_name)
            degree_options.add(degree)
            major_options.add(program_name)
            
            program_combinations.append({
                'school_name': school_name,
                'degree': degree,
                'program_name': program_name,
                'value': f"{school_name}|{degree}|{program_name}"
            })
    
    return {
        'client_info': {
            'id_hashed': client.id_hashed,
            'name': client.name,
            'email': client.email,
            'phone': client.phone,
            'location': client.location
        },
        'school_options': sorted(list(school_options)),
        'degree_options': sorted(list(degree_options)),
        'major_options': sorted(list(major_options)),
        'program_combinations': program_combinations
    } 