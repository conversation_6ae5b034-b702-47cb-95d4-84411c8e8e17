"""
Pydantic models for the Recommendation Letter (RL) generation module.
"""
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any

# --- Sub-models for Recommender Profile ---

class RecommenderCourseInfo(BaseModel):
    """授课老师关系模型"""
    course_name_cn: Optional[str] = Field(None, description="授课课程中文名")
    course_name_en: Optional[str] = Field(None, description="授课课程英文名")
    term: Optional[str] = Field(None, description="授课时间（学期/年份）")
    final_grade: Optional[str] = Field(None, description="期末成绩或评定情况")
    outstanding_performance: Optional[str] = Field(None, description="突出表现简述")

class RecommenderWorkInfo(BaseModel):
    """共事/共研关系模型"""
    collaboration_content: Optional[str] = Field(None, description="合作内容（项目/岗位/任务等）")
    collaboration_date: Optional[str] = Field(None, description="合作时间（起止年月）")
    student_duties: Optional[str] = Field(None, description="学生在其中的职责或表现")
    praised_ability: Optional[str] = Field(None, description="推荐人曾特别肯定的能力")

# --- Main Models for Parsing and Generation ---

class RecommenderProfile(BaseModel):
    """推荐人完整信息模型 (用于解析和生成)"""
    # 基本信息
    last_name_cn: Optional[str] = Field(None, description="推荐人姓（中）")
    first_name_cn: Optional[str] = Field(None, description="推荐人名（中）")
    last_name_en: Optional[str] = Field(None, description="推荐人姓（英）")
    first_name_en: Optional[str] = Field(None, description="推荐人名（英）")
    gender: Optional[str] = Field(None, description="性别")
    title: Optional[str] = Field(None, description="职务（头衔）")
    phone: Optional[str] = Field(None, description="联系电话")
    email: Optional[str] = Field(None, description="邮箱")
    affiliation: Optional[str] = Field(None, description="单位（具体到学院/公司及部门）")
    
    # 关系与交集
    course_info: Optional[RecommenderCourseInfo] = Field(None, description="授课老师关系")
    work_info: Optional[RecommenderWorkInfo] = Field(None, description="共事/共研关系")
    
    # 背景补充
    background_summary: Optional[str] = Field(None, description="推荐人背景补充信息")

class RLParseResponse(BaseModel):
    """文件解析响应模型"""
    status: str = Field("success", description="解析状态")
    file_name: str = Field(..., description="原始文件名")
    parsed_data: RecommenderProfile = Field(..., description="解析出的结构化推荐人数据")
    
class RLGenerationRequest(BaseModel):
    """RL 生成请求模型"""
    client_id: str = Field(..., description="客户哈希ID")
    recommender_profile: RecommenderProfile = Field(..., description="推荐人信息")
    word_limit: int = Field(0, description="目标字数：0=不限制, 1=500, 2=1000, 3=1500, 4=2000")
    additional_info: Optional[str] = Field(None, description="额外补充信息")
    
    # 经历选择 - 用户可以选择包含哪些经历
    selected_education_ids: Optional[List[int]] = Field([], description="选择的教育经历ID列表")
    selected_academic_ids: Optional[List[int]] = Field([], description="选择的学术经历ID列表")
    selected_work_ids: Optional[List[int]] = Field([], description="选择的工作经历ID列表")
    selected_activity_ids: Optional[List[int]] = Field([], description="选择的课外活动ID列表")
    selected_award_ids: Optional[List[int]] = Field([], description="选择的奖项ID列表")
    selected_skill_ids: Optional[List[int]] = Field([], description="选择的技能ID列表")
    selected_language_score_ids: Optional[List[int]] = Field([], description="选择的语言成绩ID列表") 

class RLSaveRequest(BaseModel):
    """RL保存请求模型"""
    client_id: str = Field(..., description="客户哈希ID")
    content_markdown: str = Field(..., description="RL的Markdown内容")
    version_name: str = Field(..., description="RL版本名称")
    recommender_name: str = Field(..., description="推荐人姓名")

class RLSaveResponse(BaseModel):
    """RL保存响应模型"""
    status: str = Field(default="success", description="保存状态")
    message: str = Field(..., description="保存结果消息")
    client_name: str = Field(..., description="客户姓名")
    saved_at: str = Field(..., description="保存时间") 