from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any

class PSGenerationRequest(BaseModel):
    """PS 生成请求模型"""
    client_id: str = Field(..., description="客户哈希ID")
    target_school: str = Field(..., description="申请院校")
    degree: str = Field(..., description="申请学位：bachelor、master、phd")
    major: str = Field(..., description="申请专业")
    personal_experience: str = Field(..., description="学生自身经历描述")
    school_requirements: Optional[str] = Field(None, description="院校要求及其他说明")
    word_limit: int = Field(0, description="目标字数：0=不限制, 1=500, 2=1000, 3=1500, 4=2000")
    paragraph_setting: int = Field(0, description="段落设置：0=不限制, 1=智能分段, 2=4段, 3=5段, 4=6段, 5=7段, 6=8段")
    
    # 经历选择 - 用户可以选择包含哪些经历
    selected_education_ids: Optional[List[int]] = Field([], description="选择的教育经历ID列表")
    selected_academic_ids: Optional[List[int]] = Field([], description="选择的学术经历ID列表")
    selected_work_ids: Optional[List[int]] = Field([], description="选择的工作经历ID列表")
    selected_activity_ids: Optional[List[int]] = Field([], description="选择的课外活动ID列表")
    selected_award_ids: Optional[List[int]] = Field([], description="选择的奖项ID列表")
    selected_skill_ids: Optional[List[int]] = Field([], description="选择的技能ID列表")
    selected_language_score_ids: Optional[List[int]] = Field([], description="选择的语言成绩ID列表")

class PSGenerationResponse(BaseModel):
    """PS 生成响应模型"""
    status: str = "success"
    ps_content: str = Field(..., description="生成的PS内容（HTML格式）")
    client_name: str = Field(..., description="客户姓名")
    target_school: str = Field(..., description="申请院校")
    degree: str = Field(..., description="申请学位")
    major: str = Field(..., description="申请专业")

class ClientProfileSummary(BaseModel):
    """客户档案摘要模型（用于前端选择）"""
    id_hashed: str = Field(..., description="客户哈希ID")
    name: str = Field(..., description="客户姓名")
    education_count: int = Field(0, description="教育经历数量")
    academic_count: int = Field(0, description="学术经历数量")
    work_count: int = Field(0, description="工作经历数量")
    activity_count: int = Field(0, description="课外活动数量")
    programs_count: int = Field(0, description="定校书数量")

class ClientBasicInfo(BaseModel):
    """客户基本信息模型"""
    id_hashed: str = Field(..., description="客户哈希ID")
    name: str = Field(..., description="客户姓名")
    email: Optional[str] = Field(None, description="邮箱")
    phone: Optional[str] = Field(None, description="电话")
    location: Optional[str] = Field(None, description="位置")

class ProgramOption(BaseModel):
    """定校书项目选项模型"""
    school_name: str = Field(..., description="学校名称")
    degree: str = Field(..., description="申请学位")
    program_name: str = Field(..., description="专业名称")
    value: str = Field(..., description="选项值，格式：学校|学位|专业")

class ClientProgramsData(BaseModel):
    """客户定校书数据模型"""
    client_info: ClientBasicInfo = Field(..., description="客户基本信息")
    school_options: List[str] = Field([], description="申请院校选项")
    degree_options: List[str] = Field([], description="申请学位选项")
    major_options: List[str] = Field([], description="申请专业选项")
    program_combinations: List[ProgramOption] = Field([], description="项目组合选项") 