import asyncio
import time
from typing import Any, Dict, Optional, Callable
from functools import wraps
import hashlib
import json

class SimpleCache:
    """
    简单的内存缓存实现，用于缓存数据库查询结果
    """
    
    def __init__(self, default_ttl: int = 300):  # 默认5分钟过期
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._default_ttl = default_ttl
    
    def _generate_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键"""
        key_data = {
            'func': func_name,
            'args': args,
            'kwargs': kwargs
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key in self._cache:
            cache_data = self._cache[key]
            if time.time() < cache_data['expires_at']:
                return cache_data['value']
            else:
                # 缓存过期，删除
                del self._cache[key]
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        ttl = ttl or self._default_ttl
        self._cache[key] = {
            'value': value,
            'expires_at': time.time() + ttl,
            'created_at': time.time()
        }
    
    def delete(self, key: str) -> None:
        """删除缓存值"""
        self._cache.pop(key, None)
    
    def clear(self) -> None:
        """清空所有缓存"""
        self._cache.clear()
    
    def stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        now = time.time()
        active_count = sum(1 for data in self._cache.values() if now < data['expires_at'])
        return {
            'total_keys': len(self._cache),
            'active_keys': active_count,
            'expired_keys': len(self._cache) - active_count
        }

# 全局缓存实例
cache = SimpleCache(default_ttl=300)  # 5分钟缓存

def cached(ttl: int = 300, key_prefix: str = ""):
    """
    缓存装饰器，用于缓存函数结果
    
    Args:
        ttl: 缓存时间（秒）
        key_prefix: 缓存键前缀
    """
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{cache._generate_key(func.__name__, args, kwargs)}"
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            cache.set(cache_key, result, ttl)
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{cache._generate_key(func.__name__, args, kwargs)}"
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache.set(cache_key, result, ttl)
            return result
        
        # 根据函数是否为协程选择包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

# 提供一些便捷的缓存操作函数
def clear_cache() -> None:
    """清空所有缓存"""
    cache.clear()

def get_cache_stats() -> Dict[str, Any]:
    """获取缓存统计信息"""
    return cache.stats()

def cache_ai_selection_data(ttl: int = 600):
    """
    AI选校数据专用缓存装饰器，缓存时间更长（10分钟）
    """
    return cached(ttl=ttl, key_prefix="ai_selection")

def cache_user_data(ttl: int = 300):
    """
    用户数据专用缓存装饰器（5分钟）
    """
    return cached(ttl=ttl, key_prefix="user_data") 