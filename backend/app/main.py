from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api import auth, dashboard, clients
from app.ai_selection.api.router import api_router as ai_selection_router
from app.background_extraction.api.router import api_router as background_extraction_router
from app.ai_augmentation.api.router import api_router as ai_augmentation_router
from app.ai_writing.api.router import api_router as ai_writing_router
from app.ai_detection.api.router import router as ai_detection_router
import logging
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger("api")

# 记录路由信息
logger.info("正在初始化API服务...")

# 创建 FastAPI 应用实例
app = FastAPI(
    title="囤鼠科技教育平台 API",
    description="囤鼠科技教育平台后端 API 接口",
    version="0.1.0"
)

# 配置 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "null"
    ],  # 允许的开发环境域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 记录路由注册信息
logger.info("正在注册API路由...")

# 包含各模块的路由
app.include_router(auth.router, prefix="/api")
app.include_router(dashboard.router, prefix="/api")
app.include_router(clients.router, prefix="/api")

# 包含AI选校系统路由
app.include_router(ai_selection_router, prefix="/api/ai-selection")

# 包含背景提取模块路由
app.include_router(background_extraction_router, prefix="/api/background-extraction")

# 包含AI增强模块路由
app.include_router(ai_augmentation_router, prefix="/api/ai-augmentation")

# 包含AI文书写作模块路由
app.include_router(ai_writing_router, prefix="/api/ai-writing")

# 包含AI检测模块路由
app.include_router(ai_detection_router, prefix="/api")

# 记录路由注册完成
logger.info("路由注册完成，应用启动")

# 根路由
@app.get("/")
async def root():
    """
    根路由，用于测试 API 是否正常运行
    """
    return {"message": "囤鼠科技教育平台 API 服务正在运行"}

# 测试路由
@app.get("/api/test")
async def test():
    """
    测试路由，用于测试 API 是否能正常访问
    """
    return {
        "message": "API 运行正常!",
        "status": "success"
    } 