from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any
from sqlalchemy import select, and_, distinct, func, Integer, case, cast
import json

from app.db.database import get_db
from app.core.cache import cache_ai_selection_data
from app.ai_selection.db.models import (
    AISelectionProgram as Program, 
    AISelectionCase as Case,
    AISelectionHomeSchool as HomeSchool,
    AISelectionAbroadSchool as AbroadSchool
)
from app.ai_selection.utils.language_parser import parse_language_score_safely

router = APIRouter()

@router.get("/regions")
@cache_ai_selection_data(ttl=1800)  # 缓存30分钟，地区数据较稳定
async def get_regions() -> List[Dict[str, Any]]:
    """
    获取所有地区列表（按指定顺序返回）
    
    Returns:
        地区列表
    """
    # 数据库值到显示值的映射
    region_display_map = {
        "香港": "中国香港",
        "澳门": "中国澳门",
        "新加坡": "新加坡",
        "英国": "英国", 
        "美国": "美国",
        "澳大利亚": "澳大利亚",
        "马来西亚": "马来西亚"
    }
    
    # 指定的显示顺序
    display_order = [
        "中国香港",
        "新加坡", 
        "英国",
        "美国",
        "澳大利亚",
        "中国澳门",
        "马来西亚"
    ]
    
    async for session in get_db():
        query = select(distinct(Program.school_region)).where(Program.school_region.isnot(None))
        result = await session.execute(query)
        available_regions = result.scalars().all()
        
        # 过滤掉 'NaN' 值，只保留有效地区
        valid_regions = [region for region in available_regions if region and region != 'NaN']
        
        # 将数据库值映射为显示值
        available_display_regions = []
        for region in valid_regions:
            display_name = region_display_map.get(region, region)
            available_display_regions.append(display_name)
        
        # 按指定顺序排列
        ordered_regions = []
        for display_region in display_order:
            if display_region in available_display_regions:
                ordered_regions.append(display_region)
        
        # 添加不在预定义列表中但存在的其他地区
        for display_region in sorted(available_display_regions):
            if display_region not in display_order:
                ordered_regions.append(display_region)
        
        return [{"name": region} for region in ordered_regions]

@router.get("/categories")
@cache_ai_selection_data(ttl=1800)  # 缓存30分钟，专业类别数据较稳定
async def get_program_categories() -> List[Dict[str, Any]]:
    """
    获取所有专业大类列表
    
    Returns:
        专业大类列表
    """
    async for session in get_db():
        query = select(distinct(Program.program_category)).where(Program.program_category.isnot(None))
        result = await session.execute(query)
        categories = result.scalars().all()
        
        return [{"name": category} for category in sorted(categories) if category]

@router.get("/directions")
@cache_ai_selection_data(ttl=1800)  # 缓存30分钟，专业方向数据较稳定
async def get_program_directions() -> List[Dict[str, Any]]:
    """
    获取所有专业方向列表
    
    Returns:
        专业方向列表
    """
    async for session in get_db():
        query = select(distinct(Program.program_direction)).where(Program.program_direction.isnot(None))
        result = await session.execute(query)
        directions = result.scalars().all()
        
        return [{"name": direction} for direction in sorted(directions) if direction]

@router.get("/schools")
async def get_schools(
    region: str = Query(None, description="按地区筛选"),
    qs_rank_range: str = Query(None, description="按QS排名范围筛选，格式：1-50")
) -> List[Dict[str, Any]]:
    """
    获取学校列表，支持按地区和QS排名筛选（从programs表中提取）
    
    Args:
        region: 地区名称（可选）
        qs_rank_range: QS排名范围（可选）
        
    Returns:
        学校列表
    """
    async for session in get_db():
        query = select(
            Program.school_name_cn,
            Program.school_name_en,
            Program.school_qs_name,
            Program.school_qs_rank,
            Program.school_region
        ).where(Program.school_name_cn.isnot(None)).distinct()
        
        conditions = []
        if region is not None:
            conditions.append(Program.school_region == region)
        
        if qs_rank_range is not None:
            try:
                start_rank, end_rank = map(int, qs_rank_range.split('-'))
                # 注意：school_qs_rank是字符串类型，需要转换
                conditions.append(
                    and_(
                        Program.school_qs_rank.isnot(None),
                        Program.school_qs_rank.cast(Integer) >= start_rank,
                        Program.school_qs_rank.cast(Integer) <= end_rank
                    )
                )
            except ValueError:
                raise HTTPException(status_code=400, detail="QS排名范围格式错误，应为：1-50")
        
        if conditions:
            query = query.where(and_(*conditions))
        
        result = await session.execute(query)
        rows = result.fetchall()
        
        # 去重并排序
        schools = {}
        for row in rows:
            key = row.school_name_cn
            if key not in schools:
                schools[key] = {
                    "school_name_cn": row.school_name_cn,
                    "school_name_en": row.school_name_en,
                    "school_qs_name": row.school_qs_name,
                    "school_qs_rank": row.school_qs_rank,
                    "school_region": row.school_region
                }
        
        return list(schools.values())

@router.get("/home-schools")
async def get_home_schools(
    location: str = Query(None, description="按所在地筛选"),
    school_type: str = Query(None, description="按学校类型筛选"),
    search: str = Query(None, description="按学校名称搜索"),
    limit: int = Query(2000, description="返回数量限制")
) -> List[Dict[str, Any]]:
    """
    获取境内院校列表，支持按所在地和学校类型筛选，按软科排名排序
    
    Args:
        location: 所在地（可选）
        school_type: 学校类型（可选）
        search: 学校名称搜索关键词（可选）
        limit: 返回数量限制（默认2000）
        
    Returns:
        境内院校列表，按软科排名排序
    """
    async for session in get_db():
        query = select(
            HomeSchool.id,
            HomeSchool.school_name,
            HomeSchool.school_type,
            HomeSchool.school_code,
            HomeSchool.location,
            HomeSchool.authority,
            HomeSchool.ranking_ruanke,
            HomeSchool.remarks
        )
        
        conditions = []
        if location is not None:
            conditions.append(HomeSchool.location.ilike(f"%{location}%"))
        if school_type is not None:
            conditions.append(HomeSchool.school_type == school_type)
        if search is not None:
            conditions.append(HomeSchool.school_name.ilike(f"%{search}%"))
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 按软科排名排序：有排名的在前，按排名从小到大，无排名的在后按学校名称排序
        query = query.order_by(
            HomeSchool.ranking_ruanke.is_(None).asc(),  # NULL值排在后面
            HomeSchool.ranking_ruanke.asc(),            # 排名从小到大
            HomeSchool.school_name.asc()                # 同排名或无排名按学校名称排序
        )
        
        # 添加数量限制
        query = query.limit(limit)
        
        result = await session.execute(query)
        rows = result.fetchall()
        
        return [
            {
                "id": row.id,
                "school_name": row.school_name,
                "school_type": row.school_type,
                "school_code": row.school_code,
                "location": row.location,
                "authority": row.authority,
                "ranking_ruanke": row.ranking_ruanke,
                "remarks": row.remarks
            }
            for row in rows
        ]

@router.get("/programs")
async def get_programs(
    school_name: str = Query(None, description="按学校中文名称筛选"),
    school_name_en: str = Query(None, description="按学校英文名称筛选"),
    program_name: str = Query(None, description="按专业中文名称筛选"),
    program_name_en: str = Query(None, description="按专业英文名称筛选"),
    region: str = Query(None, description="按地区筛选"),
    program_direction: str = Query(None, description="按专业方向筛选"),
    degree: str = Query(None, description="按学位类型筛选"),
    program_category: str = Query(None, description="按专业大类筛选"),
    limit: int = Query(50, description="返回数量限制"),
    offset: int = Query(0, description="跳过条数（用于分页）")
) -> List[Dict[str, Any]]:
    """
    获取专业项目列表，支持多种筛选条件
    
    Args:
        school_name: 学校中文名称（可选）
        school_name_en: 学校英文名称（可选）
        program_name: 专业中文名称（可选）
        program_name_en: 专业英文名称（可选）
        region: 地区（可选）
        program_direction: 专业方向（可选）
        degree: 学位类型（可选）
        program_category: 专业大类（可选）
        limit: 返回数量限制
        offset: 跳过条数（用于分页）
        
    Returns:
        专业项目列表
    """
    async for session in get_db():
        query = select(
            Program.id,
            Program.school_name_cn,
            Program.school_name_en,
            Program.school_qs_rank,
            Program.school_region,
            Program.program_name_cn,
            Program.program_name_en,
            Program.program_category,
            Program.program_direction,
            Program.faculty,
            Program.degree,
            Program.program_duration,
            Program.program_tuition,
            Program.gpa_requirements,
            Program.language_requirements,
            Program.application_time,
            Program.program_website
        )
        
        conditions = []
        if school_name is not None:
            conditions.append(Program.school_name_cn.ilike(f"%{school_name}%"))
        if school_name_en is not None:
            conditions.append(Program.school_name_en.ilike(f"%{school_name_en}%"))
        if program_name is not None:
            conditions.append(Program.program_name_cn.ilike(f"%{program_name}%"))
        if program_name_en is not None:
            conditions.append(Program.program_name_en.ilike(f"%{program_name_en}%"))
        if region is not None:
            conditions.append(Program.school_region == region)
        if program_direction is not None:
            conditions.append(Program.program_direction.ilike(f"%{program_direction}%"))
        if degree is not None:
            conditions.append(Program.degree == degree)
        if program_category is not None:
            conditions.append(Program.program_category == program_category)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 添加排序：先按是否有QS排名，再按排名数值，最后按学校名称
        query = query.order_by(
            # 先让有QS排名的在前面（NULL排在后面）
            Program.school_qs_rank.is_(None).asc(),
            # 对于数字型的排名，转换为整数排序
            func.length(Program.school_qs_rank).asc(),
            Program.school_qs_rank.asc(),
            # 最后按学校名称排序
            Program.school_name_cn.asc()
        )
        
        # 添加分页支持：先跳过offset条，再取limit条
        query = query.offset(offset).limit(limit)
        
        result = await session.execute(query)
        rows = result.fetchall()
        
        return [
            {
                "id": row.id,
                "school_name_cn": row.school_name_cn,
                "school_name_en": row.school_name_en,
                "school_qs_rank": row.school_qs_rank,
                "school_region": row.school_region,
                "program_name_cn": row.program_name_cn,
                "program_name_en": row.program_name_en,
                "program_category": row.program_category,
                "program_direction": row.program_direction,
                "faculty": row.faculty,
                "degree": row.degree,
                "program_duration": row.program_duration,
                "program_tuition": row.program_tuition,
                "gpa_requirements": row.gpa_requirements,
                "language_requirements": row.language_requirements,
                "application_time": row.application_time,
                "program_website": row.program_website
            }
            for row in rows
        ]

@router.get("/programs/count")
async def get_programs_count(
    school_name: str = Query(None, description="按学校中文名称筛选"),
    school_name_en: str = Query(None, description="按学校英文名称筛选"),
    program_name: str = Query(None, description="按专业中文名称筛选"),
    program_name_en: str = Query(None, description="按专业英文名称筛选"),
    region: str = Query(None, description="按地区筛选"),
    program_direction: str = Query(None, description="按专业方向筛选"),
    degree: str = Query(None, description="按学位类型筛选"),
    program_category: str = Query(None, description="按专业大类筛选")
) -> Dict[str, int]:
    """
    获取符合筛选条件的专业项目总数
    
    Args:
        school_name: 学校中文名称（可选）
        school_name_en: 学校英文名称（可选）
        program_name: 专业中文名称（可选）
        program_name_en: 专业英文名称（可选）
        region: 地区（可选）
        program_direction: 专业方向（可选）
        degree: 学位类型（可选）
        program_category: 专业大类（可选）
        
    Returns:
        总数信息
    """
    async for session in get_db():
        query = select(func.count(Program.id))
        
        conditions = []
        if school_name is not None:
            conditions.append(Program.school_name_cn.ilike(f"%{school_name}%"))
        if school_name_en is not None:
            conditions.append(Program.school_name_en.ilike(f"%{school_name_en}%"))
        if program_name is not None:
            conditions.append(Program.program_name_cn.ilike(f"%{program_name}%"))
        if program_name_en is not None:
            conditions.append(Program.program_name_en.ilike(f"%{program_name_en}%"))
        if region is not None:
            conditions.append(Program.school_region == region)
        if program_direction is not None:
            conditions.append(Program.program_direction.ilike(f"%{program_direction}%"))
        if degree is not None:
            conditions.append(Program.degree == degree)
        if program_category is not None:
            conditions.append(Program.program_category == program_category)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        result = await session.execute(query)
        count = result.scalar()
        
        return {"total": count}

@router.get("/programs/{program_id}")
async def get_program_detail(program_id: int) -> Dict[str, Any]:
    """
    获取专业项目详细信息
    
    Args:
        program_id: 专业项目ID
        
    Returns:
        专业项目详细信息
    """
    async for session in get_db():
        query = select(Program).where(Program.id == program_id)
        result = await session.execute(query)
        program = result.scalar_one_or_none()
        
        if not program:
            raise HTTPException(status_code=404, detail="专业项目不存在")
        
        return {
            "id": program.id,
            "school_name_cn": program.school_name_cn,
            "school_name_en": program.school_name_en,
            "school_qs_name": program.school_qs_name,
            "school_qs_rank": program.school_qs_rank,
            "school_region": program.school_region,
            "program_code": program.program_code,
            "degree": program.degree,
            "program_name_cn": program.program_name_cn,
            "program_name_en": program.program_name_en,
            "program_category": program.program_category,
            "program_direction": program.program_direction,
            "faculty": program.faculty,
            "enrollment_time": program.enrollment_time,
            "program_duration": program.program_duration,
            "program_tuition": program.program_tuition,
            "application_time": program.application_time,
            "application_requirements": program.application_requirements,
            "gpa_requirements": program.gpa_requirements,
            "language_requirements": program.language_requirements,
            "program_objectives": program.program_objectives,
            "courses": program.courses,
            "program_website": program.program_website,
            "other_cost": program.other_cost,
            "degree_evaluation": program.degree_evaluation
        }

@router.get("/cases")
async def get_cases(
    limit: int = Query(10, description="返回案例数量"),
    program_id: int = Query(None, description="按专业ID筛选"),
    offer_region: str = Query(None, description="按offer地区筛选"),
    undergraduate_tier: str = Query(None, description="按本科院校层级筛选"),
    undergraduate_major: str = Query(None, description="按本科专业筛选")
) -> List[Dict[str, Any]]:
    """
    获取申请案例列表，支持各种筛选条件
    
    Args:
        limit: 返回数量
        program_id: 专业ID（可选）
        offer_region: offer地区（可选）
        undergraduate_tier: 本科院校层级（可选）
        undergraduate_major: 本科专业（可选）
        
    Returns:
        案例列表
    """
    async for session in get_db():
        query = (
            select(
                Case.id,
                Case.student_name,
                Case.undergraduate_school,
                Case.undergraduate_school_tier,
                Case.undergraduate_major,
                Case.gpa,
                Case.offer_region,
                Case.offer_degree,
                Case.offer_major_direction,
                Case.key_experiences,
                Case.language_score,
                Case.offer_program_id,
                Program.program_name_cn,
                Program.school_name_cn
            )
            .join(Program, Case.offer_program_id == Program.id)
        )
        
        conditions = []
        if program_id is not None:
            conditions.append(Case.offer_program_id == program_id)
        if offer_region is not None:
            conditions.append(Case.offer_region == offer_region)
        if undergraduate_tier is not None:
            conditions.append(Case.undergraduate_school_tier == undergraduate_tier)
        if undergraduate_major is not None:
            conditions.append(Case.undergraduate_major.ilike(f"%{undergraduate_major}%"))
        
        if conditions:
            query = query.where(and_(*conditions))
        
        query = query.limit(limit)
        
        result = await session.execute(query)
        rows = result.fetchall()
        
        cases = []
        for row in rows:
            # 处理language_score的JSON数据 - 使用安全解析函数
            language_score = parse_language_score_safely(row.language_score)
            
            cases.append({
                "id": row.id,
                "student_name": row.student_name,
                "undergraduate_school": row.undergraduate_school,
                "undergraduate_school_tier": row.undergraduate_school_tier,
                "undergraduate_major": row.undergraduate_major,
                "gpa": row.gpa,
                "offer_region": row.offer_region,
                "offer_degree": row.offer_degree,
                "offer_major_direction": row.offer_major_direction,
                "key_experiences": row.key_experiences,
                "language_score": language_score,
                "offer_program_id": row.offer_program_id,
                "offer_program_name": row.program_name_cn,
                "offer_school_name": row.school_name_cn
            })
        
        return cases

@router.get("/statistics")
@cache_ai_selection_data(ttl=900)  # 缓存15分钟，统计数据更新不频繁
async def get_statistics() -> Dict[str, Any]:
    """
    获取系统统计信息
    
    Returns:
        统计信息
    """
    async for session in get_db():
        # 获取专业项目数量
        program_query = select(func.count(Program.id))
        program_result = await session.execute(program_query)
        program_count = program_result.scalar()
        
        # 暂时跳过案例数量查询（案例表不存在）
        case_count = 0
        
        # 获取学校数量（去重）
        school_query = select(func.count(distinct(Program.school_name_cn)))
        school_result = await session.execute(school_query)
        school_count = school_result.scalar()
        
        # 获取地区数量（去重）
        region_query = select(func.count(distinct(Program.school_region))).where(Program.school_region.isnot(None))
        region_result = await session.execute(region_query)
        region_count = region_result.scalar()
        
        return {
            "total_programs": program_count,
            "total_cases": case_count,
            "total_schools": school_count,
            "total_regions": region_count
        }

@router.get("/abroad-schools")
async def get_abroad_schools(
    school_name: str = Query(None, description="按学校中文名称搜索"),
    qs_rank_range: str = Query(None, description="按QS排名范围筛选，格式：1-50"),
    limit: int = Query(100, description="返回数量限制")
) -> List[Dict[str, Any]]:
    """
    获取境外院校列表，支持按学校名称搜索和QS排名筛选
    
    Args:
        school_name: 学校中文名称搜索关键词（可选）
        qs_rank_range: QS排名范围（可选）
        limit: 返回数量限制（默认100）
        
    Returns:
        境外院校列表，按QS排名排序
    """
    async for session in get_db():
        query = select(
            AbroadSchool.id,
            AbroadSchool.school_name_cn,
            AbroadSchool.school_name_en,
            AbroadSchool.school_qs_rank,
            AbroadSchool.school_website,
            AbroadSchool.school_logo_url
        )
        
        conditions = []
        if school_name is not None:
            conditions.append(AbroadSchool.school_name_cn.ilike(f"%{school_name}%"))
        
        if qs_rank_range is not None:
            try:
                start_rank, end_rank = map(int, qs_rank_range.split('-'))
                conditions.append(
                    and_(
                        AbroadSchool.school_qs_rank.isnot(None),
                        AbroadSchool.school_qs_rank >= start_rank,
                        AbroadSchool.school_qs_rank <= end_rank
                    )
                )
            except ValueError:
                raise HTTPException(status_code=400, detail="QS排名范围格式错误，应为：1-50")
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 按QS排名排序（较小的排名在前）
        query = query.order_by(AbroadSchool.school_qs_rank.asc().nulls_last()).limit(limit)
        
        result = await session.execute(query)
        rows = result.fetchall()
        
        return [
            {
                "id": row.id,
                "school_name_cn": row.school_name_cn,
                "school_name_en": row.school_name_en,
                "school_qs_rank": row.school_qs_rank,
                "school_website": row.school_website,
                "school_logo_url": row.school_logo_url
            }
            for row in rows
        ] 