from sqlalchemy import Column, Integer, String, Float, Text, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from app.db.database import Base

# 专业模型（合并了学校信息和地区信息）
class AISelectionProgram(Base):
    __tablename__ = "ai_selection_programs"
    
    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="ID，自增主键")
    
    # 学校信息
    school_name_cn = Column(String(200), nullable=False, index=True, comment="学校中文名")
    school_name_en = Column(String(200), nullable=True, index=True, comment="学校英文名")
    school_qs_name = Column(String(200), nullable=True, comment="学校QS英文名")
    school_qs_rank = Column(String(100), nullable=True, comment="学校QS排名")
    school_region = Column(String(100), nullable=True, comment="学校所在地区")

    # 专业信息
    program_code = Column(Integer, nullable=True, comment="专业代码")
    degree = Column(String(50), nullable=True, comment="申请学位类型")
    program_name_cn = Column(String(200), nullable=False, index=True, comment="专业中文名")
    program_name_en = Column(String(200), nullable=True, index=True, comment="专业英文名")
    program_category = Column(String(100), nullable=True, comment="专业大类")
    program_direction = Column(String(200), nullable=True, comment="专业方向")
    faculty = Column(String(200), nullable=True, comment="所在学院")
    
    enrollment_time = Column(String(100), nullable=True, comment="入学时间")
    program_duration = Column(String(100), nullable=True, comment="项目时长")
    program_tuition = Column(String(100), nullable=True, comment="项目学费")
    application_time = Column(Text, nullable=True, comment="申请时间")
    application_requirements = Column(Text, nullable=True, comment="申请要求")
    gpa_requirements = Column(Float, nullable=True, comment="绩点要求")
    language_requirements = Column(Text, nullable=True, comment="语言要求")

    program_objectives = Column(Text, nullable=True, comment="培养目标")
    courses = Column(Text, nullable=True, comment="课程设置")

    # 其他信息
    program_website = Column(String(500), nullable=True, comment="项目官网")
    other_cost = Column(String(100), nullable=True, comment="年开销预估值")
    degree_evaluation = Column(Text, nullable=True, comment="留服认证")
    
    # 向量嵌入（用于AI匹配）
    embedding = Column(JSONB, nullable=True, comment="专业描述的向量嵌入")
    
    # 关联到案例
    cases = relationship("AISelectionCase", back_populates="offer_program_rel")
    
    # 关联到客户选择（删除反向关系避免循环依赖）
    # client_programs = relationship("ClientProgram", back_populates="ai_selection_programs")

# 案例模型（修改字段名称）
class AISelectionCase(Base):
    __tablename__ = "ai_selection_cases"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 录取信息
    offer_school = Column(String(200), nullable=True, comment="录取学校")
    offer_program = Column(String(200), nullable=True, comment="录取项目")
    offer_program_id = Column(Integer, ForeignKey("ai_selection_programs.id"))
    offer_program_code = Column(Integer, nullable=True, comment="录取项目代码")
    offer_region = Column(String(100), nullable=True, comment="录取地区")
    offer_degree = Column(String(50), nullable=True, comment="录取学位")
    offer_major_direction = Column(String(200), nullable=True, comment="录取专业方向")
    
    # 学生基本信息
    student_name = Column(String(100), nullable=True, comment="学生姓名")
    undergraduate_school = Column(String(200), nullable=True, comment="本科学校")
    undergraduate_school_tier = Column(String(50), nullable=True, comment="本科学校层次")
    undergraduate_major = Column(String(200), nullable=True, comment="本科专业")
    gpa = Column(Float, nullable=True, comment="绩点")
    language_score = Column(String(200), nullable=True, comment="语言成绩")
    key_experiences = Column(Text, nullable=True, comment="关键经历描述")
    embedding = Column(JSONB, nullable=True, comment="案例描述的向量嵌入")
    
    # 关系字段 - 重命名避免与offer_program字段冲突
    offer_program_rel = relationship("AISelectionProgram", back_populates="cases") 

# 境内院校模型
class AISelectionHomeSchool(Base):
    """
    境内院校数据库模型类，对应 PostgreSQL 数据库中的 ai_selection_home_schools 表
    存储境内院校信息数据
    """
    __tablename__ = "ai_selection_home_schools"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="ID，自增主键")
    
    # 学校基本信息
    school_name = Column(String(200), nullable=False, index=True, comment="学校名称")
    school_type = Column(String(100), nullable=True, comment="学校类型")
    school_code = Column(String(50), nullable=True, index=True, comment="学校标识码")
    location = Column(String(100), nullable=True, comment="所在地")
    authority = Column(String(200), nullable=True, comment="主管部门")
    ranking_ruanke = Column(Integer, nullable=True, comment="软科排名")
    remarks = Column(Text, nullable=True, comment="备注")

# 境外院校模型
class AISelectionAbroadSchool(Base):
    """
    境外院校数据库模型类，对应 PostgreSQL 数据库中的 ai_selection_abroad_schools 表
    存储境外院校信息数据
    """
    __tablename__ = "ai_selection_abroad_schools"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="ID，自增主键")
    
    # 学校基本信息
    school_name_cn = Column(String(200), nullable=False, index=True, comment="学校中文名")
    school_name_en = Column(String(200), nullable=True, index=True, comment="学校英文名")
    school_qs_rank = Column(Integer, nullable=True, comment="学校QS排名")
    school_website = Column(String(500), nullable=True, comment="学校网站链接")
    school_logo_url = Column(String(500), nullable=True, comment="学校logo链接") 