from sqlalchemy import Column, Integer, String, <PERSON><PERSON>an, DateTime, ForeignKey, Text, func
from sqlalchemy.orm import relationship
from datetime import datetime, timezone, timedelta
from app.db.database import Base
import hashlib
import random
import string

# 所有客户相关模型都集中在此文件中

def get_utc_now():
    """
    获取当前UTC时间
    """
    # PostgreSQL不支持带时区的datetime对象，所以需要转换为不带时区的datetime
    # 先获取UTC时间，再手动加上8小时得到上海时间，但不包含时区信息
    return datetime.now(timezone.utc).replace(tzinfo=None) + timedelta(hours=8)

# 生成客户哈希ID
def generate_client_hash_id():
    """
    生成客户哈希ID
    使用当前时间戳、随机字符串和随机数生成唯一的哈希ID
    """
    current_time = datetime.now().strftime('%Y%m%d%H%M%S%f')
    random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
    hash_string = f"{current_time}{random_str}{random.randint(1000, 9999)}"
    return hashlib.md5(hash_string.encode()).hexdigest()[:16]

class Client(Base):
    """
    客户模型类，对应 PostgreSQL 数据库中的 clients 表
    存储客户的基本信息
    """
    __tablename__ = "clients"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="客户 ID，自增主键")
    id_hashed = Column(String(16), unique=True, index=True, nullable=False, default=generate_client_hash_id, comment="客户哈希ID，用于前端展示和API调用")
    name = Column(String(100), nullable=False, index=True, comment="客户姓名")
    gender = Column(String(20), nullable=True, comment="性别：male(男)、female(女)、unknown(未知)")
    phone = Column(String(50), nullable=True, comment="电话号码")
    email = Column(String(120), nullable=True, comment="电子邮箱")
    location = Column(String(100), nullable=True, comment="所在城市")
    address = Column(Text, nullable=True, comment="详细地址")
    is_archived = Column(Boolean, default=False, comment="是否已归档（服务完成）")

    # 证件信息
    id_card = Column(String(50), nullable=True, comment="身份证号码")
    passport = Column(String(50), nullable=True, comment="护照号码")
    id_card_issuer = Column(String(100), nullable=True, comment="身份证签发机构")
    id_card_validity = Column(String(100), nullable=True, comment="身份证有效期")
    passport_issue_place = Column(String(100), nullable=True, comment="护照签发地")
    passport_issue_date = Column(String(50), nullable=True, comment="护照签发日期")
    passport_expiry = Column(String(50), nullable=True, comment="护照过期日期")

    # 服务类型
    service_type = Column(String(50), default="undergraduate", comment="服务类型：undergraduate(本科)、master(硕士)等")

    # 关联用户ID（创建者/顾问）
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, comment="关联的用户ID（顾问）")
    user = relationship("User", back_populates="clients")

    # 关联的其他信息（一对多关系）
    education = relationship("Education", back_populates="client", cascade="all, delete-orphan")
    academic = relationship("Academic", back_populates="client", cascade="all, delete-orphan")
    work = relationship("Work", back_populates="client", cascade="all, delete-orphan")
    activities = relationship("Activity", back_populates="client", cascade="all, delete-orphan")
    awards = relationship("Award", back_populates="client", cascade="all, delete-orphan")
    skills = relationship("Skill", back_populates="client", cascade="all, delete-orphan")
    language_scores = relationship("LanguageScore", back_populates="client", cascade="all, delete-orphan")
    thoughts = relationship("Thought", back_populates="client", cascade="all, delete-orphan")
    background_modules = relationship("BackgroundCustomModule", back_populates="client", cascade="all, delete-orphan")
    thought_modules = relationship("ThoughtCustomModule", back_populates="client", cascade="all, delete-orphan")
    client_programs = relationship("ClientProgram", back_populates="client", cascade="all, delete-orphan")
    cvs = relationship("AIWritingCV", back_populates="client", cascade="all, delete-orphan")
    rls = relationship("AIWritingRL", back_populates="client", cascade="all, delete-orphan")

    # 时间相关字段
    created_at = Column(DateTime, default=get_utc_now, comment="创建时间")
    updated_at = Column(DateTime, default=get_utc_now, onupdate=get_utc_now, comment="更新时间")

    def to_dict(self) -> dict:
        """
        将客户对象转换为字典，用于 API 响应

        Returns:
            dict: 客户信息字典
        """
        return {
            'id': self.id_hashed,  # 使用哈希ID代替真实ID
            'id_hashed': self.id_hashed,  # 同时提供哈希ID字段，便于前端使用
            'name': self.name,
            'gender': self.gender,
            'phone': self.phone,
            'email': self.email,
            'location': self.location,
            'address': self.address,
            'id_card': self.id_card,
            'passport': self.passport,
            'id_card_issuer': self.id_card_issuer,
            'id_card_validity': self.id_card_validity,
            'passport_issue_place': self.passport_issue_place,
            'passport_issue_date': self.passport_issue_date,
            'passport_expiry': self.passport_expiry,
            'service_type': self.service_type,
            'user_id': self.user_id,
            'is_archived': self.is_archived,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class Education(Base):
    """
    教育经历模型类，对应 PostgreSQL 数据库中的 education 表
    存储客户的教育背景信息
    """
    __tablename__ = "education"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="教育经历 ID，自增主键")
    school = Column(String(200), nullable=False, comment="学校名称")
    major = Column(String(200), nullable=True, comment="专业")
    degree = Column(String(50), nullable=True, comment="学位：bachelor(本科)、master(硕士)、phd(博士)等")
    gpa = Column(String(20), nullable=True, comment="GPA成绩")
    start_date = Column(String(50), nullable=True, comment="开始日期")
    end_date = Column(String(50), nullable=True, comment="结束日期")
    description = Column(Text, nullable=True, comment="描述信息")

    # 关联客户ID
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False, comment="关联的客户ID")
    client = relationship("Client", back_populates="education")

    # 排序字段
    order = Column(Integer, default=0, comment="排序顺序")

    # 时间相关字段
    created_at = Column(DateTime, default=get_utc_now, comment="创建时间")
    updated_at = Column(DateTime, default=get_utc_now, onupdate=get_utc_now, comment="更新时间")

    def to_dict(self) -> dict:
        """
        将教育经历对象转换为字典，用于 API 响应

        Returns:
            dict: 教育经历信息字典
        """
        return {
            'id': self.id,
            'school': self.school,
            'major': self.major,
            'degree': self.degree,
            'gpa': self.gpa,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'description': self.description,
            'client_id': self.client_id,
            'order': self.order,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class Academic(Base):
    """
    学术经历模型类，对应 PostgreSQL 数据库中的 academic 表
    存储客户的学术经历信息，如论文发表、研究项目等
    """
    __tablename__ = "academic"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="学术经历 ID，自增主键")
    title = Column(String(200), nullable=False, comment="项目主题")
    type = Column(String(200), nullable=True, comment="项目类型：毕业设计、论文、科研项目、学科课程项目、大学生创业项目、其他等")
    date = Column(String(50), nullable=True, comment="研究日期")
    description = Column(Text, nullable=True, comment="详细描述")

    # 关联客户ID
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False, comment="关联的客户ID")
    client = relationship("Client", back_populates="academic")

    # 排序字段
    order = Column(Integer, default=0, comment="排序顺序")

    # 时间相关字段
    created_at = Column(DateTime, default=get_utc_now, comment="创建时间")
    updated_at = Column(DateTime, default=get_utc_now, onupdate=get_utc_now, comment="更新时间")

    def to_dict(self) -> dict:
        """
        将学术经历对象转换为字典，用于 API 响应

        Returns:
            dict: 学术经历信息字典
        """
        return {
            'id': self.id,
            'title': self.title,
            'type': self.type,
            'date': self.date,
            'description': self.description,
            'client_id': self.client_id,
            'order': self.order,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class Work(Base):
    """
    工作经历模型类，对应 PostgreSQL 数据库中的 work 表
    存储客户的工作/实习经历信息
    """
    __tablename__ = "work"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="工作经历 ID，自增主键")
    company = Column(String(200), nullable=False, comment="公司/单位名称")
    position = Column(String(200), nullable=True, comment="职位")
    start_date = Column(String(50), nullable=True, comment="开始日期")
    end_date = Column(String(50), nullable=True, comment="结束日期")
    description = Column(Text, nullable=True, comment="工作描述")

    # 关联客户ID
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False, comment="关联的客户ID")
    client = relationship("Client", back_populates="work")

    # 排序字段
    order = Column(Integer, default=0, comment="排序顺序")

    # 时间相关字段
    created_at = Column(DateTime, default=get_utc_now, comment="创建时间")
    updated_at = Column(DateTime, default=get_utc_now, onupdate=get_utc_now, comment="更新时间")

    def to_dict(self) -> dict:
        """
        将工作经历对象转换为字典，用于 API 响应

        Returns:
            dict: 工作经历信息字典
        """
        return {
            'id': self.id,
            'company': self.company,
            'position': self.position,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'description': self.description,
            'client_id': self.client_id,
            'order': self.order,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class Activity(Base):
    """
    课外活动模型类，对应 PostgreSQL 数据库中的 activities 表
    存储客户的课外活动信息
    """
    __tablename__ = "activities"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="活动 ID，自增主键")
    name = Column(String(200), nullable=False, comment="活动名称")
    role = Column(String(100), nullable=True, comment="角色/职位")
    start_date = Column(String(50), nullable=True, comment="开始日期")
    end_date = Column(String(50), nullable=True, comment="结束日期")
    description = Column(Text, nullable=True, comment="活动描述")

    # 关联客户ID
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False, comment="关联的客户ID")
    client = relationship("Client", back_populates="activities")

    # 排序字段
    order = Column(Integer, default=0, comment="排序顺序")

    # 时间相关字段
    created_at = Column(DateTime, default=get_utc_now, comment="创建时间")
    updated_at = Column(DateTime, default=get_utc_now, onupdate=get_utc_now, comment="更新时间")

    def to_dict(self) -> dict:
        """
        将课外活动对象转换为字典，用于 API 响应

        Returns:
            dict: 课外活动信息字典
        """
        return {
            'id': self.id,
            'name': self.name,
            'role': self.role,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'description': self.description,
            'client_id': self.client_id,
            'order': self.order,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class Award(Base):
    """
    奖项荣誉模型类，对应 PostgreSQL 数据库中的 awards 表
    存储客户获得的奖项和荣誉信息
    """
    __tablename__ = "awards"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="奖项 ID，自增主键")
    name = Column(String(200), nullable=False, comment="奖项名称")
    level = Column(String(100), nullable=True, comment="奖项级别")
    date = Column(String(50), nullable=True, comment="获奖日期")
    description = Column(Text, nullable=True, comment="奖项描述")

    # 关联客户ID
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False, comment="关联的客户ID")
    client = relationship("Client", back_populates="awards")

    # 排序字段
    order = Column(Integer, default=0, comment="排序顺序")

    # 时间相关字段
    created_at = Column(DateTime, default=get_utc_now, comment="创建时间")
    updated_at = Column(DateTime, default=get_utc_now, onupdate=get_utc_now, comment="更新时间")

    def to_dict(self) -> dict:
        """
        将奖项荣誉对象转换为字典，用于 API 响应

        Returns:
            dict: 奖项荣誉信息字典
        """
        return {
            'id': self.id,
            'name': self.name,
            'level': self.level,
            'date': self.date,
            'description': self.description,
            'client_id': self.client_id,
            'order': self.order,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class Skill(Base):
    """
    技能模型类，对应 PostgreSQL 数据库中的 skills 表
    存储客户的技能信息
    """
    __tablename__ = "skills"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="技能 ID，自增主键")
    type = Column(String(100), nullable=False, comment="技能类型，如'专业技能'、'综合技能'等")
    description = Column(Text, nullable=True, comment="技能描述，具体的技能内容")

    # 关联客户ID
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False, comment="关联的客户ID")
    client = relationship("Client", back_populates="skills")

    # 排序字段
    order = Column(Integer, default=0, comment="排序顺序")

    # 时间相关字段
    created_at = Column(DateTime, default=get_utc_now, comment="创建时间")
    updated_at = Column(DateTime, default=get_utc_now, onupdate=get_utc_now, comment="更新时间")

    def to_dict(self) -> dict:
        """
        将技能对象转换为字典，用于 API 响应

        Returns:
            dict: 技能信息字典
        """
        return {
            'id': self.id,
            'type': self.type,
            'description': self.description,
            'client_id': self.client_id,
            'order': self.order,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class LanguageScore(Base):
    """
    语言成绩模型类，对应 PostgreSQL 数据库中的 language_scores 表
    存储客户的语言考试成绩信息，如托福、雅思、GRE等
    """
    __tablename__ = "language_scores"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="语言成绩 ID，自增主键")
    type = Column(String(50), nullable=False, comment="考试类型：toefl、ielts、gre、gmat等")
    score = Column(String(50), nullable=False, comment="分数")
    date = Column(String(50), nullable=True, comment="考试日期")
    validity = Column(String(50), nullable=True, comment="有效期")

    # 关联客户ID
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False, comment="关联的客户ID")
    client = relationship("Client", back_populates="language_scores")

    # 排序字段
    order = Column(Integer, default=0, comment="排序顺序")

    # 时间相关字段
    created_at = Column(DateTime, default=get_utc_now, comment="创建时间")
    updated_at = Column(DateTime, default=get_utc_now, onupdate=get_utc_now, comment="更新时间")

    def to_dict(self) -> dict:
        """
        将语言成绩对象转换为字典，用于 API 响应

        Returns:
            dict: 语言成绩信息字典
        """
        return {
            'id': self.id,
            'type': self.type,
            'score': self.score,
            'date': self.date,
            'validity': self.validity,
            'client_id': self.client_id,
            'order': self.order,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class Thought(Base):
    """
    个人想法模型类，对应 PostgreSQL 数据库中的 thoughts 表
    存储客户的个人想法信息，如目标专业、个人解读、学术匹配等
    """
    __tablename__ = "thoughts"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="想法 ID，自增主键")
    target_major = Column(Text, nullable=True, comment="目标专业申请动机")
    personal_understanding = Column(Text, nullable=True, comment="专业个人解读")
    academic_match = Column(Text, nullable=True, comment="学术经历匹配")
    work_match = Column(Text, nullable=True, comment="工作经历匹配")
    future_plan = Column(Text, nullable=True, comment="未来规划")

    # 关联客户ID
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False, comment="关联的客户ID")
    client = relationship("Client", back_populates="thoughts")

    # 时间相关字段
    created_at = Column(DateTime, default=get_utc_now, comment="创建时间")
    updated_at = Column(DateTime, default=get_utc_now, onupdate=get_utc_now, comment="更新时间")

    def to_dict(self) -> dict:
        """
        将个人想法对象转换为字典，用于 API 响应

        Returns:
            dict: 个人想法信息字典
        """
        return {
            'id': self.id,
            'target_major': self.target_major,
            'personal_understanding': self.personal_understanding,
            'academic_match': self.academic_match,
            'work_match': self.work_match,
            'future_plan': self.future_plan,
            'client_id': self.client_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class BackgroundCustomModule(Base):
    """
    背景自定义模块模型类，对应 PostgreSQL 数据库中的 background_custom_modules 表
    存储客户的背景模块信息
    """
    __tablename__ = "background_custom_modules"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="背景模块 ID，自增主键")
    title = Column(String(100), nullable=False, comment="模块标题")
    content = Column(Text, nullable=True, comment="模块内容")
    order = Column(Integer, default=0, comment="排序顺序")

    # 关联客户ID
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False, comment="关联的客户ID")
    client = relationship("Client", back_populates="background_modules")

    # 时间相关字段
    created_at = Column(DateTime, default=get_utc_now, comment="创建时间")
    updated_at = Column(DateTime, default=get_utc_now, onupdate=get_utc_now, comment="更新时间")

    def to_dict(self) -> dict:
        """
        将背景模块对象转换为字典，用于 API 响应

        Returns:
            dict: 背景模块信息字典
        """
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'order': self.order,
            'client_id': self.client_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class ThoughtCustomModule(Base):
    """
    想法自定义模块模型类，对应 PostgreSQL 数据库中的 thought_custom_modules 表
    存储客户的想法模块信息
    """
    __tablename__ = "thought_custom_modules"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="想法模块 ID，自增主键")
    title = Column(String(100), nullable=False, comment="模块标题")
    content = Column(Text, nullable=True, comment="模块内容")
    order = Column(Integer, default=0, comment="排序顺序")

    # 关联客户ID
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False, comment="关联的客户ID")
    client = relationship("Client", back_populates="thought_modules")

    # 时间相关字段
    created_at = Column(DateTime, default=get_utc_now, comment="创建时间")
    updated_at = Column(DateTime, default=get_utc_now, onupdate=get_utc_now, comment="更新时间")

    def to_dict(self) -> dict:
        """
        将想法模块对象转换为字典，用于 API 响应

        Returns:
            dict: 想法模块信息字典
        """
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'order': self.order,
            'client_id': self.client_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class ClientProgram(Base):
    """
    客户定校书模型类，对应 PostgreSQL 数据库中的 client_programs 表
    存储客户选择的学校项目信息
    """
    __tablename__ = "client_programs"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="定校记录 ID，自增主键")

    # 关联客户ID
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False, comment="关联的客户ID")
    client = relationship("Client", back_populates="client_programs")

    # 关联项目ID
    program_id = Column(Integer, ForeignKey("ai_selection_programs.id"), nullable=False, comment="关联的项目ID")
    # 注意：这里使用字符串引用，避免循环导入
    ai_selection_program = relationship("AISelectionProgram", foreign_keys=[program_id])

    # 时间相关字段
    created_at = Column(DateTime, default=get_utc_now, comment="创建时间")

    def to_dict(self) -> dict:
        """
        将客户定校书对象转换为字典，用于 API 响应

        Returns:
            dict: 客户定校书信息字典
        """
        return {
            'id': self.id,
            'client_id': self.client_id,
            'program_id': self.program_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
        }


class AIWritingCV(Base):
    """
    AI写作-CV内容模型类，对应 PostgreSQL 数据库中的 ai_writing_cv 表
    存储客户的CV内容，支持多版本
    """
    __tablename__ = "ai_writing_cv"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="CV记录ID，自增主键")
    version_name = Column(String(200), nullable=False, default="未命名版本", comment="CV版本名称")
    target_major = Column(String(200), nullable=True, comment="目标专业")
    content_markdown = Column(Text, nullable=True, comment="CV内容的Markdown格式")

    # 关联客户ID (一对多)
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False, comment="关联的客户ID")
    client = relationship("Client", back_populates="cvs")
    
    # 时间相关字段
    created_at = Column(DateTime, default=get_utc_now, comment="创建时间")
    updated_at = Column(DateTime, default=get_utc_now, onupdate=get_utc_now, comment="更新时间")

    def to_dict(self) -> dict:
        """
        将CV对象转换为字典，用于 API 响应

        Returns:
            dict: CV信息字典
        """
        return {
            'id': self.id,
            'client_id': self.client_id,
            'version_name': self.version_name,
            'target_major': self.target_major,
            'content_markdown': self.content_markdown,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }


class AIWritingRL(Base):
    __tablename__ = 'ai_writing_rl'
    
    id = Column(Integer, primary_key=True, index=True)
    client_id = Column(Integer, ForeignKey('clients.id', ondelete='CASCADE'), nullable=False)
    
    content_markdown = Column(Text, nullable=False)
    version_name = Column(String, nullable=False)
    recommender_name = Column(String, nullable=False, comment="推荐人姓名")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    client = relationship("Client", back_populates="rls")